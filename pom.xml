<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.tellhow</groupId>
    <artifactId>powernet-graphic-app-czp-new</artifactId>
    <version>1.0</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.source.level>1.8</project.source.level>
        <project.target.level>1.8</project.target.level>
    </properties>

    <dependencies>
        <dependency>
            <groupId>AbsoluteLayout</groupId>
            <artifactId>AbsoluteLayout</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>aopalliance</groupId>
            <artifactId>aopalliance</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>axis</groupId>
            <artifactId>axis</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>batik</groupId>
            <artifactId>batik</artifactId>
            <version>1.7</version>
        </dependency>
        <dependency>
            <groupId>c3p0</groupId>
            <artifactId>c3p0</artifactId>
            <version>0.9.0</version>
        </dependency>
        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>1.6</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp</groupId>
            <artifactId>commons-lang</artifactId>
            <version>2.4</version>
        </dependency>
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>commons-dbcp</groupId>
            <artifactId>commons-dbcp</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>commons-discovery</groupId>
            <artifactId>commons-discovery</artifactId>
            <version>0.2</version>
        </dependency>
        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
            <version>1.4</version>
        </dependency>
        <dependency>
            <groupId>commons-logging</groupId>
            <artifactId>commons-logging</artifactId>
            <version>1.1.1</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>commons-pool</groupId>
            <artifactId>commons-pool</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.dm6</groupId>
            <artifactId>Dm6JdbcDriver</artifactId>
            <version>6.0</version>
        </dependency>
        <dependency>
            <groupId>com.dm7</groupId>
            <artifactId>Dm7JdbcDriver</artifactId>
            <version>7.0</version>
        </dependency>
        <dependency>
            <groupId>com.dameng</groupId>
            <artifactId>DmDialect</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>dom4j</groupId>
            <artifactId>dom4j</artifactId>
            <version>1.6.1</version>
        </dependency>
        <dependency>
            <groupId>filemanager</groupId>
            <artifactId>filemanager</artifactId>
            <version>1.0.0-czp</version>
        </dependency>
        <dependency>
            <groupId>gnu-regexp</groupId>
            <artifactId>gnu-regexp</artifactId>
            <version>1.1.4</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow</groupId>
            <artifactId>GraphPlatResource</artifactId>
            <version>2.0.0</version>
        </dependency>
        <dependency>
            <groupId>groovy</groupId>
            <artifactId>groovy</artifactId>
            <version>1.1-rc-1</version>
        </dependency>
        <dependency>
            <groupId>hibernate3</groupId>
            <artifactId>hibernate3</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>higlayout</groupId>
            <artifactId>higlayout</artifactId>
            <version>1.0a-wenzel</version>
            <classifier>wenzel</classifier>
        </dependency>
        <dependency>
            <groupId>httpclient</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.3.1</version>
        </dependency>
        <dependency>
            <groupId>httpcore</groupId>
            <artifactId>httpcore</artifactId>
            <version>4.3</version>
        </dependency>
        <dependency>
            <groupId>net.sf.jaxrpc-maven</groupId>
            <artifactId>jaxrpc-maven-plugin</artifactId>
            <version>0.3.1</version>
        </dependency>
        <dependency>
            <groupId>org.jcommon</groupId>
            <artifactId>jcommon</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp.gz</groupId>
            <artifactId>jdom</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.rover12421.opensource</groupId>
            <artifactId>JFontChooser</artifactId>
            <version>1.0.5</version>
        </dependency>
        <dependency>
            <groupId>jfree</groupId>
            <artifactId>jfreechart</artifactId>
            <version>1.0.13</version>
        </dependency>
        <dependency>
            <groupId>jfreereport</groupId>
            <artifactId>jfreereport</artifactId>
            <version>0.8.7</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-bsf</groupId>
            <artifactId>jfreereport-bsf</artifactId>
            <version>2.3.0</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-bsh</groupId>
            <artifactId>jfreereport-bsh</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-itext</groupId>
            <artifactId>jfreereport-itext</artifactId>
            <version>1.3.6</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-jcommon</groupId>
            <artifactId>jfreereport-jcommon</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-jcommon-xml</groupId>
            <artifactId>jfreereport-jcommon-xml</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-libfonts</groupId>
            <artifactId>jfreereport-libfonts</artifactId>
            <version>0.1.4</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-pixie</groupId>
            <artifactId>jfreereport-pixie</artifactId>
            <version>0.8.4</version>
        </dependency>
        <dependency>
            <groupId>jfreereport-poi</groupId>
            <artifactId>jfreereport-poi</artifactId>
            <version>3.0-alpha1</version>
            <classifier>alpha1</classifier>
        </dependency>
        <dependency>
            <groupId>com.sun.woodstock.dependlibs</groupId>
            <artifactId>jhall</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>jl1.0.1</groupId>
            <artifactId>jl1.0.1</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>js</groupId>
            <artifactId>js</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>jta</groupId>
            <artifactId>jta</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>kingbasejdbc3</groupId>
            <artifactId>kingbasejdbc3</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>log4j</groupId>
            <artifactId>log4j</artifactId>
            <version>1.2.8</version>
        </dependency>
        <dependency>
            <groupId>novaworx-syntax</groupId>
            <artifactId>novaworx-syntax</artifactId>
            <version>0.0.7</version>
        </dependency>
        <dependency>
            <groupId>ojdbc14</groupId>
            <artifactId>ojdbc14</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>oracle-jdbc</groupId>
            <artifactId>oracle-jdbc</artifactId>
            <version>10.1.0.2.0</version>
        </dependency>
        <dependency>
            <groupId>pinyin4j</groupId>
            <artifactId>pinyin4j</artifactId>
            <version>2.5.0</version>
        </dependency>
         <dependency>
             <groupId>com.tellhow.czp.basic</groupId>
             <artifactId>powernet-graphic-framework</artifactId>
             <version>2.0.0</version>
         </dependency>
         <dependency>
             <groupId>powernet-graphic-xmlgui</groupId>
             <artifactId>powernet-graphic-xmlgui</artifactId>
             <version>1.0.0</version>
         </dependency>
        <dependency>
            <groupId>RmiJDBC</groupId>
            <artifactId>RmiJDBC</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>saaj</groupId>
            <artifactId>saaj</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>spring-aop</groupId>
            <artifactId>spring-aop</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-asm</groupId>
            <artifactId>spring-asm</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-beans</groupId>
            <artifactId>spring-beans</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-context</groupId>
            <artifactId>spring-context</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-context-support</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-core</groupId>
            <artifactId>spring-core</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-expression</groupId>
            <artifactId>spring-expression</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-hibernate</groupId>
            <artifactId>spring-hibernate</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-jdbc</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-orm</groupId>
            <artifactId>spring-orm</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-tx</groupId>
            <artifactId>spring-tx</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-web</groupId>
            <artifactId>spring-web</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>spring-webmvc</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>3.1.1.RELEASE</version>
        </dependency>
        <dependency>
            <groupId>sqlite-jdbc</groupId>
            <artifactId>sqlite-jdbc</artifactId>
            <version>3.7.2</version>
        </dependency>
        <dependency>
            <groupId>swing-layout</groupId>
            <artifactId>swing-layout</artifactId>
            <version>1.0.3</version>
        </dependency>
        <dependency>
            <groupId>tbp-common-configuration</groupId>
            <artifactId>tbp-common-configuration</artifactId>
            <version>1.0.0-czp</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow</groupId>
            <artifactId>tbp-common-exception</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow</groupId>
            <artifactId>tbp-common-jdbc</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow</groupId>
            <artifactId>tbp-common-utils</artifactId>
            <version>1.0.0</version>
        </dependency>
        
        <!-- <dependency>
            <groupId>tm-extractors</groupId>
            <artifactId>tm-extractors</artifactId>
            <version>0.4</version>
        </dependency> -->
        <dependency>
            <groupId>tomcat</groupId>
            <artifactId>tomcat-apr</artifactId>
            <version>5.5.15</version>
        </dependency>
        <dependency>
            <groupId>webserviceutils</groupId>
            <artifactId>webserviceutils</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>wsdl4j</groupId>
            <artifactId>wsdl4j</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>xercesImpl</groupId>
            <artifactId>xercesImpl</artifactId>
            <version>2.6.2</version>
        </dependency>
        <dependency>
            <groupId>xml-apis</groupId>
            <artifactId>xml-apis-ext</artifactId>
            <version>1.3.04</version>
        </dependency>
        <dependency>
            <groupId>ZTable</groupId>
            <artifactId>ZTable</artifactId>
            <version>1.0</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp.gz</groupId>
            <artifactId>poi</artifactId>
            <version>3.8</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp.gz</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>3.8</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp.gz</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>3.8</version>
        </dependency>
        <dependency>
            <groupId>com.tellhow.czp.gz</groupId>
            <artifactId>poi-scratchpad</artifactId>
            <version>3.8</version>
        </dependency>
    </dependencies>
  <build>
        <finalName>powernet-graphic-app-czp-new-1.0.0</finalName>
        <pluginManagement>
        <plugins>
            <plugin>
                <!-- 指定项目编译时的java版本和编码方式 -->
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <target>1.6</target>
                    <source>1.6</source>
                    <encoding>UTF-8</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-jar-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <archive>
                        <manifest>
                            <mainClass>com.tellhow.czp.Main</mainClass> <!-- 指定入口类 -->
                            <addClasspath>true</addClasspath> <!-- 在jar的MF文件中生成classpath属性 -->
                            <classpathPrefix>czp_lib/</classpathPrefix> <!-- classpath前缀,即依赖jar包的路径 -->
                        </manifest>
                    </archive>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-dependency-plugin</artifactId>
                <version>2.8</version>
                <executions>
                    <execution>
                        <id>copy-dependencies</id>
                        <phase>package</phase>
                        <goals>
                            <goal>copy-dependencies</goal>
                        </goals>
                        <configuration>
                            <outputDirectory>${project.build.directory}/czp_lib</outputDirectory>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        </pluginManagement>
    </build>
    <distributionManagement>
        <repository>
            <id>releases</id>
            <name>User Project Release</name>
            <url>http://192.168.14.27:8081/nexus/content/repositories/releases/</url>
        </repository>
        <snapshotRepository>
            <id>snapshots</id>
            <name>User Project SNAPSHOTS</name>
            <url>http://192.168.14.27:8081/nexus/content/repositories/snapshots/</url>
        </snapshotRepository>
    </distributionManagement>
</project>
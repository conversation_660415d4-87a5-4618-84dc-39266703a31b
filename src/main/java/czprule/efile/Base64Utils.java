package czprule.efile;

import org.apache.commons.codec.binary.Base64;

public class Base64Utils {

	public static String encode(byte[] b) {
		b = Base64.encodeBase64(b);
		return new String(b);
	}
	
	public static String encode(String str) {
		return encode(str.getBytes());
	}

	public static byte[] decode(String str) {
		return Base64.decodeBase64(str.getBytes());
	}

	public static void main(String[] args) {
		String str = encode("更多更多");
		System.out.println("编码后: " + str);
		System.out.println("解码后: " + new String(decode(str)));
	}

}

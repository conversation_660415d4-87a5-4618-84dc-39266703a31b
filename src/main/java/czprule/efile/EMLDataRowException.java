package czprule.efile;

public class EMLDataRowException extends RuntimeException {
	
	/**
	 * 
	 */
	private static final long serialVersionUID = 6812658653901245491L;
	
	
	private String rowLine = null;
	private int lineIndex = -1;
	private EMLDataBlock dataBlock = null;

	public EMLDataRowException() {
		super();
	}

	public EMLDataRowException(String rowLine, int lineIndex, EMLDataBlock dataBlock, Throwable cause) {
		super(toMessage(rowLine, lineIndex, dataBlock), cause);
		this.dataBlock = dataBlock;
		this.lineIndex = lineIndex;
		this.rowLine = rowLine;
	}

	public EMLDataRowException(String rowLine, int lineIndex, EMLDataBlock dataBlock) {
		super(toMessage(rowLine, lineIndex, dataBlock));
		this.dataBlock = dataBlock;
		this.lineIndex = lineIndex;
		this.rowLine = rowLine;
	}
	
	public static String toMessage(String rowLine, int lineIndex,EMLDataBlock dataBlock) {
		String message = "\n解析数据行时出现异常，异常信息如下：";
		message += "\n所属数据块：className=" + dataBlock.getClassName() + ", entityName=" + dataBlock.getEntityName();
		message += "\n第" + lineIndex + "行：" + rowLine;
		return message;
	}

	public String getRowLine() {
		return rowLine;
	}

	public void setRowLine(String rowLine) {
		this.rowLine = rowLine;
	}

	public int getLineIndex() {
		return lineIndex;
	}

	public void setLineIndex(int lineIndex) {
		this.lineIndex = lineIndex;
	}

	public EMLDataBlock getDataBlock() {
		return dataBlock;
	}

	public void setDataBlock(EMLDataBlock dataBlock) {
		this.dataBlock = dataBlock;
	}

}

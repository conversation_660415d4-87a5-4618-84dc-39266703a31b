package czprule.efile;

import java.io.IOException;
import java.io.Writer;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.ListIterator;

import org.apache.commons.lang.StringUtils;

import czprule.efile.EMLDataRowUtils.EMLDataRowConvert;



public class EMLDataRow {
	
	private HashMap row = new HashMap();
	
	private ArrayList<Object> rowList = new ArrayList<Object>();
	
	public EMLDataBlock dataBlock = null;
	
	
	public EMLDataRow(EMLDataBlock dataBlock) {
		this.dataBlock = dataBlock;
	}

	//--------------------------------新增数据
	public void addValue(Object value) {
		if(value != null && value instanceof String) {
			String str = (String) value;
			str = str.replaceAll("&nbsp;", " ");
			value = str;
		}
		
		String headerName = dataBlock.getHeader().getName(rowList.size());
		row.put(headerName, value);
		rowList.add(value);
	}
	
	public void addValues(Object... values) {
		for(Object value : values)
			addValue(value);
	}
	
	public void addValues(Collection values) {
		Iterator iter = values.iterator();
		while(iter.hasNext())
			addValue(iter.next());
	}

	public void put(String headerName, Object value) {
		row.put(headerName, value);
		rowList.add(value);
	}
	
	//===============================获取数据
	public EMLDataBlock getDataBlock() {
		return this.dataBlock;
	}
	
	public Object get(int index) {
		return rowList.get(index);
	}
	
	public String getString(int index) {
		Object obj = rowList.get(index);
		if(obj == null) return null;
		String value = String.valueOf(obj);
		if("NULL".equals(value.toUpperCase()))
			return null;
		return value;
	}
	
	public BigDecimal getBigDecimal(int index) {
		Object obj = rowList.get(index);
		if(obj == null) return null;
		if(obj instanceof String) {
			try {
				BigDecimal value = new BigDecimal((String) obj);
				return value;
			} catch(Exception e) {
				//egnore...
			}
		}
		if(obj instanceof Number) 
			return BigDecimal.valueOf(((Number) obj).doubleValue());
		return (BigDecimal) obj;
	}
	
	public Integer getInt(int index) {
		Object obj = rowList.get(index);
		if(obj == null) return null;
		if(obj instanceof String) return Integer.parseInt((String) obj);
		else if(obj instanceof Number) return ((Number) obj).intValue();
		return (Integer) obj;
	}
	
	public Object get(String headerName) {
		return row.get(headerName);
	}
	
	public String getString(String headerName) {
		Object obj = row.get(headerName);
		if(obj == null) return null;
		return String.valueOf(obj);
	}
	
	public BigDecimal getBigDecimal(String headerName) {
		Object obj = row.get(headerName);
		if(obj == null) return null;
		if(obj instanceof String) {
			try {
				BigDecimal value = new BigDecimal((String) obj);
				return value;
			} catch(Exception e) {
				//egnore...
			}
		}
		if(obj instanceof Number) return BigDecimal.valueOf(((Number) obj).doubleValue());
		return (BigDecimal) obj;
	}
	
	public Integer getInt(String headerName) {
		Object obj = row.get(headerName);
		if(obj == null) return null;
		if(obj instanceof String) return Integer.parseInt((String) obj);
		else if(obj instanceof Number) return ((Number) obj).intValue();
		return (Integer) obj;
	}
	
	//在生成E文件的时候调用
	private String wrap(Object obj) {
		if(obj == null) {
			EMLDataRowConvert convert = EMLDataRowUtils.getConverter(null);
			if(convert != null)
				return convert.convert(null, null);
			else
				return "无";
		}
		if(obj instanceof String) {
			String value = (String) obj;
			if(StringUtils.containsAny(value, " \t".toCharArray()))
				return "'" + value + "'";
			value = value.replaceAll("\n", "&nbsp;");
			return value;
		} else if(obj instanceof byte[]) {
			String value = Base64Utils.encode((byte[])obj);
			return value;
		} else if(obj instanceof char[]) {
			return (String) obj;
		} else if(obj instanceof java.util.Date || obj instanceof java.sql.Date || obj instanceof java.sql.Timestamp) {
			EMLDataRowConvert convert = EMLDataRowUtils.getConverter(obj.getClass());
			if(convert != null)
				return convert.convert(obj.getClass(), obj);
			else
				return String.format("'%1$tY-%1$tm-%1$td %1$tH:%1$tM:%1$tS.%1$tL'", (Date) obj);
		}
		
		EMLDataRowConvert convert = EMLDataRowUtils.getConverter(obj.getClass());
		if(convert != null)
			return convert.convert(obj.getClass(), obj);
		else
			return String.valueOf(obj);
	}
	
	public void detch() {
		dataBlock.removeDataRow();
	}

	public void write(Writer writer) throws IOException {
		writer.write("#");
		ListIterator iter = rowList.listIterator();
		while(iter.hasNext()) {
			Object value = iter.next();
			writer.write(" " + wrap(value));
		}
		writer.write("\n");
	}

	@Override
	public String toString() {
		StringBuffer sb = new StringBuffer();
		ListIterator iter = rowList.listIterator();
		while(iter.hasNext()) {
			Object value = iter.next();
			sb.append(value + " ");
		}
		return sb.toString();
	}
}

package czprule.efile;

import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

public class EMLDocument {

	private LinkedHashMap<String, String> attributes = new LinkedHashMap<String, String>();
	private ArrayList<EMLDataBlock> dataBlocks = new ArrayList<EMLDataBlock>();
	private HashMap<String, EMLDataBlock> blocks = new HashMap<String, EMLDataBlock>();
	
	public EMLDataBlock getDataBlock(String className, String entityName) {
		if(StringUtils.isEmpty(entityName))
			return blocks.get(className);
		else
			return blocks.get(className + "::" + entityName);
	}
	
	public int getDataBlockCount() {
		return blocks.size();
	}
	
	public EMLDataBlock removeDataBlock(String className, String entityName) {
		Iterator<EMLDataBlock> iter = dataBlocks.iterator();
		while(iter.hasNext()) {
			EMLDataBlock dataBlock = iter.next();
			if(dataBlock.getClassName().equals(className)) {
				if(StringUtils.isEmpty(entityName) && StringUtils.isEmpty(dataBlock.getEntityName())) {
					iter.remove();
					break;
				} else if(!StringUtils.isEmpty(entityName) && entityName.equals(dataBlock.getEntityName())) {
					iter.remove();
					break;
				}
			}
		}
		if(!StringUtils.isEmpty(entityName))
			return blocks.remove(className + "::" + entityName);
		else
			return blocks.remove(className);
	}
	
	public EMLDataBlock removeDataBlock(EMLDataBlock dataBlock) {
		return removeDataBlock(dataBlock.getClassName(), dataBlock.getEntityName());
	}
	
	public void removeAll() {
		dataBlocks.clear();
		blocks.clear();
	}

	public void addAttribute(String attrName, String attrValue) {
		attributes.put(attrName, attrValue);
	}
	
	public String getAttribute(String attrName) {
		return attributes.get(attrName);
	}
	
	public EMLDataBlock addDataBlock(String className, String entityName) {
		EMLDataBlock dataBlock = new EMLDataBlock(this, className, entityName);
		if(StringUtils.isEmpty(dataBlock.getEntityName()))
			blocks.put(dataBlock.getClassName(), dataBlock);
		else
			blocks.put(dataBlock.getClassName() + "::" + dataBlock.getEntityName(), dataBlock);
		dataBlocks.add(dataBlock);
		return dataBlock;
	}
	
	public void addDataBlock(EMLDataBlock dataBlock) {
		dataBlocks.add(dataBlock);
		if(StringUtils.isEmpty(dataBlock.getEntityName()))
			blocks.put(dataBlock.getClassName(), dataBlock);
		else
			blocks.put(dataBlock.getClassName() + "::" + dataBlock.getEntityName(), dataBlock);
	}
	
	public Iterator<EMLDataBlock> iteratorDataBlocks() {
		return dataBlocks.iterator();
	}
	
	public void write(Writer writer) throws IOException {
		
		if(attributes.size() > 0) {
			writer.write("<!");
			Iterator<Map.Entry<String, String>> iter = attributes.entrySet().iterator();
			while(iter.hasNext()) {
				Map.Entry<String, String> entry = iter.next();
				writer.write(entry.getKey() + "='" + entry.getValue() + "'");
				if(iter.hasNext())
					writer.write(" ");
				else
					writer.write("!>");
			}
			writer.write("\n");
		}
		
		Iterator<EMLDataBlock> iter = dataBlocks.iterator();
		while(iter.hasNext()) {
			EMLDataBlock dataBlock = iter.next();
			dataBlock.write(writer);
			writer.write("\n");
		}
	}
	
}

package czprule.efile;

import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.Map;

import org.apache.commons.lang.StringUtils;

public class EMLDataBlock {
	
	private String className;
	
	private String entityName;
	
	private LinkedHashMap<String, String> attributes = new LinkedHashMap<String, String>();
	
	private EMLDataHeader header = new EMLDataHeader(this);
	
	private ArrayList<EMLDataRow> dataRows = new ArrayList<EMLDataRow>();
	
	private EMLDocument doc = null;
	
	public EMLDataBlock() {
		this(null, null, null);
	}
	
	public EMLDataBlock(EMLDocument doc, String className) {
		this.doc = doc;
		this.className = className;
	}
	
	public EMLDataBlock(EMLDocument doc, String className, String entityName) {
		this.doc = doc;
		this.className = className;
		this.entityName = entityName;
	}

	public void addAttribute(String name, String value) {
		attributes.put(name, value);
	}
	
	public String getAttribute(String name) {
		return attributes.get(name);
	}

	public String getClassName() {
		return className;
	}

	public void setClassName(String className) {
		this.className = className;
	}

	public String getEntityName() {
		return entityName;
	}

	public void setEntityName(String entityName) {
		this.entityName = entityName;
	}
	
	public Iterator<String> iteratorHeaders() {
		return header.getHeaderNames().iterator();
	}
	
	public Iterator<EMLDataRow> iteratorDataRows() {
		return dataRows.iterator();
	}
	
	public void addHeaders(String... headerNames) {
		for(String headerName : headerNames)
			header.add(headerName);
	}
	
	public int getDataRowCount() {
		return dataRows.size();
	}
	
	/***
	 * 获得指定的数据行，该行的定位由指定数据块头headerName的值决定
	 * 
	 * @param headerName - 列名称
	 * @param value - 列值
	 * @return
	 */
	public EMLDataRow getDataRow(String headerName, Object value) {
		Iterator<EMLDataRow> iter = dataRows.iterator();
		while(iter.hasNext()) {
			EMLDataRow dataRow = iter.next();
			if(dataRow.get(headerName).equals(value))
				return dataRow;
		}
		return null;
	}
	
	public EMLDataRow getCaseInsensitiveDataRow(String headerName, String value) {
		Iterator<EMLDataRow> iter = dataRows.iterator();
		while(iter.hasNext()) {
			EMLDataRow dataRow = iter.next();
			if(value.equalsIgnoreCase(dataRow.getString(headerName)))
				return dataRow;
		}
		return null;
	}
	
	public EMLDataRow getDataRow(int index) {
		return dataRows.get(index);
	}
	
	/***
	 * 增加一列，不含序号
	 * @return
	 */
	public EMLDataRow addDataRow() {
		EMLDataRow dataRow = new EMLDataRow(this);
		dataRows.add(dataRow);
		return dataRow;
	}
	
	/***
	 * 增加一列，不指定序号
	 * @param values
	 * @return
	 */
	public EMLDataRow addDataRow(Object... values) {
		EMLDataRow dataRow = addDataRow();
		dataRow.addValues(values);
		return dataRow;
	}
	
	public EMLDataRow addDataRow(Collection values) {
		EMLDataRow dataRow = addDataRow();
		dataRow.addValues(values);
		return dataRow;
	}
	
	/***
	 * 增加一列，指定序号
	 * @param rowNum
	 * @return
	 */
	public EMLDataRow addDataRow(int rowNum) {
		EMLDataRow dataRow = new EMLDataRow(this);
		dataRow.addValue(rowNum);
		dataRows.add(dataRow);
		return dataRow;
	}
	
	/***
	 * 增加一列，指定序号
	 * @param rowNum
	 * @return
	 */
	public EMLDataRow addDataRow(int rowNum, Object... values) {
		EMLDataRow dataRow = addDataRow(rowNum);
		dataRow.addValues(values);
		return dataRow;
	}
	
	public EMLDataRow addDataRow(int rowNum, Collection values) {
		EMLDataRow dataRow = addDataRow(rowNum);
		dataRow.addValues(values);
		return dataRow;
	}
	
	/***
	 * 增加一列，指定序号
	 * 如果没有指定表头，那么使用values的key做表头
	 * @param values
	 * @return
	 */
	public EMLDataRow addDataRow(Map values) {
		if(header.length() == 0) addHeader(values.keySet());
		return addDataRow(dataRows.size() + 1, values.values());
	} 
	
	public EMLDataHeader addHeader(Collection collection) {
		header.add(collection);
		return header;
	}
	
	public EMLDataHeader addHeader(String... args) {
		header.add(Arrays.asList(args));
		return header;
	}
	
	public EMLDataHeader getHeader() {
		return header;
	}
	
	public Iterator<EMLDataRow> rowIterator() {
		return dataRows.iterator();
	}
	
	public Iterator<String> headerIterator() {
		return header.getHeaderNames().iterator();
	}
	
	public int getBlockType() {
		return blockType;
	}

	public void setBlockType(int blockType) {
		this.blockType = blockType;
	}

	private int blockType = 0;
	
	//横表式
	public static final int CROSS_TABLE = 0;
	public static final int SINGLE_TABLE = 1;
	public static final int MULTI_TABLE = 2;
	
	public void write(Writer writer) throws IOException {
		if(StringUtils.isEmpty(entityName))
			writer.write("<" + className);
		else
			writer.write("<" + className + "::" + entityName);
		if(attributes.size() > 0) {
			Iterator<Map.Entry<String, String>> iter = attributes.entrySet().iterator();
			while(iter.hasNext()) {
				Map.Entry<String, String> entry = iter.next();
				writer.write(' ' + entry.getKey() + "='" + entry.getValue() + "'");
			}
		}
		writer.write(">\n");
		
		header.write(writer);
		
		Iterator<EMLDataRow> it = dataRows.iterator();
		while(it.hasNext()) {
			EMLDataRow dataRow = it.next();
			dataRow.write(writer);
		}
			
		if(StringUtils.isEmpty(entityName))
			writer.write("</" + className + ">");
		else
			writer.write("</" + className + "::" + entityName + ">");
		writer.write("\n");
	}
	
	public void removeDataRow() {
		if(dataRows.size() > 0)
			dataRows.remove(dataRows.size() - 1);
	}
	
	//移除一行数据
	public void removeDataRow(EMLDataRow dataRow) {
		
	}
	
	public void detach() {
		doc.removeDataBlock(this);
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result
				+ ((className == null) ? 0 : className.hashCode());
		result = prime * result
				+ ((entityName == null) ? 0 : entityName.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		EMLDataBlock other = (EMLDataBlock) obj;
		if (className == null) {
			if (other.className != null)
				return false;
		} else if (!className.equals(other.className))
			return false;
		if (entityName == null) {
			if (other.entityName != null)
				return false;
		} else if (!entityName.equals(other.entityName))
			return false;
		return true;
	}
	
}

package czprule.efile;

import java.io.IOException;
import java.io.Writer;
import java.util.ArrayList;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.ListIterator;

public class EMLDataHeader {
	
	private ArrayList<String> headerNames = new ArrayList<String>();
	
	private EMLDataBlock dataBlock;
	
	public EMLDataHeader(EMLDataBlock dataBlock) {
		this.dataBlock = dataBlock;
	}
	
	public int length() {
		return headerNames.size();
	}
	
	public void add(String str) {
		headerNames.add(str);
	}
	
	public void add(Collection<String> col) {
		headerNames.addAll(col);
	}
	
	public int getIndex(String headerName) {
		int i=0;
		ListIterator<String> iter = headerNames.listIterator();
		while(iter.hasNext()) {
			if(headerName.equals(iter.next()))
				return i;
			i++;
		}
		return -1;
	}
	
	public int getCaseInsensitiveIndex(String headerName) {
		int i=0;
		ListIterator<String> iter = headerNames.listIterator();
		while(iter.hasNext()) {
			if(headerName.equalsIgnoreCase(iter.next()))
				return i;
			i++;
		}
		return -1;
	}
	
	public String getName(int index) {
		return headerNames.get(index);
	}
	
	public List<String> getHeaderNames() {
		return headerNames;
	}
	
	public EMLDataBlock getDataBlock() {
		return dataBlock;
	}

	public void write(Writer writer) throws IOException {
		if(dataBlock.getBlockType() == EMLDataBlock.CROSS_TABLE)
			writer.write("@");
		else if(dataBlock.getBlockType() == EMLDataBlock.SINGLE_TABLE)
			writer.write("@@");
		else
			writer.write("@#");
		
		Iterator<String> iter = headerNames.iterator();
		while(iter.hasNext()) {
			String header = iter.next();
			writer.write(" " + header);
		}
		writer.write("\n");
	}
	
}

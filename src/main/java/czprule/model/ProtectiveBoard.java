/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：操作票专家系统
* 功能说明 : 电网设备基类
* 作    者 : 张俊
* 开发日期 : 2008-05-22
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/
package czprule.model;

public class ProtectiveBoard {
	private String scsId = "";      //压板ID
	private String scsscsName = ""; //压板名称
	private String deviceType = "";     //压板类型
    private String protectId = "";    //关联保护ID
    private String equipId = "";     //关联设备ID
    private String stationId = "";     //所属变电站ID
    private String boardStatus = "";     //状态

	public String getScsId() {
		return scsId;
	}
	public void setScsId(String scsId) {
		this.scsId = scsId;
	}
	public String getScsscsName() {
		return scsscsName;
	}
	public void setScsscsName(String scsscsName) {
		this.scsscsName = scsscsName;
	}
	public String getDeviceType() {
		return deviceType;
	}
	public void setDeviceType(String deviceType) {
		this.deviceType = deviceType;
	}
	public String getProtectId() {
		return protectId;
	}
	public void setProtectId(String protectId) {
		this.protectId = protectId;
	}
	public String getEquipId() {
		return equipId;
	}
	public void setEquipId(String equipId) {
		this.equipId = equipId;
	}
	public String getStationId() {
		return stationId;
	}
	public void setStationId(String stationId) {
		this.stationId = stationId;
	}
	public String getBoardStatus() {
		return boardStatus;
	}
	public void setBoardStatus(String boardStatus) {
		this.boardStatus = boardStatus;
	}
}

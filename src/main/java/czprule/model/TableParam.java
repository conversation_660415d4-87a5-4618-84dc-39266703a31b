package czprule.model;

import java.awt.Component;
import java.util.ArrayList;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年8月28日 上午10:28:28 
 */
public class TableParam {

	private String tableTitle; //标题
	private int tableWidth; //宽
	private int tableHeight; //高
	private String tableName; //数据库表名
	private String queryCondition; //查询SQL条件部分
	private ArrayList<TableField> fieldList; //字段
	private ArrayList<Component> CompList; //查询组件
	private ArrayList<String> afterSqlList = new ArrayList<String>(); //插入、更新后执行SQL
	
	
	public String getTableTitle() {
		return tableTitle;
	}
	public void setTableTitle(String tableTitle) {
		this.tableTitle = tableTitle;
	}
	
	public int getTableWidth() {
		return tableWidth;
	}
	public void setTableWidth(int tableWidth) {
		this.tableWidth = tableWidth;
	}
	public int getTableHeight() {
		return tableHeight;
	}
	public void setTableHeight(int tableHeight) {
		this.tableHeight = tableHeight;
	}
	public String getTableName() {
		return tableName;
	}
	public void setTableName(String tableName) {
		this.tableName = tableName;
	}
	
	public String getQueryCondition() {
		return queryCondition;
	}
	public void setQueryCondition(String queryCondition) {
		this.queryCondition = queryCondition;
	}
	public ArrayList<TableField> getFieldList() {
		return fieldList;
	}
	public TableField getField(String fieldID) {
		for(TableField field : fieldList) {
			if(field.getFieldID().equals(fieldID))
				return field;
		}
		return null;
	}
	public void setFieldList(ArrayList<TableField> fieldList) {
		this.fieldList = fieldList;
	}
	public ArrayList<Component> getCompList() {
		return CompList;
	}
	public void setCompList(ArrayList<Component> compList) {
		CompList = compList;
	}
	public ArrayList<String> getAfterSqlList() {
		return afterSqlList;
	}
	public void setAfterSqlList(ArrayList<String> afterSqlList) {
		this.afterSqlList = afterSqlList;
	}
	
	
}

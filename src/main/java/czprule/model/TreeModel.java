package czprule.model;

public class TreeModel {
	private String equipType = "";
	private String ruleType = "";
	private String code = "";
	private String name = "";

	public TreeModel(String code, String name, String equipType, String ruleType) {
		this.code = code;
		this.name = name;
		this.equipType = equipType;
		this.ruleType = ruleType;
	}
	
	public TreeModel(String code, String name) {
		this.code = code;
		this.name = name;
	}

	public TreeModel() {
	}

	public String getCode() {
		return code;
	}

	public void setCode(String code) {
		this.code = code;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	@Override
	public String toString() {
		return this.name;
	}

	@Override
	public boolean equals(Object obj) {
		if (obj == null) {
			return false;
		}
		if (getClass() != obj.getClass()) {
			return false;
		}
		final TreeModel other = (TreeModel) obj;
		if (this.code != other.code
				&& (this.code == null || !this.code.equals(other.code))) {
			return false;
		}
		other.setName(name);
		return true;
	}

	public TreeModel copy() {
		return new TreeModel(this.code, this.name, this.equipType,
				this.ruleType);
	}

	public String getEquipType() {
		return equipType;
	}

	public void setEquipType(String equipType) {
		this.equipType = equipType;
	}

	public String getRuleType() {
		return ruleType;
	}

	public void setRuleType(String ruleType) {
		this.ruleType = ruleType;
	}

}

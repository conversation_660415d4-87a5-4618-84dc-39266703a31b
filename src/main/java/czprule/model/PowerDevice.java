/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 电网设备基类
 * 作    者 : 张俊
 * 开发日期 : 2008-05-22
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.model;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.system.CBSystemConstants;

/**
 * 
 * <AUTHOR>
 */
public class PowerDevice extends com.tellhow.graphicframework.model.PowerDevice{
	private String deviceRunModel = ""; // 设备接线方式  
	private String openStatus = ""; // 当前元件是否处合位 0表示处合位，1表示处分位，9表示没有该状态
	private String groundLead = ""; // 接地线状态 0表示接地线 1表示不接地线
 	private String graphFileName = "";
	private String deviceKind = ""; //设备子类，如小车刀闸
	private String orgaId = "";//组织机构id
	private String permissionOrganID = ""; //许可机构
	private String supervisionright_id="";//设备监控权
	private String run_unit="";//运行单位
	private String roomID="";//配网设备所在容器设备ID
	public static final int GROUNDLINE = 1;//  1是接地线
	public static final int ACCESSCARD = 2;//  2是检修牌
	public static final int SOMEBODYIN = 3;//  3有人施工
	public static final int AVC=4;//4 AVC
	public static final int CHUANDONGPAI=5;//传动牌
	public static final int VQC=6;//VQC
	public static final int AVCBBH=7;//AVC半闭环
	public static final int GZPCDP=8;//光字牌挂传动牌
	public static final int JDBYB=9;//接地变压板
	public static final int ADDFHCL=10;//单带
	public static final int ADDCHZ=11;//重合闸
	public static final int VQCBBH=12;//VQC半闭环
	public static final int BH=0;//保护类型
	
	private String Device;//主设备 id
	private String Knife;//设备某侧刀闸 id
	private int rmType=-1;//设备类型
    private String actionWord="";//保护设备独有的属性 保护术语
    private boolean isPW = false;
    private boolean isSS = true;

	public PowerDevice() {
		
	}
	
	public String getDevice() {
		return Device;
	}

	public void setDevice(String device) {
		Device = device;
	}

	public String getKnife() {
		return Knife;
	}

	public void setKnife(String knife) {
		Knife = knife;
	}

	public int getRmType() {
		return rmType;
	}

	public void setRmType(int rmType) {
		this.rmType = rmType;
	}

	public boolean isConnected(){
		return "0".equals(this.getDeviceStatus());
	}
	public String toMoreString() {
		return super.getPowerStationName()+":"+this.getPowerDeviceName()+"("+super.getPowerDeviceID()+" "+super.getDeviceType()+")";
	}
	public String getDeviceRunModel() {
		return deviceRunModel;
	}

	public void setDeviceRunModel(String deviceRunModel) {
		this.deviceRunModel = deviceRunModel;
	}

	public String getOpenStatus() {
		if(openStatus.equals("")&&super.getDeviceType().equals(SystemConstants.Switch)){
			if(super.getDeviceStatus().equals("0")||super.getDeviceStatus().equals("1")){ //开关运行和热备用是元件是合上的
				return "0";
			}else{
				return "1";
			}
			
		}else{
			return openStatus;
		}		
	}

	public void setOpenStatus(String openStatus) {
		this.openStatus = openStatus;
	}

	public String getGraphFileName() {
		return graphFileName;
	}

	public void setGraphFileName(String graphFileName) {
		this.graphFileName = graphFileName;
	}

	@Override
	public int hashCode() {
		final int prime = 31;
		int result = 1;
		result = prime * result + ((powerDeviceID == null) ? 0 : powerDeviceID.hashCode());
		return result;
	}

	@Override
	public boolean equals(Object obj) {
		if (this == obj)
			return true;
		if (obj == null)
			return false;
		if (getClass() != obj.getClass())
			return false;
		PowerDevice other = (PowerDevice) obj;
		if (powerDeviceID == null) {
			if (other.powerDeviceID != null)
				return false;
		} else if (!powerDeviceID.equals(other.powerDeviceID))
			return false;
		return true;
	}

	public String getActionWord() {
		return actionWord;
	}

	public void setActionWord(String ac) {
		actionWord = ac;
	}

	public String getDeviceKind() {
		return deviceKind;
	}

	public void setDeviceKind(String deviceKind) {
		this.deviceKind = deviceKind;
	}
	
	public String getOrgaId() {
		return orgaId;
	}

	public void setOrgaId(String orgaId) {
		this.orgaId = orgaId;
	}

	public String getPermissionOrganID() {
		return permissionOrganID;
	}

	public void setPermissionOrganID(String permissionOrganID) {
		this.permissionOrganID = permissionOrganID;
	}
	
	public String getOrgaName() {
		if(!CBSystemConstants.getMapPowerOrgan().containsKey(orgaId))
			return "";
		else
			return CBSystemConstants.getMapPowerOrgan().get(orgaId).getPowerDeviceCode();
	}
	
	public String getPermissionOrganName() {
		if(!CBSystemConstants.getMapPowerOrgan().containsKey(permissionOrganID))
			return "";
		else
			return CBSystemConstants.getMapPowerOrgan().get(permissionOrganID).getPowerDeviceCode();
	}

	public boolean isPW() {
		return isPW;
	}

	public void setPW(boolean isPW) {
		this.isPW = isPW;
	}

	public String getSupervisionright_id() {
		return supervisionright_id;
	}

	public void setSupervisionright_id(String supervisionright_id) {
		this.supervisionright_id = supervisionright_id;
	}

	public String getRun_unit() {
		return run_unit;
	}

	public void setRun_unit(String run_unit) {
		this.run_unit = run_unit;
	}

	public String getRoomID() {
		return roomID;
	}

	public void setRoomID(String roomID) {
		this.roomID = roomID;
	}

	public boolean isSS() {
		return isSS;
	}

	public void setSS(boolean isSS) {
		this.isSS = isSS;
	}

	public String getGroundLead() {
		return groundLead;
	}

	public void setGroundLead(String groundLead) {
		this.groundLead = groundLead;
	}

    public void setStatusEffective(String is_valid) {
    }
}

package czprule.model;

import com.tellhow.graphicframework.model.Node;


public class DeviceNode extends Node{
	private int isPw ;       //是否配网设备1，是
    private String deviceType = "";         //设备类型
    private boolean connected;
    public String getDeviceStatus() {
		return deviceStatus;
	}
	public void setDeviceStatus(String deviceStatus) {
		this.deviceStatus = deviceStatus;
	}

	private String deviceStatus;
    private String deviceName;
    private String containerId;
    public String getDeviceName() {
		return deviceName;
	}
	public void setDeviceName(String deviceName) {
		this.deviceName = deviceName;
	}
	public String getContainerId() {
		return containerId;
	}
	public void setContainerId(String containerId) {
		this.containerId = containerId;
	}
    public boolean isPw(){
    	return isPw==1?true:false;
    }
    public String getDeviceType(){
    	return deviceType;
    }
    public DeviceNode(int number, String id) {
		super(number, id);
		// TODO Auto-generated constructor stub
	}
    public DeviceNode(int number, String id,int isPw,String deviceType){
    	super(number, id);
    	this.isPw = isPw;
    	this.deviceType = deviceType;
    }
    public boolean isConnected(){
    	return this.connected;
    }
    
    public DeviceNode(int number, String id,int isPw,String deviceType,boolean connected,String containerId,String deviceName,String deviceStatus ){
    	super(number, id);
    	this.isPw = isPw;
    	this.deviceType = deviceType;
    	this.connected = connected;
    	this.deviceName=deviceName;
    	this.containerId = containerId;
    	this.deviceStatus = deviceStatus;
    } 
}

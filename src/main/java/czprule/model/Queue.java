package czprule.model;

import java.util.Iterator;

/**
 * 嗯，这是一个队列。“缪东欣提醒：有基于迭代的宽度优先算法，也有基于队列的。”嘿嘿基于迭代的，会有栈溢出的风险，嘿嘿老版本搜索
 * 结构好像就是基于迭代的，所以太深的，它搜不了。咋们不一样，咋们是基于队列的，再深咋们也能搜。嘿嘿
 * <AUTHOR>
 * 入框架时间：2023年4月18日
 * @param <Item>
 */

public class Queue<Item> implements Iterable<Item> {
	private Node first;
	private Node last;
	private int N;
	private class Node{
		Item item;
		Node next;
	}
	public boolean isEmpty(){
		return first == null;
	}
	public int size(){
		return N;
	}
	
	public void enqueue(Item item){
		Node oldlast = last;
		last = new Node();
		last.item = item;
		last.next = null;
		if(isEmpty()) first = last;
		else oldlast.next = last;
		N++;
	}
	public Item dequeue(){
		Item item = first.item;
		first = first.next;
		if(isEmpty()) last = null;
		N--;
		return item;
	}
	@Override
	public Iterator<Item> iterator() {
		// TODO Auto-generated method stub
		return new ListIterator();
	}
	private class ListIterator implements Iterator<Item>{
		private Node current = first;

		@Override
		public boolean hasNext() {
			// TODO Auto-generated method stub
			return current != null;
		}
		public Item next(){
			Item item = current.item;
			current = current.next;
			return item;
		}

		@Override
		public void remove() {
			
		} 
	}
}

package czprule.model;

public class CodeNameModel {
	 private String code="";
	 private String name="";
	public CodeNameModel(String code,String name){
		this.code=code;
		this.name=name;
	}
	public CodeNameModel(){
	}
	public String getCode() {
		return code;
	}
	public void setCode(String code) {
		this.code = code;
	}
	public String getName() {
		return name;
	}
	public void setName(String name) {
		this.name = name;
	}
	 
	@Override
	public String toString(){
		return this.name;
	}
	
	@Override
    public boolean equals(Object obj) {
        if (obj == null) {
            return false;
        }
        if (getClass() != obj.getClass()) {
            return false;
        }
        final CodeNameModel other = (CodeNameModel) obj;
        if (this.code != other.code && (this.code == null || !this.code.equals(other.code))) {
            return false;
        }
        other.setName(name);
        return true;
    }
		 
	public CodeNameModel copy(){
		return new CodeNameModel(this.code,this.name);
	}

}

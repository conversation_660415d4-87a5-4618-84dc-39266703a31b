package czprule.model;

import java.util.ArrayList;
import java.util.HashMap;

public class PowerDeviceListAndMapPaths {
	ArrayList<PowerDevice> pdList ;
	HashMap<PowerDevice, Iterable<PowerDevice>> pdMapsPaths;
	
	public ArrayList<PowerDevice> getPowerDeviceList() {
		return pdList;
	}
	public void addPowerDevice(PowerDevice pd) {
		this.pdList.add(pd);
	}
	public HashMap<PowerDevice, Iterable<PowerDevice>>  getMapPath() {
		return pdMapsPaths;
	}
	public  Iterable<PowerDevice>  getPdPath(PowerDevice pd) {
		return pdMapsPaths.get(pd);
	}
	public void addPaths(PowerDevice pd,Iterable<PowerDevice> path) {
		this.pdMapsPaths.put(pd, path);
	}
	public PowerDeviceListAndMapPaths(){
		pdList = new ArrayList<PowerDevice>();
		pdMapsPaths = new HashMap<PowerDevice, Iterable<PowerDevice>>();
	}

	@Override
	public String toString() {
		return "PowerDeviceListAndMapPaths{" +
				"pdList=" + pdList.toString() +
				", pdMapsPaths=" + pdMapsPaths.toString() +
				'}';
	}
}

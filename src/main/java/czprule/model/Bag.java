package czprule.model;

import java.util.Iterator;
/**
 * 
 * <AUTHOR>
 * 嘿嘿，我也在项目框架里留下我得名字了
 * 这就是一个可迭代的链表结构，就以容器。
 * 入框架时间：2023年4月18日
 * @param <Item>
 */
public class Bag<Item> implements Iterable<Item> {
	private boolean break_flag; 
	
	private Node first;
	
	private int size;
	
	private class Node{
		Item item;
		Node next;
	}
	public void add (Item item){
		Node oldfirst = first;
		first = new Node();
		first.item = item;
		first.next = oldfirst;
		size++;
	}
	public int size(){
		return size;
	}
	
	public void breakBag(){
		this.break_flag = true;
	}
	
	@Override
	public Iterator<Item> iterator() {
		// TODO Auto-generated method stub
		return new ListIterator();
	}
	private class ListIterator implements Iterator<Item>{
		private Node current = first;

		@Override
		public boolean hasNext() {
			// TODO Auto-generated method stub
			return current != null;
		}
		public Item next(){
			Item item = current.item;
			current = current.next;
			return item;
		}

		@Override
		public void remove() {
			
		} 
	}
	
}

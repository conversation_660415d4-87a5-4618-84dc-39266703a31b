package czprule.model;

import java.util.HashMap;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年7月25日 下午3:41:44 
 */
public class TableField {

	private String fieldID = "";
	private String fieldName = "";
	private String fieldType = "";
	private String fieldFillSQL = "";
	private boolean isPrimaryKey = false;
	private boolean isVisible = true;
	private boolean isCheck = false;
	private String fieldValue;
	private HashMap codeMap;
	
	public TableField(String fieldID, String fieldName, String fieldFillSQL, boolean isPrimaryKey) {
		super();
		this.fieldID = fieldID;
		this.fieldName = fieldName;
		this.fieldFillSQL = fieldFillSQL;
		this.isPrimaryKey = isPrimaryKey;
	}
	
	public TableField(String fieldID, boolean isVisible, String fieldValue) {
		super();
		this.fieldID = fieldID;
		this.isVisible = isVisible;
		this.fieldValue = fieldValue;
	}
	
	public TableField(String fieldName, boolean isCheck) {
		super();
		this.fieldName = fieldName;
		this.isCheck = isCheck;
	}
	
	public String getFieldID() {
		return fieldID;
	}
	public void setFieldID(String fieldID) {
		this.fieldID = fieldID;
	}
	public String getFieldName() {
		return fieldName;
	}
	public void setFieldName(String fieldName) {
		this.fieldName = fieldName;
	}
	public String getFieldType() {
		return fieldType;
	}
	public void setFieldType(String fieldType) {
		this.fieldType = fieldType;
	}
	public String getFieldFillSQL() {
		return fieldFillSQL;
	}
	public void setFieldFillSQL(String fieldFillSQL) {
		this.fieldFillSQL = fieldFillSQL;
	}
	public boolean isPrimaryKey() {
		return isPrimaryKey;
	}
	public void setPrimaryKey(boolean isPrimaryKey) {
		this.isPrimaryKey = isPrimaryKey;
	}
	public HashMap getCodeMap() {
		return codeMap;
	}
	public void setCodeMap(HashMap codeMap) {
		this.codeMap = codeMap;
	}

	public boolean isVisible() {
		return isVisible;
	}

	public void setVisible(boolean isVisible) {
		this.isVisible = isVisible;
	}

	public String getFieldValue() {
		return fieldValue;
	}

	public void setFieldValue(String fieldValue) {
		this.fieldValue = fieldValue;
	}

	public boolean isCheck() {
		return isCheck;
	}

	public void setCheck(boolean isCheck) {
		this.isCheck = isCheck;
	}
	
}

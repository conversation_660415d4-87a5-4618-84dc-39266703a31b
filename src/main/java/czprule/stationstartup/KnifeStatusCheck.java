/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 同步数据后刀闸状态校核
 * 作    者 : zhangyp
 * 开发日期 : 2010-09-09
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.dao.DevicePropertyDB;
import czprule.algorithm.devicerunmode.JudgeSwitchRunModeDouble;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/**
 * 用途：只能在变电站启动后才可以调用执行
 * <AUTHOR>
 *
 */
public class KnifeStatusCheck implements StationStartup {



	public void StationLoad(String stationID) {
		// TODO 自动生成方法存根
		Map<String,String> DevChangeStatus=CBSystemConstants.DevcieStatusCache.get(stationID);
		if(DevChangeStatus==null)
			return;
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inPara=new HashMap<String,Object>();
		Map<String,Object> outPara=new HashMap<String,Object>();
		String equipID="";
		String knifeStatus="";
		PowerDevice devKnife; //刀闸或者接地刀闸
		PowerDevice devSwitch; //刀闸直接连接的开关
		String SwitchStatus;
		List<String> removeKnifes=new ArrayList<String>();
		for (Iterator iter = DevChangeStatus.keySet().iterator(); iter.hasNext();) {
			 equipID= iter.next().toString();
			 knifeStatus=DevChangeStatus.get(equipID);
			 devKnife=(PowerDevice)CBSystemConstants.getPowerDevice(stationID,equipID);
			 
			 //  1、 刀闸
			 if(devKnife!=null&&devKnife.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				 	//刀闸
				    //先找直连的开关，如果找不到再找非直连的开关
					inPara.put("oprSrcDevice", devKnife);
					inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
					inPara.put("isSearchDirectDevice", true);
					cs.execute(inPara, outPara);
					inPara.clear();
					List Switchs = (ArrayList) outPara.get("linkedDeviceList");
					devSwitch=(Switchs!=null&&Switchs.size()>0)?(PowerDevice)Switchs.get(0):null;
					if(devSwitch==null)
						continue;
					
					//如果刀闸连接的开关处于缓存中，优先考虑缓存中的开关
					String switchID=devSwitch.getPowerDeviceID();
					SwitchStatus=DevChangeStatus.get(switchID)!=null?DevChangeStatus.get(switchID):DevicePropertyDB.getDeviceEMSStatus(switchID);
					
					if(SwitchStatus.equals("0")&&knifeStatus.equals("1")){
							//开关处于运行，这时刀闸一定处于合位 这时要排除双母开关的情况
						    JudgeSwitchRunModeDouble jsmdlMode=new JudgeSwitchRunModeDouble();
						    inPara.put("oprSrcDevice", devSwitch);
						    String check=jsmdlMode.doJudge(devSwitch);
						    if(check.equals("0"))
						    	continue;
						    removeKnifes.add(equipID);
						    DevChangeStatus.put(equipID, "0");
						    DevicePropertyDB.updateDeviceEMSStatus(equipID, "0"); //更新遥信缓冲表
							continue;
					}	
			 }
			 
             // 2、 接地刀闸
			 if(devKnife!=null&&devKnife.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
				 	//接地刀闸
					
					inPara.put("oprSrcDevice", devKnife);
					inPara.put("tagDevType", SystemConstants.SwitchSeparate);// 目标设备为刀闸
					inPara.put("isSearchDirectDevice", true);
					cs.execute(inPara, outPara);
					List knifes = (ArrayList) outPara.get("linkedDeviceList");
					devSwitch=knifes.size()>0?(PowerDevice)knifes.get(0):null;
					if(devSwitch==null)
							continue;
					
					//如果接地刀闸连接的刀闸处于缓存中，优先考虑缓存中的刀闸
					String switchID=devSwitch.getPowerDeviceID();
					SwitchStatus=DevChangeStatus.get(switchID)!=null?DevChangeStatus.get(switchID):DevicePropertyDB.getDeviceEMSStatus(switchID);
					
					if(SwitchStatus.equals("0")&&knifeStatus.equals("0")){
							//刀闸处于合位，这时接地刀闸一定处于分位
						    removeKnifes.add(equipID);
						    DevChangeStatus.put(equipID, "1");
						    DevicePropertyDB.updateDeviceEMSStatus(equipID, "1"); //更新遥信缓冲表
							continue;
					}
					
			 }
			
		}
		
		//移除经过状态校核的设备
//		for(int i=0;i<removeKnifes.size();i++){
//			DevChangeStatus.remove(removeKnifes.get(i)); 
//		}
		
	}


}

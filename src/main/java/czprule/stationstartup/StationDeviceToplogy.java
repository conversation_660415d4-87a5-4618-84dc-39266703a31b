package czprule.stationstartup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.common.spring.BeanFactory;
import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.GraphDataService;
import czprule.system.PowerSystemDBOperator;

public class StationDeviceToplogy implements StationStartup {

    public void StationLoad(String stationID) {
        StationLoad(stationID,true);
    }

	public void StationLoad(String stationID,boolean isLoadBySql) {

        if(isLoadBySql){
            //厂站设备拓扑
            loadStationPowerDevices(stationID);
            //厂站连接方式拓扑关系
            loadConnectCache(stationID);
            //接地线加入缓存中
            loadRMDeviceCache(stationID);
            //保护设备
            loadProtectCache(stationID);
        }else{
            //厂站设备拓扑,通过dubbo
            loadStationPowerDevicesDubbo(stationID);
        }
        if(CBSystemConstants.cardstatus.equals("1")) {
            InitDeviceStatus ids = new InitDeviceStatus();
            ids.initStatus_EMSToCache(stationID);
        }

    	if(CBSystemConstants.jh_tai == 1 ){
    		if(CBSystemConstants.roleCode.equals("1")&&CBSystemConstants.usePwRole) { //配网要由馈线ID找厂站ID，加载厂站设备
    			stationID = stationID.replace(",", "','");
    			List<Map<String,String>> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id in('"+stationID+"')");
                if(list.size() > 0 && list.get(0).get("st_id")!=null) {
                	StationStartupManager.startup(list.get(0).get("st_id").toString());
                }
    		}
    	}
	}



    private void loadLineFlowInfo(String stationID) {
		String sql="SELECT T.* FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T,"+CBSystemConstants.opcardUser+"T_E_SUBSTATIONTOPOLOGY S WHERE T.EQUIP_ID=S.EQUIP_ID AND S.STATION_ID=?";
		List<Map> list=DBManager.queryForList(sql, stationID);
		for (Map map : list) {
			String equipid=StringUtils.ObjToString(map.get("EQUIP_ID"));
			String flowinfo=StringUtils.ObjToString(map.get("FLOWINFO"));
			CBSystemConstants.getLineFlowInfo().put(equipid, flowinfo);
		}
	}
	
	/**
	 * 根据母联开关状态及自投常方式变更自投投退状态
	 * */
	private void changeBZT(PowerDevice mlkg) {
		String state = mlkg.getDeviceStatus();
		if(state.equals("1")){//热备用时候自投有效
			String sql = "update "+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t set t.CHANGE_STATE='0' where "
					+"t.DEVICE_ID in ( select t.OBJ_ID from "+CBSystemConstants.opcardUser+"T_A_STATENORMAL t where t.OBJ_ID  in (select RELATION_ID from "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE  where SCS_OBJ= ? and DEVICE_TYPE=1) and t.OBJ_STATE= '0')";
			DBManager.update(sql, mlkg.getPowerDeviceID());

		}else{//其他状态自投无效
			String sql = "update "+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD t set t.CHANGE_STATE='1' where "
					+"t.DEVICE_ID in (select RELATION_ID from "+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE  where SCS_OBJ=? and DEVICE_TYPE=1)";
			DBManager.update(sql, mlkg.getPowerDeviceID());
			

		}
	}
	/**
	 * 厂站设备拓扑
	 * */
	public static void loadStationPowerDevices(String stationID){
		
		List<PowerDevice> powerDeviceList = new ArrayList<PowerDevice>();
		List devicelist = PowerSystemDBOperator.getStationDeviceList(stationID);
		String cb=CBSystemConstants.cardstatus;
		for(Iterator iter = devicelist.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String powerDeviceID = StringUtils.ObjToString(map.get("EQUIP_ID"));
			String powerDeviceDefaultSta = StringUtils.ObjToString(map.get("OBJ_STATE"));//黄翔修改
			String powerDeviceCode = StringUtils.ObjToString(map.get("EQUIP_CODE"));
			String powerDeviceName = StringUtils.ObjToString(map.get("EQUIP_NAME"));
			String powerStationID = StringUtils.ObjToString(map.get("STATION_ID"));
//			String powerStationCode = StringUtils.ObjToString(map.get("STATION_CODE"));
			String powerStationName = StringUtils.ObjToString(map.get("STATION_NAME"));
			String cimID =  StringUtils.ObjToString(map.get("CIM_ID"));
			double powerVoltGrade = 0;
			String volt = StringUtils.ObjToString(map.get("VOLTAGE_CODE"));
			String orgaId = StringUtils.ObjToString(map.get("ORGA_ID"));
			String permissionOrganID = StringUtils.ObjToString(map.get("DISPATCH_PERMISSION_ID"));
			String reg = "^[0-9]+(.[0-9]+)?$";
			if(volt.matches(reg))
				powerVoltGrade = Double.valueOf(volt);
			else
				powerVoltGrade = 0;
			String deviceType = StringUtils.ObjToString(map.get("EQUIPTYPE_FLAG"));
			String deviceruntype = StringUtils.ObjToString(map.get("DEVICERUNTYPE"));
			String devicerunmodel = StringUtils.ObjToString(map.get("DEVICERUNMODEL"));
			String isPW = StringUtils.ObjToString(map.get("ISPW")); //是否配网设备
			String LOADELECSTATUS = StringUtils.ObjToString(map.get("LOADELECSTATUS")); //设备是否失电
			String supervisionright_id=StringUtils.ObjToString(map.get("SUPERVISIONRIGHT_ID"));
			String roomID = StringUtils.ObjToString(map.get("ROOM_ID"));
			String devicestatus="";
			if(cb.equals("0")){
				devicestatus = StringUtils.ObjToString(map.get("DEVICESTATUS"));
			}else if(cb.equals("1")){
				devicestatus = StringUtils.ObjToString(map.get("DISPATCH"));
			}else{
				devicestatus = StringUtils.ObjToString(map.get("MONITORING"));
			}
//			PowerDevice temp = CBSystemConstants.getPowerDevice(powerStationID, powerDeviceID);
//			if(temp != null) {
//				temp.setDeviceStatus(devicestatus);
//				continue;
//			}
			PowerDevice pd = new PowerDevice();
			pd.setPowerDeviceID(powerDeviceID);
			pd.setSupervisionright_id(supervisionright_id);
			pd.setPowerDeviceDefaultSta(powerDeviceDefaultSta);//黄翔修改
			pd.setPowerDeviceCode(powerDeviceCode);
			pd.setPowerDeviceName(powerDeviceName);
			pd.setDeviceType(deviceType);
			pd.setPowerStationID(powerStationID);
//			pd.setPowerStationCode(powerStationCode);
			PowerDevice st = CBSystemConstants.getPowerStation(powerStationID);
			if(st != null)
				pd.setPowerStationName(CZPService.getService().getDevNameInit(st));
			pd.setPowerVoltGrade(powerVoltGrade);
			pd.setCimID(cimID);
			pd.setDeviceRunType(deviceruntype);
			pd.setDeviceSetType(deviceruntype);
			pd.setDeviceRunModel(devicerunmodel);
			
			if(devicestatus.equals("-1")){
				if(deviceruntype.equals(CBSystemConstants.RunTypeGroundKnife)){
					devicestatus = "1";
				}else{
					devicestatus = "0";
				}
			}
			
			pd.setDeviceStatus(devicestatus);
			pd.setPowerDeviceName(CZPService.getService().getDevNameInit(pd));//处理设备名称
			pd.setOrgaId(orgaId);
			pd.setPermissionOrganID(permissionOrganID);
			if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate) && pd.getPowerDeviceName().indexOf("互")<0 &&
					(pd.getPowerDeviceName().indexOf("小车")>=0||pd.getPowerDeviceName().indexOf("手车")>=0
					||pd.getPowerDeviceName().indexOf("触头")>=0))
				pd.setDeviceKind(CBSystemConstants.KindKnifeXC);
			pd.setPW(isPW.equals("1"));
			pd.setIsLoseElec(LOADELECSTATUS);
			pd.setRoomID(roomID);
			
			if(!CBSystemConstants.isGroundDisconnectorExist &&
					deviceType.equals(SystemConstants.SwitchFlowGroundLine))
				CBSystemConstants.isGroundDisconnectorExist = true;
			if(deviceruntype.equals(CBSystemConstants.RunTypeSwitchMLPL)){
				powerDeviceList.add( pd);
			}
			HashMap<String,PowerDevice> powerDeviceMap = new HashMap<String,PowerDevice>();
			if(!CBSystemConstants.getMapPowerStationDevice().containsKey(powerStationID))
				CBSystemConstants.putMapPowerStationDevices(powerStationID, new HashMap<String,PowerDevice>());
			CBSystemConstants.getMapPowerStationDevice().get(powerStationID).put(powerDeviceID, pd);
		}
		

	}


	
	/**
	 * 将可拆卸设备加入缓存  如接地线 检修牌
	 * */
	public static void loadRMDeviceCache(String stationID){
		ArrayList<PowerDevice> rms =new ArrayList<PowerDevice>();
		List queryList = PowerSystemDBOperator.getStationRMDevice(stationID);
		for(Iterator iter = queryList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String RMDeviceID = StringUtils.ObjToString(map.get("REMOVEID"));
			String equipID = StringUtils.ObjToString(map.get("EQUIPID"));
			String terminalID = StringUtils.ObjToString(map.get("TERMINALID"));
			String RMStatus = StringUtils.ObjToString(map.get("ROMVESTATUS"));
			int rmType=Integer.parseInt(StringUtils.ObjToString(map.get("REMOVETYPE")));
			PowerDevice rmd = new PowerDevice();
			rmd.setPowerDeviceID(RMDeviceID);
			rmd.setDevice(equipID);
			rmd.setKnife(terminalID);
			rmd.setDeviceStatus(RMStatus);
			rmd.setRmType(rmType);//拆装类型
			rmd.setPowerStationID(stationID);
			rmd.setDeviceType(SystemConstants.RemovableDevice);
			PowerDevice equip = CBSystemConstants.getPowerDevice(stationID, equipID);
			if(equip != null)
				rmd.setOrgaId(equip.getOrgaId());
			rms.add(rmd);
		}
		CBSystemConstants.getRMDevice().put(stationID, rms);
	}
	public static void loadProtectCache(String stationID) {
		HashMap<String,PowerDevice> protectMap = new HashMap<String,PowerDevice>();
    	List protectlist = PowerSystemDBOperator.getStationProtectList(stationID);
    	for(Iterator iter = protectlist.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String protectID = StringUtils.ObjToString(map.get("PROTECTID"));
			String deviceID=StringUtils.ObjToString(map.get("EQUIPID"));
			String protectName = map.get("PROTECTNAME")==null?StringUtils.ObjToString(map.get("PROTECTTYPENAME")):StringUtils.ObjToString(map.get("PROTECTNAME"));
			String protectTypeName = StringUtils.ObjToString(map.get("PROTECTTYPENAME"));
			String protectKind = StringUtils.ObjToString(map.get("PROTECTKIND"));
			String protectStatus = map.get("PROTECTSTATUS")==null?"0":StringUtils.ObjToString(map.get("PROTECTSTATUS"));
			String devicetypeid = map.get("DEVICETYPEID")==null?"":StringUtils.ObjToString(map.get("DEVICETYPEID"));
			PowerDevice pd = new PowerDevice();
			pd.setDevice(deviceID);
			pd.setPowerDeviceID(protectID);
			pd.setPowerDeviceName(protectName);
			pd.setDeviceType(protectTypeName);
			pd.setRmType(0);
			pd.setDeviceStatus(protectStatus);
			pd.setPowerStationID(stationID);
		//	pd.setDeviceRunType(deviceRunType);
			protectMap.put(protectID, pd);
    	}
    	CBSystemConstants.putMapPowerStationProtects(stationID, protectMap);

		
	}

    private void loadStationPowerDevicesDubbo(String stationID) {
        GraphDataService databean = (GraphDataService) BeanFactory.getBean("GraphDataService");

        HashMap<String, HashMap<String, PowerDevice>> mapPowerStationDevice = databean.queryLineOrStationDevices(stationID);
        Iterator<String> iterator = mapPowerStationDevice.keySet().iterator();
        while(iterator.hasNext()){
            String powerStationID = iterator.next();
            CBSystemConstants.putMapPowerStationDevices(powerStationID, mapPowerStationDevice.get(powerStationID));
        }

        HashMap<String,HashMap<String,ArrayList<String>>> devicePointMap = databean.queryToplogyDevicePoint(stationID);
        Iterator<String> iterator2 = mapPowerStationDevice.keySet().iterator();
        while(iterator2.hasNext()){
            String powerStationID = iterator2.next();
            SystemConstants.putMapToplogyDevicePoint(powerStationID, devicePointMap.get(powerStationID));
        }

        HashMap<String,HashMap<String,ArrayList<String>>> pointDeviceMap = databean.queryToplogyPointDevice(stationID);
        Iterator<String> iterator3 = mapPowerStationDevice.keySet().iterator();
        while(iterator3.hasNext()){
            String powerStationID = iterator3.next();
            SystemConstants.putMapToplogyPointDevice(powerStationID, pointDeviceMap.get(powerStationID));
        }
    }

	public static void loadConnectCache(String stationID){
		
		ArrayList<String> sIDList = new ArrayList<String>();
    	ArrayList<String> motherlinePointList = new ArrayList<String>();
    	List list = PowerSystemDBOperator.getStationToplogy(stationID);
		for(Iterator iter = list.iterator();iter.hasNext();) {
	
			Map map = (Map) iter.next();
			String connectPointID = StringUtils.ObjToString(map.get("CONNECTIVITYNODE_ID"));
			String powerEquipID = StringUtils.ObjToString(map.get("EQUIP_ID"));
			String sID = StringUtils.ObjToString(map.get("STATION_ID"));
			if(!sIDList.contains(sID))
				sIDList.add(sID);
			if(!SystemConstants.getMapToplogyDevicePoint().containsKey(sID))
				SystemConstants.putMapToplogyDevicePoint(sID, new HashMap<String,ArrayList<String>>());
			if(!SystemConstants.getMapToplogyPointDevice().containsKey(sID))
				SystemConstants.putMapToplogyPointDevice(sID, new HashMap<String,ArrayList<String>>());
			HashMap<String,ArrayList<String>> devicePointMap = SystemConstants.getMapToplogyDevicePoint().get(sID);
			HashMap<String,ArrayList<String>> pointDeviceMap = SystemConstants.getMapToplogyPointDevice().get(sID);
			
			
			if(pointDeviceMap.get(connectPointID) == null)
				pointDeviceMap.put(connectPointID, new ArrayList());
			if(!pointDeviceMap.get(connectPointID).contains(powerEquipID))
				pointDeviceMap.get(connectPointID).add(powerEquipID);
			if(devicePointMap.get(powerEquipID) == null)
				devicePointMap.put(powerEquipID, new ArrayList());
			if(!devicePointMap.get(powerEquipID).contains(connectPointID))
				devicePointMap.get(powerEquipID).add(connectPointID);
		}
	
		for(String sID : sIDList) {
			Map mapStationDevice = CBSystemConstants.getStationPowerDevices(sID);
			if(mapStationDevice==null){
			}else{
				for(Iterator iter = mapStationDevice.values().iterator();iter.hasNext();) {
					PowerDevice device = (PowerDevice) iter.next();
					if(device.getDeviceType().equals(SystemConstants.MotherLine))
						if(SystemConstants.getMapToplogyDevicePoint().get(sID).get(device.getPowerDeviceID())!=null){
							motherlinePointList.addAll(SystemConstants.getMapToplogyDevicePoint().get(sID).get(device.getPowerDeviceID()));	
						}
				}
			}
			SystemConstants.getMapMotherlinePoint().put(sID, motherlinePointList);
		}
		
	}

	public static List getStationDeviceList(String powerStationID) {
		String ver_id = null;
		String unitcode = CBSystemConstants.unitCode;//获取unitcode
		String selectSql = "select a.ver_id from "+CBSystemConstants.opcardUser+"T_a_statenormalver a where a.ver_flag='1' and a.opcode='0'";
		try {
			ver_id = DBManager.queryForString(selectSql);//获取到常状态最新版本号
		} catch (Exception e) {
			// TODO: handle exception
		}

		String selField = "A.ORGA_ID";
		try {
			DBManager.queryForList("select orga_id from "+CBSystemConstants.equipUser+"T_C_ACLINEEND where rownum=1");
		} catch (Exception e) {
			selField = "D.ORGA_ID";
		}


		String sql = "select " +
				"        '0' ispw, " +
				"        F.*," +
				"        G.OBJ_STATE  " +
				"from " +
				"        ( " +
				"        SELECT " +
				"                A.ORGA_ID, " +
				"                A.SUPERVISIONRIGHT_ID, " +
				"                A.DISPATCH_PERMISSION_ID, " +
				"                A.EQUIP_ID, " +
				"                A.EQUIP_CODE, " +
				"                NVL(NVL(F.MAPPING_NAME, E.EQUIPNAME), A.EQUIP_NAME) EQUIP_NAME, " +
				"                A.CIM_ID, " +
				"                B.STATION_ID, " +
				"                '' ROOM_ID," +
				"                NVL(G.ST_NAME, B.STATION_NAME) STATION_NAME, " +
				"                nvl(C.VOLTAGE_CODE,0) VOLTAGE_CODE, " +
				"                D.EQUIPTYPE_FLAG, " +
				"                E.DEVICERUNTYPE, " +
				"                E.DEVICERUNMODEL, " +
				"                E.DEVICESTATUS, " +
				"                E.DISPATCH, " +
				"                E.MONITORING, " +
				"                E.LOADELECSTATUS " +
				"        from " +
				"                (select * from "+CBSystemConstants.equipUser+"T_EQUIPINFO where STATION_ID in ('"+powerStationID.replace(",", "','")+"')) A left join "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C on A.VOLTAGE_ID  =C.VOLTAGE_ID, " +
				"                "+CBSystemConstants.equipUser+"T_SUBSTATION B, " +
				"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " +
				"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E, " +
				"				 "+CBSystemConstants.equipUser+"TH_EP_MAPPING_EQUIP F, " +
				"				 "+CBSystemConstants.equipUser+"TH_EP_MAPPING_STATION_OMS G" +
				"        where " +
				"                A.STATION_ID=B.STATION_ID " +
				"            and A.EQUIP_ISDEL   =0 " +
				"            and A.EQUIPTYPE_ID    =D.EQUIPTYPE_ID " +
				"            and A.EQUIP_ID     =E.EQUIPID " +
				"			 and F.MAPPING_ID = E.EQUIPID  " +
				"			 and G.ST_ID = B.STATION_ID ";

		sql+=
				"            and B.STATION_ID   in ('"+powerStationID.replace(",", "','")+"') " +
						"        union all " +
						"        SELECT " +
						"                "+selField+" ORGA_ID, " +
						"                D.SUPERVISIONRIGHT_ID SUPERVISIONRIGHT_ID, " +
						"                D.DISPATCH_PERMISSION_ID DISPATCH_PERMISSION_ID, " +
						"                A.ID EQUIP_ID, " +
						"                A.CODE EQUIP_CODE, " +
						"                NVL(NVL(F.MAPPING_NAME, E.EQUIPNAME), A.NAME) EQUIP_NAME, " +
						"                A.CIM_ID, " +
						"                B.STATION_ID, " +
						"                '' ROOM_ID," +
						"                NVL(G.ST_NAME, B.STATION_NAME) STATION_NAME, " +
						"                C.VOLTAGE_CODE, " +
						"                'ACLineSegment', " +
						"                E.DEVICERUNTYPE, " +
						"                E.DEVICERUNMODEL, " +
						"                E.DEVICESTATUS, " +
						"                E.DISPATCH, " +
						"                E.MONITORING, " +
						"                E.LOADELECSTATUS " +
						"        from " +
						"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND A, " +
						"                "+CBSystemConstants.equipUser+"T_SUBSTATION B, " +
						"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " +
						"                "+CBSystemConstants.equipUser+"T_C_LINE D, " +
						"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E, " +
						"				 "+CBSystemConstants.equipUser+"TH_EP_MAPPING_EQUIP F, " +
						"				 "+CBSystemConstants.equipUser+"TH_EP_MAPPING_STATION_OMS G" +
						"        where " +
						"                A.ST_ID=B.STATION_ID " +
						"            and A.acline_id      =d.line_id " +
						"            and A.EQUIP_ISDEL     =0 " +
						"            and A.VOLTAGE_ID  =C.VOLTAGE_ID " +
						"            and A.ID          =E.EQUIPID " +
						"			 and F.MAPPING_ID = E.EQUIPID " +
						"			 and G.ST_ID = B.STATION_ID ";

		sql += "            and B.STATION_ID  in ('"+powerStationID.replace(",", "','")+"') " +
				"        ) " +
				"        f " +
				"Left JOIN " +
				"        ( " +
				"        select " +
				"                STATION_ID, " +
				"                obj_id, " +
				"                obj_state " +
				"        from " +
				"                "+CBSystemConstants.opcardUser+"T_A_STATENORMAL " +
				"        where " +
				"                ver_id='"+ver_id+"' " +
				"            and STATION_ID      in ('"+powerStationID.replace(",", "','")+"') " +
				"        ) " +
				"        G " +
				"On " +
				"        f.EQUIP_ID = G.obj_id";
		if(CBSystemConstants.roleCode.equals("1")) {
			if(CBSystemConstants.isDevMultiLine)
				sql = sql + " union all SELECT '1' ispw," +
						"                A.RUN_UNIT_NO ORGA_ID," +
						"                '' SUPERVISIONRIGHT_ID," +
						"                '' DISPATCH_PERMISSION_ID," +
						"                A.EQUIP_ID," +
						"                A.EQUIP_CODE," +
						"                NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME," +
						"                A.CIM_ID," +
						"                A.LINE_ID ST_ID," +
						"                ROOM_ID ROOM_ID," +
						"                '' NAME," +
						"                C.VOLTAGE_CODE," +
						"                D.EQUIPTYPE_FLAG," +
						"                E.DEVICERUNTYPE," +
						"                E.DEVICERUNMODEL," +
						"                E.DEVICESTATUS," +
						"                E.DISPATCH, " +
						"                E.MONITORING, " +
						"                E.LOADELECSTATUS,null OBJ_STATE " +
						"        from " +
						"                "+CBSystemConstants.equipUser+"T_pd_EQUIPINFO A," +
						"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND B," +
						"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " +
						"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " +
						"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " +
						"        where " +
						"                B.ID='"+powerStationID+"' " +
						"            and A.EQUIP_ISDEL   =0 " +
						"            and A.VOLTAGELVL =C.VOLTAGE_ID " +
						"            and A.EQUIP_TYPE =D.EQUIPTYPE_ID " +
						"            and A.EQUIP_ID   =E.EQUIPID " +
						"            and instr(A.LINE_ID,'"+powerStationID+"')>0";
			else
				sql = sql + " union all SELECT '1' ispw," +
						"                A.RUN_UNIT_NO ORGA_ID," +
						"                '' SUPERVISIONRIGHT_ID," +
						"                '' DISPATCH_PERMISSION_ID," +
						"                A.EQUIP_ID," +
						"                A.EQUIP_CODE," +
						"                NVL(E.EQUIPNAME,A.EQUIP_NAME) EQUIP_NAME," +
						"                A.CIM_ID," +
						"                A.LINE_ID ST_ID," +
						"                ROOM_ID ROOM_ID," +
						"                '' NAME," +
						"                C.VOLTAGE_CODE," +
						"                D.EQUIPTYPE_FLAG," +
						"                E.DEVICERUNTYPE," +
						"                E.DEVICERUNMODEL," +
						"                E.DEVICESTATUS," +
						"                E.DISPATCH, " +
						"                E.MONITORING, " +
						"                E.LOADELECSTATUS,null OBJ_STATE " +
						"        from " +
						"                "+CBSystemConstants.equipUser+"T_pd_EQUIPINFO A," +
						"                "+CBSystemConstants.equipUser+"T_C_ACLINEEND B," +
						"                "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL C, " +
						"                "+CBSystemConstants.equipUser+"T_EQUIPTYPE D, " +
						"                "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO E " +
						"        where " +
						"                B.ID=A.LINE_ID " +
						"            and A.EQUIP_ISDEL   =0 " +
						"            and A.VOLTAGELVL =C.VOLTAGE_ID " +
						"            and A.EQUIP_TYPE =D.EQUIPTYPE_ID " +
						"            and A.EQUIP_ID   =E.EQUIPID " +
						"            and A.LINE_ID in ('"+powerStationID.replace(",", "','")+"')";
		}
		return DBManager.queryForList(sql);
	}

}

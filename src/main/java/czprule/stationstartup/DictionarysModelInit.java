/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 系统启动加载操作,初始化系统常量
 * 作    者 : 张余平
 * 开发日期 : 2010-09-10
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.startup.StartupLoader;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.DeviceStateModel;
import czprule.model.DeviceStatusModel;
import czprule.model.DictionarysModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class DictionarysModelInit implements StartupLoader,StationStartup {
	public void load() {
		// TODO 自动生成方法存根
		//初始化系统字典
		String sql = "";
		List runtypes = null;
		try{
			sql="SELECT T.CODE,T.NAME,T.CODETYPE FROM "+CBSystemConstants.opcardUser+"t_a_DICTIONARY T WHERE UNITCODE='system'  ORDER BY CODETYPE,CODEORDER,NAME ";
			runtypes=DBManager.queryForList(sql);
		}
		catch(Exception ex) {
			ex.printStackTrace();
			sql="SELECT T.CODE,T.NAME,T.CODETYPE FROM "+CBSystemConstants.opcardUser+"t_a_DICTIONARY T WHERE UNITCODE='system'  ORDER BY CODETYPE,NAME ";
			runtypes=DBManager.queryForList(sql);
		}
		
		Map runtype=new HashMap();
		for (Iterator iter = runtypes.iterator(); iter.hasNext();) {
			runtype=(Map)iter.next();
			DictionarysModel drt=new DictionarysModel();
			drt.setCode(runtype.get("CODE").toString());
			drt.setName(runtype.get("NAME").toString());
			drt.setCodetype(runtype.get("CODETYPE").toString());
			CBSystemConstants.addDictionary(drt);
		}
		
		//初始化设备状态
		sql=OPEService.getService().DictionarysModelInit();
		List DevStatus=DBManager.queryForList(sql);
		Map status=new HashMap();
		for (Iterator iter = DevStatus.iterator(); iter.hasNext();) {
			status=(Map)iter.next();
			DeviceStatusModel dsm=new DeviceStatusModel();
			dsm.setStateCode(StringUtils.ObjToString(status.get("STATE_CODE")));
			dsm.setStateName(StringUtils.ObjToString(status.get("STATE_NAME")));
			dsm.setStateCodeSwitch(StringUtils.ObjToString(status.get("SWITCHSTATE_CODE")));
			dsm.setStateNameSwitch(StringUtils.ObjToString(status.get("SWITCHSTATE_NAME")));
			dsm.setEquipTypeFlag(StringUtils.ObjToString(status.get("EQUIPTYPE_FLAG")));
			if(!CBSystemConstants.DeviceStatusModel.containsKey(dsm.getEquipTypeFlag())) {
				CBSystemConstants.DeviceStatusModel.put(dsm.getEquipTypeFlag(), new HashMap<String, DeviceStatusModel>());
			}
			CBSystemConstants.DeviceStatusModel.get(dsm.getEquipTypeFlag()).put(dsm.getStateCode(), dsm);
		}
		
		List<DictionarysModel> equipStatusDicList = CBSystemConstants.getDictionary("EquipStatus");
		for(DictionarysModel dm : equipStatusDicList) {
			CBSystemConstants.DeviceStateModel.put(dm.getCode(), dm.getName());
		}
		
		
		//初始化电压字典
		List<Map> list=DBManager.queryForList(OPEService.getService().DictionaryModelInitSql());
		for (Map map :list){
			CBSystemConstants.VoltMap.put((String)map.get("VOLTAGE_ID"), (String)map.get("VOLTAGE_CODE"));
		}
		
		
	}

	public void StationLoad(String stationID) {
		// TODO 自动生成方法存根
		
	}

}

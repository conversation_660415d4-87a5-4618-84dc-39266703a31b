package czprule.stationstartup;

import java.io.InputStream;
import java.util.ArrayList;
import java.util.Enumeration;
import java.util.Iterator;
import java.util.List;
import java.util.Properties;

import org.apache.log4j.Logger;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.startup.StartupManager;

import czprule.system.CBSystemConstants;

public class StationStartupManager {
	private static Logger log = Logger.getLogger(StartupManager.class);
	private static List<StationStartup> startupLoaders = new ArrayList<StationStartup>();
	
	static {
		try {
			InputStream inputStream = StationStartupManager.class.getResourceAsStream("/StationstartupLoader.properties");
			if (inputStream != null) {
				LinkedProperties properties = new LinkedProperties();
				properties.load(inputStream);
				
				 Enumeration enumeration = properties.keys(); 
				 for (int i = 0; i < properties.getKeys().size(); i++) {
					String key = (String)properties.getKeys().get(i);
					String className = properties.getProperty(key);

					Class clazz = Class.forName(className);
					registerStartupLoader(clazz);
					//log.info("Register StationstartupLoader in ordered for class: " + className);
				}
			}
		} catch (Exception ex) {
			log.error("Load startup loader classes fail.", ex);
		}
	}

    public static void startup(String stationID) {
        startup(stationID,true);
    }
	public static void startup(String stationID,boolean isLoadBySql) {
		for (Iterator iterator = startupLoaders.iterator(); iterator.hasNext();) {
			StationStartup loader = (StationStartup) iterator.next();
			if(!isLoadBySql && (loader instanceof StationDeviceToplogy)){
                ((StationDeviceToplogy)loader).StationLoad(stationID,false);
            }else{
                loader.StationLoad(stationID);
            }
		}
		
		if(CBSystemConstants.isOpeDeviceTypeInit){
			if(!stationID.equals("")) {
				String[] staarr = stationID.split(",");
				for(String sta : staarr) {
					CZPService.getService().initDeviceRunType(sta);
				}
			}
		}
	}
	
	private static void registerStartupLoader(Class clazz) {
		StationStartup loader;
		try {
			loader = (StationStartup)clazz.newInstance();
			startupLoaders.add(loader);
		} catch (InstantiationException e) {
			log.error(e.getMessage(), e);
		} catch (IllegalAccessException e) {
			log.error(e.getMessage(), e);
		}
	}
}

class LinkedProperties extends Properties { 
	 
    private final List<Object> keys = new ArrayList<Object>(); 
 
    public List getKeys() {
    	return keys;
    }
 
    @Override
	public Object put(Object key, Object value) { 
        keys.add(key); 
        return super.put(key, value); 
    } 
} 

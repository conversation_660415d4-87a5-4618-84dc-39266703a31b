package czprule.stationstartup;

import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import org.apache.batik.dom.svg.SAXSVGDocumentFactory;
import org.apache.batik.util.XMLResourceDescriptor;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.czp.svg.document.DefaultSVGDocumentResolver;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

/**
 * 图模校核算法
 * */
public class CheckSvgAndData {
	public static SVGDocumentResolver resovler = DefaultSVGDocumentResolver
			.getResolver();
	// 初始化与查询算法有关的变量
	public static CommonSearch cs = new CommonSearch();
	public static Map<String, Object> inPara = new HashMap<String, Object>();
	public static Map<String, Object> outPara = new HashMap<String, Object>();
    public static String unCheckLayer="Substation_Layer,MeasurementValue_Layer,Link_Layer,Clock_Layer";
	/**
	 * 根据数据模型检查svg图形是否有遗漏
	 * 
	 * @return 设备的List
	 * */
	public static List<PowerDevice> checkSvgByData(String stationID) {
		if (stationID == null || stationID.equals("")) {
			return null;
		}
		new StationDeviceToplogy();
		StationDeviceToplogy.loadStationPowerDevices(stationID);
		List<PowerDevice> result = new ArrayList<PowerDevice>();
		HashMap<String, PowerDevice> stationMap = CBSystemConstants
				.getStationPowerDevices(stationID);
		// 复制一个map
		if (stationMap == null) {
			return null;
		}
		HashMap<String, PowerDevice> checkMap = new HashMap<String, PowerDevice>(
				stationMap);
		List<SVGFile> svgFiles = SystemConstants
				.getSVGFileByStationID(stationID);
		if (svgFiles == null || svgFiles.size() == 0) {
			// 返回null表示没关联厂站图
			return null;
		}
		List<SVGDocument> svgdocs = new ArrayList<SVGDocument>();
		for (SVGFile sf : svgFiles) {
			svgdocs.add(getDocBySVGFile(sf));
		}
		PowerDevice pd;
		for (Iterator<String> iter = checkMap.keySet().iterator(); iter
				.hasNext();) {
			pd = checkMap.get(iter.next());
			if (!isInSvgDocs(pd, svgdocs)) {
				result.add(pd);
			}
		}
		return result;

	}

	// 检查设备是否在svg模型中
	private static boolean isInSvgDocs(PowerDevice pd, List<SVGDocument> svgdocs) {
		Element root;
		Element layer;
		Node node1;
		NodeList equips;
		String equipid;
		for (SVGDocument svgdoc : svgdocs) {
			root = resovler.resolveSvgElement(svgdoc);
			for (int i = 0; i < root.getChildNodes().getLength(); i++) {
				node1 = root.getChildNodes().item(i);
				if (node1.getNodeName().equals("#text"))
					continue;
				layer = (Element) node1;
				equips = resovler.getDeviceGroupElementList(layer);
				for (int j = 0; j < equips.getLength(); j++) {
					equipid = resovler.getDeviceID((Element) equips.item(j));
					if (equipid != null
							&& equipid.equals(pd.getPowerDeviceID())) {
						return true;
					}
				}
			}
		}
		return false;
	}

	/**
	 * 根据svg图形检查数据模型是否有遗漏
	 * 
	 * @return Elment集合
	 * */
	public static List<String> checkDataBySvg(String stationID) {
		if (stationID == null || stationID.equals("")) {
			return null;
		}
		
		
		StationDeviceToplogy.loadStationPowerDevices(stationID);
		//test
		//CBSystemConstants.getStationPowerDevices(stationID).remove("9b52b32b-9bff-4ceb-9bb6-37288e57aa8f");
		
		List<SVGFile> svgFiles = SystemConstants
				.getSVGFileByStationID(stationID);
		SVGDocument doc;
		Element svg;
		Node node1;
		Node node2;
		Element layerElement;
		Element equipElement;
		NodeList equips;
		String equipid;
		List<String> result = new ArrayList<String>();
		for (SVGFile sf : svgFiles) {
			doc = getDocBySVGFile(sf);
			svg = resovler.resolveSvgElement(doc);
			for (int i = 0; i < svg.getChildNodes().getLength(); i++) {
				node1 = svg.getChildNodes().item(i);
				if (node1.getNodeName().equals("#text"))
					continue;
				else if (node1.getNodeName().equals("defs"))
					continue;
				
			    else if(unCheckLayer.contains(((Element)node1).getAttribute("id"))) 
					  continue;
				
				else {
					layerElement = (Element) node1;
					equips = layerElement.getChildNodes();
					for (int j = 0; j < equips.getLength(); j++) {
						node2 = equips.item(j);
						if (!node2.getNodeName().equals("g"))
							continue;
						equipElement = (Element) node2;
						equipid = getDeviceID(equipElement);
						if(equipid==null){
							continue;
						}
						if (equipid != null && !equipid.equals("")) {
							if (CBSystemConstants.getPowerDevice(stationID, equipid) == null) {
								result.add(equipElement.getAttribute("id"));
							}
						}
						else
							result.add(equipElement.getAttribute("id"));
					}
				}
			}

		}
		return result;
	}

	/**
	 * 根据svgfile对象获取svgDocument
	 * */
	private static SVGDocument getDocBySVGFile(SVGFile svgFile) {
		String parser = XMLResourceDescriptor.getXMLParserClassName();
		SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory(parser);
		SVGDocument doc = null;
		try {
			File file = new File(svgFile.getFilePath());
			doc = factory.createSVGDocument(file.toURI().toString());
		} catch (IOException e) {
			e.printStackTrace();
		}
		return doc;
	}

	/** 设备状态校核 */
	public static List<PowerDevice> checkDeviceState(String stationID) {
		if (stationID == null || stationID.equals("")) {
			return null;
		}
		// 把厂站信息加入缓存
		StationDeviceToplogy.loadStationPowerDevices(stationID);
		// 把厂站连接信息加入缓存为查询算法提供数据
		StationDeviceToplogy.loadConnectCache(stationID);
		HashMap<String, PowerDevice> deviceMap = CBSystemConstants
				.getStationPowerDevices(stationID);
		PowerDevice equip;
		List<PowerDevice> checkout = new ArrayList<PowerDevice>();
		// 遍历厂站内所有设备
		for (String equipID : deviceMap.keySet()) {
			equip = deviceMap.get(equipID);
			if (equip.getDeviceType().equals(SystemConstants.Switch)) {
				if (!judgeSwitch(cs, inPara, outPara, equip)) {
					checkout.add(equip);
				}
			} else if (equip.getDeviceType().equals(
					SystemConstants.SwitchSeparate)) {
				if (!judgeSwitchSeparate(cs, inPara, outPara, equip)) {
					checkout.add(equip);
				}
			} else if (equip.getDeviceType().equals(
					SystemConstants.SwitchFlowGroundLine)) {
				if (!judgeSwitchFlowGroundLine(cs, inPara, outPara, equip)) {
					checkout.add(equip);
				}
			}
		}
		return checkout;
	}

	/**
	 * 开关状态校核
	 * 
	 * @return true 校核无误
	 */
	private static boolean judgeSwitch(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice equip) {
		List<PowerDevice> separates = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.SwitchSeparate);
		List<PowerDevice> groundlines = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.SwitchFlowGroundLine);
		// 开关在合位
		if (equip.getDeviceStatus().equals("0")) {
			// 某侧不能都断开
			if (!judgeSwitchEachSide(separates, "1")) {
				return false;
			}
			// 接地刀闸不能为合位
			if (judgeState(groundlines, "0")) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 某侧刀闸不能都断开
	 * 只考虑了一侧最多两刀闸的情况
	 * @return true 正常 false 有某侧刀闸都断开了
	 * */
	private static boolean judgeSwitchEachSide(List<PowerDevice> separates,
			String string) {
         for(PowerDevice pd:separates){
        	 //如果刀闸断开了
        	 if(pd.getDeviceStatus().equals("1")){
        		 for(PowerDevice dev:separates){
        			 //如果两刀闸相连
        			if( RuleUtil.isConnected(pd, dev, cs, inPara, outPara)){
        				//如果它也是断开状态
        				if(dev.getDeviceStatus().equals("1")){
        					return false;
        				}
        			}
        		 }
        	 }
         }
		return true;
	}

	/**
	 * 刀闸状态校核
	 * 
	 * @return true 校核无误
	 * */
	private static boolean judgeSwitchSeparate(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice equip) {
		List<PowerDevice> switchs = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.Switch);
		List<PowerDevice> groundlines = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.SwitchFlowGroundLine);
		// 刀闸在合位
		if (equip.getDeviceStatus().equals("0")) {
			// //接地刀闸不能在合位
			if (judgeState(groundlines, "0")) {
				return false;
			}
		}
		return true;

	}

	/**
	 * 接地刀闸状态校核
	 * 
	 * @return true 校核无误
	 */
	private static boolean judgeSwitchFlowGroundLine(CommonSearch cs,
			Map<String, Object> inPara, Map<String, Object> outPara,
			PowerDevice equip) {
		List<PowerDevice> separates = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.SwitchSeparate);
		List<PowerDevice> switchs = RuleUtil.getDirectDevice(cs, inPara,
				outPara, equip, SystemConstants.Switch);
		// 接地刀闸在合位
		if (equip.getDeviceStatus().equals("0")) {
			// 刀闸不能为合位
			if (judgeState(separates, "0")) {
				return false;
			}
			// 开关不能为合位
			if (judgeState(switchs, "0")) {
				return false;
			}
		}
		return true;
	}

	/**
	 * 查找集合中所有设备状态是否与目标状态一致
	 * 
	 * @param pds
	 *            设备集合
	 * @param targetState
	 *            目标状态
	 * */
	private static boolean judgeState(List<PowerDevice> pds, String targetState) {
		if (pds == null || pds.size() == 0) {
			return false;
		}
		for (PowerDevice pd : pds) {
			if (!pd.getDeviceStatus().equals(targetState)) {
				return false;
			}
		}
		return true;
	}
	
	public static String getDeviceID(Element element) {
		String deviceID = null;
		Element metadata;
		if((metadata = getChildByTagName(element, "metadata")) != null)
		{
			if(metadata.getElementsByTagName("cge:TPSR_Ref").getLength() != 0)
			{
				Element psr = (Element)metadata.getElementsByTagName("cge:TPSR_Ref").item(0);
				if(psr.hasAttribute("TObjectID"))
					deviceID =psr.getAttribute("TObjectID");
			}
			
		}
		return deviceID;
	}
	
	protected static Element getChildByTagName(Element e, String name) {
		Element element = null;
		NodeList nodeList = e.getChildNodes();
		for (int i = 0; i < nodeList.getLength(); i++)
		{
			if (nodeList.item(i) instanceof Element && nodeList.item(i).getNodeName().equals(name)) 
			{
				element =  (Element)nodeList.item(i);
				break;
			}
		}
		return element;
	}
}

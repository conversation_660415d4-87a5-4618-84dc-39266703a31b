/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 对涉及系统运行的配置文件进行解析
 * 作    者 : 张余平
 * 开发日期 : 2008-07-16
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import org.apache.log4j.Level;
import org.apache.log4j.Logger;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.czp.app.CZPImpl;
import com.tellhow.czp.operationcard.TempTicket;
import com.tellhow.czp.user.UserLoginInter;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.loader.ConfigurationLoader;
import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class ProjectConfigLoader extends ConfigurationLoader{
	
	//private  Logger log = Logger.getLogger(ProjectConfigLoader.class);
	private NodeList allVlaues;
	private Document doc=null;
	
	public void load() {
		loadConfiguration(false, "");
		loadProjectConfig(false, "");
	}
	
	public void loadProjectConfig(boolean isReload, String paramName) {
		
        try {

			try {
				doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
			} catch (Exception e) {
				//log.error(e.getMessage(), e);
				return;
			}

            Element rootE = doc.getDocumentElement();

            NodeList childEs = rootE.getChildNodes();
            Element childE = null;
            Element values = null;
            for (int i = 0; i < childEs.getLength(); i++) {
            	if(childEs.item(i).getNodeName().equals("#text"))
            		continue;
                childE = (Element) childEs.item(i);
                if(isReload)
                {
	                if(!childE.getAttribute("name").equals(paramName))
	                	continue;
                }
                
                if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
                    Element codeElem = null;
                    String codeKey = "";
                    String codeValue="";
                     allVlaues = childE.getElementsByTagName("value");
                    for (int j = 0; j < allVlaues.getLength(); j++) {
                        codeElem = (Element) allVlaues.item(j);
                        codeKey = codeElem.getAttribute("key").trim();
                        codeValue=codeElem.getTextContent().trim();
                        
                        if("cardtype".equals(codeKey)){
                        	CBSystemConstants.cardtype=codeValue;
                        	continue;
                        }
                        else if("RealTimeService".equals(codeKey)){
							CBSystemConstants.RealTimeService=codeValue;   
							continue;
						}
                        else if("wlflAddress".equals(codeKey)){
							CBSystemConstants.wlflAddress=codeValue;   
							continue;
						}
                        else if("apptype".equals(codeKey)){
//                        	CBSystemConstants.apptype=codeValue;
//                        	if(CBSystemConstants.apptype.equals("0"))
//                        		CBSystemConstants.cardstatus = "0";
//                        	else if(CBSystemConstants.apptype.equals("1"))
//                        		CBSystemConstants.cardstatus = "2";
                        	continue;
                        }
                        else if("dxp".equals(codeKey)){
                        	CBSystemConstants.dxp=codeValue;  
							continue;
                        }
                        else if("unitCode".equals(codeKey)){
//							CBSystemConstants.unitCode=codeValue;   
							continue;
						}
                        else if("projectID".equals(codeKey)){
							CBSystemConstants.projectID=codeValue;   
							continue;
						}
                        else if("isAutoLoadSVGFile".equals(codeKey)){
							CBSystemConstants.isAutoLoadSVGFile=codeValue;      
							continue;
						}
                        else if("isvalidateMAC".equals(codeKey)){
							CBSystemConstants.isvalidateMAC=Boolean.parseBoolean(codeValue);   
							continue;
                        }
//                        else if("isInitEMSStatus".equals(codeKey)){
//							CBSystemConstants.isInitEMSStatus=Boolean.parseBoolean(codeValue);  
//							continue;
//                        }
//                        else if ("isInitDoubleScreen".equals(codeKey)){
//                        	SystemConstants.isInitDoubleScreen=Boolean.parseBoolean(codeValue);
//                        	continue;
//                        }
                       /* else if("isUseEmsColor".equals(codeKey)){
							CBSystemConstants.isUseEmsColor=Boolean.parseBoolean(codeValue);  
							continue;
                        }*/
                        else if("isCheckCloseWindow".equals(codeKey)){
                            CBSystemConstants.isCheckCloseWindow=Boolean.parseBoolean(codeValue);   
                            continue;
                        }
                        else if("isUseSysColor".equals(codeKey)){
                            CBSystemConstants.isUseSysColor=Boolean.parseBoolean(codeValue);   
                            continue;
                        }
                        else if("isPrintSQL".equals(codeKey)){
                            DBManager.isPrintSQL=Boolean.parseBoolean(codeValue);   
                            continue;
                        }
                        else if("isFlowShow".equals(codeKey)){
                            CBSystemConstants.isFlowShow=Boolean.parseBoolean(codeValue);   
                            continue;
                        }
                        else if("mapScale".equals(codeKey)){
							SystemConstants.MAP_SCALE=codeValue;  
							continue;
                        }
                        else if("rememberPassword".equals(codeKey)){
							CBSystemConstants.rememberPassword=codeValue;  
							continue;
                        }
                        else if("password".equals(codeKey)){
							CBSystemConstants.password=codeValue;  
							continue;
                        }
                        else if("defaultUser".equals(codeKey)){
							CBSystemConstants.defaultUser=codeValue;  
							continue;
                        }
                        else if("flowNum".equals(codeKey)){
							CBSystemConstants.FLOW_NUM=codeValue;  
							continue;
                        }
                        else if("isMaxRangeOff".equals(codeKey)){
                        	if(codeValue.equals("0") || codeValue.equals("1"))
                        		CBSystemConstants.isMaxRangeOffTicket=codeValue.equals("0")?false:true;
                        	else
                        		CBSystemConstants.isMaxRangeOffTicket=Boolean.parseBoolean(codeValue);
							continue;
                        } else if ("emsetluser".equals(codeKey)) {
							CBSystemConstants.emsetlUser = codeValue;
						} else if("equipuser".equals(codeKey)){
							CBSystemConstants.equipUser=codeValue;  
							continue;
                        }
                        else if("opcarduser".equals(codeKey)){
							CBSystemConstants.opcardUser=codeValue; 
							if(CBSystemConstants.deviceEquipUser.equals("opcard.")){//没配置deviceEquipUser的，默认取CBSystemConstants.opcardUser
								CBSystemConstants.deviceEquipUser=codeValue; 
							}
							continue;
                        }
                        else if("platformuser".equals(codeKey)){
							CBSystemConstants.platformUser=codeValue;  
							continue;
                        }else if("deviceequipuser".equals(codeKey)){
							CBSystemConstants.deviceEquipUser=codeValue;  
							continue;
                        }
                        else if("isSetToplogySVGColor".equals(codeKey)){
                        	CBSystemConstants.isSetToplogySVGColor = Boolean.parseBoolean(codeValue);   
							continue;
                        }
                        else if("isOpeDeviceTypeInit".equals(codeKey)){
                        	CBSystemConstants.isOpeDeviceTypeInit = Boolean.parseBoolean(codeValue);   
                        	continue;
                        }
                        else if("appConfig".equals(codeKey)){
							CBSystemConstants.appConfig=codeValue;  
							continue;
                        }
                        else if("isProcessSwitch".equals(codeKey)){
							CBSystemConstants.isProcessSwitch=Boolean.parseBoolean(codeValue);  
							continue;
                        }
                        else if("isSpecialMenu".equals(codeKey)){
							CBSystemConstants.isSpecialMenu=Boolean.parseBoolean(codeValue);  
							continue;
                        }
                        else if("useOldRule".equals(codeKey)){
							if("1".equals(codeValue)){
								CBSystemConstants.useOldRole = true;
							}
							continue;
                        }
                        else if("qybm".equals(codeKey)){
							CBSystemConstants.qybm=codeValue;   
							continue;
						}
                        else if("isLimitStateMultipleTicket".equals(codeKey)){
							CBSystemConstants.isLimitStateMultipleTicket=Boolean.parseBoolean(codeValue); 
							continue;
						}
                        else if("bdzTicket".equals(codeKey)){
							CBSystemConstants.bdzTicket=Boolean.parseBoolean(codeValue); 
                            continue;
                        }
                        else if("isContinueTicket".equals(codeKey)){
							CBSystemConstants.isContinueTicket=Boolean.parseBoolean(codeValue); 
                            continue;
                        }
                    }
                }
                else if (childE.getAttribute("name").toUpperCase().equals("DEVICECODE")) {//获取设备编号规范
                    Element codeElem = null;
                    String codeKey = "";
                    String codeName = "";
                    NodeList allVlaues = childE.getElementsByTagName("value");
                    for (int j = 0; j < allVlaues.getLength(); j++) {
                        codeElem = (Element) allVlaues.item(j);
                        codeName = codeElem.getAttribute("code").trim();
                        codeKey = codeElem.getAttribute("key").trim();
                        CBSystemConstants.putDeviceTypeCodes(codeKey, codeName);
                    }
                }
                else if (childE.getAttribute("name").equals("tuoDevice")) {//获取二次设备术语规范
                    Element codeElem = null;
                    String codeKey = "";
                    String codeName = "";
                    NodeList allVlaues = childE.getElementsByTagName("value");
                    for (int j = 0; j < allVlaues.getLength(); j++) {
                        codeElem = (Element) allVlaues.item(j);
                        codeName = codeElem.getAttribute("code").trim();
                        codeKey = codeElem.getAttribute("key").trim();
                        CBSystemConstants.setTwoDevice(codeName);
                    }
                }
//                else if (childE.getAttribute("name").equals("czpOperator")) { //读取应用实现类
//                    values = (Element)childE.getElementsByTagName("value").item(0);
//                    String className = values.getTextContent().trim();
//					try {
//						Class clazz = Class.forName(className);
//						//CZPOperator.setOperator(clazz);
//					} catch (ClassNotFoundException e) {
//						log.error(className + " not found.");
//					}
//                }
                else if(childE.getAttribute("name").equals("userlogin")) { //读取应用实现类
                    values = (Element)childE.getElementsByTagName("value").item(0);
                    String className = values.getTextContent().trim();
					try {
						Class clazz = Class.forName(className);
						UserLoginInter.setuserlogininter(clazz);
					} catch (ClassNotFoundException e) {
						//log.error(className + " not found.");
					}
                }else if (childE.getAttribute("name").equals("TempTicket")) { //读取应用实现类
                    values = (Element)childE.getElementsByTagName("value").item(0);
                    String className = values.getTextContent().trim();
					try {
						Class clazz = Class.forName(className);
						TempTicket.setTempTicket(clazz);
					} catch (ClassNotFoundException e) {
						//log.error(className + " not found.");
					}
                }else if (childE.getAttribute("name").equals("omsweburl")) { //读取应用实现类
                    values = (Element)childE.getElementsByTagName("value").item(0);
					CBSystemConstants.omsurl = values.getTextContent().trim();
                }
            }
            
//            if(CBSystemConstants.roleCode.equals("0")){
//        		CBSystemConstants.cardstatus = "0";
//            }
//        	else if(CBSystemConstants.roleCode.equals("2")){
//        		CBSystemConstants.cardstatus = "2";
//        	}
//            if(SystemConstants.isInitEMSStatus.equals("1")){
//            	CBSystemConstants.cardstatus = "1";
//            } else{
//            	CBSystemConstants.cardstatus = "0";
//            }
            String filePatten = CZPImpl.getPropertyValue("FilePattenDel");
            SystemConstants.MAP_NAME_PATTERN = filePatten==null?"":filePatten;
        } catch (Exception ex) {
            Logger.getLogger(ProjectConfigLoader.class.getName()).log(Level.FATAL, null, ex);
        }
	}
	/**
	 * 修改codeKey
	 * */
	public void setCodeKey(String key, String value) {
		Element codeElem = null;
		String codeKey = "";
		if (allVlaues == null) {
			return;
		}
		for (int j = 0; j < allVlaues.getLength(); j++) {
			codeElem = (Element) allVlaues.item(j);
			codeKey = codeElem.getAttribute("key").trim();
			if (key.equals(codeKey)) {
				codeElem.setTextContent(value);
				DOMUtil
						.writeXMLFile(doc,
								CBSystemConstants.SYS_CONFIG_XML_FILE);

				break;
			}
		}
	}
	//获取key的值
	public String getCodeKey(String key) {
		Element codeElem = null;
		String codeKey = "";
		if (allVlaues == null) {
			return null;
		}
		for (int j = 0; j < allVlaues.getLength(); j++) {
			codeElem = (Element) allVlaues.item(j);
			codeKey = codeElem.getAttribute("key").trim();
			if (key.equals(codeKey)) {
			return codeElem.getTextContent();
			
			}
		}
		return null;
	}
	
}

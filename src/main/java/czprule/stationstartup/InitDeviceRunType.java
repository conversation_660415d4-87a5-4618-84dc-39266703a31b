/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 系统启动加载操作,初始化设备运行状态
 * 作    者 : 张余平
 * 开发日期 : 2010-09-10
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.dao.DevicePropertyDB;
import czprule.algorithm.devicerunmode.JudgeLineRunMode;
import czprule.algorithm.devicerunmode.JudgeMotherLineRunMode;
import czprule.algorithm.devicerunmode.JudgeSwitchRunMode;
import czprule.algorithm.deviceruntype.JudgeGroundKnifeRunType;
import czprule.algorithm.deviceruntype.JudgeKnifeType;
import czprule.algorithm.deviceruntype.JudgeSideMotherLine;
import czprule.algorithm.deviceruntype.JudgeSwitchType;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class InitDeviceRunType implements StationStartup {

	public void StationLoad(String stationID) {
		// TODO 自动生成方法存根
//		String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
		//edit 2014.6.23
		String sql = OPEService.getService().getIdrt(stationID);
		boolean isExistNewEquip = DBManager.queryForInt(sql)==0?false:true;
		if(isExistNewEquip) {
			execute(stationID);
			DBManager.update("update "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO a set a.devicestatus='0' where a.devicestatus='-1' and a.equipid in (select t.equip_id from "+CBSystemConstants.equipUser+"T_EQUIPINFO t where t.station_id='"+stationID+"' union all select b.id from "+CBSystemConstants.equipUser+"t_c_Aclineend b where b.st_id='"+stationID+"')");
		}
		else {
			if(CBSystemConstants.roleCode.equals("1") && CBSystemConstants.getMapPowerFeeder().containsKey(stationID)) {
	    		
	    		
	    		List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+stationID+"'");
	            if(list.size() > 0 && list.get(0).get("st_id")!=null) {
	            	if(CBSystemConstants.getStationPowerDevices(list.get(0).get("st_id").toString())==null){
	            		InitDeviceRunType idrte = new InitDeviceRunType();
						idrte.execute(stationID);
	    			}
	            	CZPService.getService().initDeviceRunType(list.get(0).get("st_id").toString());
	            }
	    	}
		}
	}
	
	public void execute(String stationID) {
		JudgeKnifeType jkt= new JudgeKnifeType();
    	JudgeSwitchType jst=new JudgeSwitchType();
    	JudgeSwitchRunMode jslm=new JudgeSwitchRunMode();
    	JudgeMotherLineRunMode jmlm=new JudgeMotherLineRunMode();
    	JudgeSideMotherLine jsm=new JudgeSideMotherLine();
    	JudgeLineRunMode jclm=new JudgeLineRunMode();
    	JudgeTransformerRunMode jtrm = new JudgeTransformerRunMode();
    	JudgeGroundKnifeRunType jgkrt= new JudgeGroundKnifeRunType();
    	if(CBSystemConstants.getStationPowerDevices(stationID) == null)
    		return;
    	
    	if(CBSystemConstants.roleCode.equals("1") && CBSystemConstants.getMapPowerFeeder().containsKey(stationID)) {
    		CZPService.getService().initDeviceRunType(CBSystemConstants.getMapPowerFeeder().get(stationID).getPowerStationID());
    		CZPService.getService().initDeviceRunType(stationID); //特殊初始化接线方式、安装类型
    		return;
    	}
    	if(!CBSystemConstants.roleCode.equals("1")) {
	    	//System.out.println("判断母线接线方式");
	    	//判断母线接线方式
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				
				inMap.put("oprSrcDevice", pd);
				 if(SystemConstants.MotherLine.equals(pd.getDeviceType()))
					jmlm.doJudge(pd);
			}
	    	//System.out.println("判断母线运行类型");
	    	//判断母线运行类型
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.MotherLine.equals(pd.getDeviceType()))
					jsm.doJudge(pd);
				
	    	}
	    	//System.out.println("判断刀闸运行类型");
	    	
	    	//判断设备运行类型
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.SwitchSeparate.equals(pd.getDeviceType()))
					jkt.doJudge(pd);
				else if(SystemConstants.SwitchFlowGroundLine.equals(pd.getDeviceType()))
					jgkrt.doJudge(pd);
				pd.setDeviceSetType(pd.getDeviceRunType());
				
	    	}
	    	//System.out.println("判断开关运行类型");
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.Switch.equals(pd.getDeviceType()))
					jst.doJudge(pd);
				pd.setDeviceSetType(pd.getDeviceRunType());
	    	}
	    	
	    	//System.out.println("判断开关接线方式");
	    	//判断开关接线方式
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();) {
				PowerDevice pd = (PowerDevice) iter.next();
				Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.Switch.equals(pd.getDeviceType()))
					jslm.doJudge(pd);
			}
	//    	System.out.println("判断主变接线方式");
	//    	//判断主变是否为内桥接线
	    	for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();){
	    		PowerDevice pd = (PowerDevice) iter.next();
	    		Map<String,Object> inMap = new HashMap<String,Object>();
				Map outMap = new HashMap();
				
				inMap.put("oprSrcDevice", pd);
				if(SystemConstants.PowerTransformer.equals(pd.getDeviceType()))
					jtrm.doJudge(pd);
	    	}
    	}
    	CZPService.getService().initDeviceRunType(stationID); //特殊初始化接线方式、安装类型
    	DevicePropertyDB dpd = new DevicePropertyDB();
    	dpd.deviceModelInsertList(stationID);
    	dpd.deviceRunTypeInsertList(stationID);
	}

}

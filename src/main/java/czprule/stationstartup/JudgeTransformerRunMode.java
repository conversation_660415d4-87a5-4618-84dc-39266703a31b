package czprule.stationstartup;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

/**
 * 判断主变是否为内桥接线 1：是内桥接线 2：不是内桥接线 -1：无意义
 * <AUTHOR>
 *
 */
public class JudgeTransformerRunMode implements DeviceTypeJudgeInf{

	@Override
	public String doJudge(PowerDevice pd) {
		DevicePropertyDB idrt=new DevicePropertyDB();//状态插入类
		
		if(pd == null)
			return "-1";
		if(RuleUtil.isTransformerNQ(pd)){
			pd.setDeviceRunModel(CBSystemConstants.RunModelBridgeLine);
			return "1";
		}
		else {
			pd.setDeviceRunModel("");
			return "2";
		}
	}

}

/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 将历史遥信数据表和即时遥信数据表对比，将其中存在差异的设备和其信号一起存入缓冲中
 * 作    者 : 张余平
 * 开发日期 : 2010-09-09
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.startup.StartupLoader;

import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/**
 * 
 * 用途：一般在系统启动后加载，或者电站启动后加载，也可定时加载
 * <AUTHOR>
 *
 */
public class GetDifferStatusDevices implements StartupLoader,StationStartup{   
         

	//系统启动加载
	public void load() {
		// TODO 自动生成方法存根
		String sql = "";
		
		//清除"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO的垃圾数据，一般用不到
//		if(!CBSystemConstants.filterSomeSQL){
//			DBManager.execute(OPEService.getService().getDsd2());
//		}

		
		//同步DEVICEEQUIPINFO表设备
//		int count = DBManager.queryForInt("select a.ac+b.bc+c.cc-d.dc from (select count(*) as ac from "+CBSystemConstants.equipUser+"T_EQUIPINFO) a,(select count(*) as bc from "+CBSystemConstants.equipUser+"T_C_ACLINEEND) b,(select count(*) as cc from "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO) c,(select count(*) as dc from "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO) d");
//		if(count > 0) {
			sql=OPEService.getService().getDsd();
			DBManager.execute(sql);
//		}
		
        //同步线路解和环表
//        sql="INSERT INTO "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO SELECT E.EQUIP_ID,'0' FROM "+CBSystemConstants.opcardUser+"t_e_SUBSTATIONTOPOLOGY E WHERE E.EQUIP_ID not in (SELECT T.EQUIP_ID FROM "+CBSystemConstants.opcardUser+"T_A_LINEFLOWINFO T)";
        //edit 2014.6.25
		//sql= OPEService.getService().GetDifferStatusDevicesSql();
        //DBManager.execute(sql);
        
        //初始化新设备
       // InitDeviceRunType idrt = new InitDeviceRunType();
        //InitDeviceStatus ie=new InitDeviceStatus();
        CreatePowerStationToplogy.loadSysData();
//        sql = "SELECT B.STATION_ID FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' GROUP BY B.STATION_ID";
        //edit 2014.6.30
        /*
        sql = OPEService.getService().GetDIfferStatusDevicesSql1();
        List stationList = DBManager.query(sql);
		if(stationList.size()!=0) {
			for (Iterator iter = stationList.iterator(); iter.hasNext();) {
				Map map = ((Map)iter.next());
				if(map.get("STATION_ID") == null)
					continue;
				String stationID=map.get("STATION_ID").toString();
				if(CBSystemConstants.getPowerStation(stationID)==null)
					continue;
				System.out.println("初始化厂站数据："+CBSystemConstants.getPowerStation(stationID).getPowerStationName());
				CreatePowerStationToplogy.loadFacData(stationID);
				idrt.execute(stationID);
				ie.initStatus(stationID);
			}
		}
		
		ArrayList<String> sqlList= new ArrayList<String>();
		String cb=CBSystemConstants.cardstatus;
		if(cb.equals("0")){
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICESTATUS='0' where DEVICESTATUS='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DISPATCH=T.DEVICESTATUS where T.DISPATCH='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.MONITORING=T.DEVICESTATUS where T.MONITORING='-1'");
		}else if(cb.equals("1")){
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DISPATCH='0' where DISPATCH='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICESTATUS=T.DISPATCH where T.DEVICESTATUS='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.MONITORING=T.DISPATCH where T.MONITORING='-1'");
		}else{
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.MONITORING='0' where MONITORING='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICESTATUS=T.MONITORING where T.DEVICESTATUS='-1'");
			sqlList.add("UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DISPATCH=T.MONITORING where T.DISPATCH='-1'");
		}
		for(String s : sqlList) {
			DBManager.update(s);
		}
		*/
	}
    //变电站启动加载
	public void StationLoad(String stationID) {
		// TODO 自动生成方法存根
		this.load();
	}
}  


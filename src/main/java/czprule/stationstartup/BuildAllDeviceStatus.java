/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 变电站启动加载操作,设置存在差异的设备状态
 * 作    者 : 张余平
 * 开发日期 : 2010-09-26
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.startup.StartupLoader;

import czprule.algorithm.dao.DevicePropertyDB;
import czprule.algorithm.devicestatus.ElecCapacityStatus;
import czprule.algorithm.devicestatus.ElecShockStatus;
import czprule.algorithm.devicestatus.LineStatus;
import czprule.algorithm.devicestatus.MainTransStatus;
import czprule.algorithm.devicestatus.MotherLineStatus;
import czprule.algorithm.devicestatus.SwitchStatus;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class BuildAllDeviceStatus  implements StationStartup,StartupLoader{

	

	public void load() {
		// TODO 自动生成方法存根
	}

	public void StationLoad(String stationID) {
		// TODO 自动生成方法存根
		Map<String,String> DevChangeStatus=CBSystemConstants.DevcieStatusCache.get(stationID);  //变电站内遥信状态和实际状态存在差异的设备集合
		if(DevChangeStatus==null)
			return;
		
		 Map<Object,String> Switchs=new HashMap<Object,String>();      //开关
		 Map<Object,String> monthlines=new HashMap<Object,String>();   //母线
		 Map<Object,String> maintrans=new HashMap<Object,String>();    //主变
		 Map<Object,String> lines=new HashMap<Object,String>();        //线路
		 Map<Object,String> dks=new HashMap<Object,String>();          //电抗  
		 Map<Object,String> drs=new HashMap<Object,String>();          //电容
		
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inPara=new HashMap<String,Object>();
		Map<String,Object> outPara=new HashMap<String,Object>();
		
		
		
		PowerDevice temDev=null;
		List listDevs=new ArrayList();
		String equipID="";
		PowerDevice dev=null;
		
		//一、搜索缓存Map中设备连接的需要判定状态的设备
		
		for (Iterator iter = DevChangeStatus.keySet().iterator(); iter.hasNext();) {
			 equipID= iter.next().toString();
			 temDev=(PowerDevice)CBSystemConstants.getPowerDevice(stationID,equipID);
			 
			 
            //-----  缓存中的刀闸处理,先搜索刀闸能够影响的其他设备，再进行刀闸状态校核，最后将刀闸存入设备库中-------------------

			 if(temDev!=null&&temDev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
				    inPara.put("oprSrcDevice", temDev);
					inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
					inPara.put("isSearchDirectDevice", true);
					cs.execute(inPara, outPara);
					inPara.clear();
					listDevs = (ArrayList) outPara.get("linkedDeviceList");
					
					// 先搜索直连的开关
					if(listDevs!=null&&listDevs.size()>0){
						for (int i = 0; i < listDevs.size(); i++) {
							Switchs.put(listDevs.get(i), "0"); //初始状态0
						}
					}else{
						    //搜索电抗器和线路
							inPara.put("oprSrcDevice", temDev);
							inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.ElecShock);// 目标设备为电抗器，线路
							inPara.put("isSearchDirectDevice", true);
							cs.execute(inPara, outPara);
							inPara.clear();
							listDevs= (ArrayList) outPara.get("linkedDeviceList");
							if(listDevs!=null&&listDevs.size()>0){
								for (int i = 0; i < listDevs.size(); i++) {
									dev=(PowerDevice)listDevs.get(i); 
									if(SystemConstants.InOutLine.equals(dev.getDeviceType()))
										lines.put(dev, "0");
									if(SystemConstants.ElecShock.equals(dev.getDeviceType()))
										dks.put(dev, "0");
								}
							}
					}
					temDev.setDeviceStatus(DevChangeStatus.get(equipID));
					DevicePropertyDB.UpdateDeviceStatus(temDev, DevChangeStatus.get(equipID)); //刀闸状态可以直接插入数据库deviceequipinfo
			 }
			 
			 
            //------   缓存中的开关处理    ------------------
			 
			 else if(temDev!=null&&temDev.getDeviceType().equals(SystemConstants.Switch)){
				   Switchs.put(temDev, "0");
				   temDev.setDeviceStatus(DevChangeStatus.get(equipID));
			 }
			 
			 
             //-------   缓存中的接地倒处理  ----------------
			 
			 else if(temDev!=null&&temDev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
				    
				    inPara.put("oprSrcDevice", temDev);
					inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
					inPara.put("isSearchDirectDevice", true);
					cs.execute(inPara, outPara);
					inPara.clear();
					listDevs = (ArrayList) outPara.get("linkedDeviceList");
					// 先搜索直连的开关
					if(listDevs!=null&&listDevs.size()>0){
						for (int i = 0; i < listDevs.size(); i++) {
							Switchs.put(listDevs.get(i), "0"); //初始状态0
						}
					}
				 
				    inPara.put("oprSrcDevice", temDev);
					inPara.put("isSearchDirectDevice", true);
					inPara.put("excDevType", SystemConstants.SwitchSeparate);
					cs.execute(inPara, outPara);
					inPara.clear();
					listDevs = (ArrayList) outPara.get("linkedDeviceList");
					// 先搜索直连的开关
					if(listDevs!=null&&listDevs.size()>0){
							for (int i = 0; i < listDevs.size(); i++) {
								  dev=(PowerDevice)listDevs.get(i);
								
								  if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
									  lines.put(dev, "0");
								  } 
								  if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
									  maintrans.put(dev, "0");
								  } 
								  if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
									  monthlines.put(dev, "0");
								  } 
								  if(dev.getDeviceType().equals(SystemConstants.ElecShock)){
									  dks.put(dev, "0");
								  } 
								  if(dev.getDeviceType().equals(SystemConstants.ElecCapacity)){
									  drs.put(dev, "0");
								  } 
							}
					}
					temDev.setDeviceStatus(DevChangeStatus.get(equipID));
					DevicePropertyDB.UpdateDeviceStatus(temDev, DevChangeStatus.get(equipID)); //接地刀闸可以直接插入数据库deviceequipinfo
			 } 
		}
		
		
		
		// 二、开关连接的需要判定状态的设备
		for (Iterator iter = Switchs.keySet().iterator(); iter.hasNext();) {
			    temDev= (PowerDevice)iter.next();
			    inPara.put("oprSrcDevice", temDev);
				cs.execute(inPara, outPara);
				inPara.clear();
				List devs = (ArrayList) outPara.get("linkedDeviceList");
				if(devs!=null&&devs.size()>0){
						for (int i = 0; i < devs.size(); i++) {
								dev=(PowerDevice)devs.get(i);
			
								if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
									lines.put(dev, "0");
								} 
								if(dev.getDeviceType().equals(SystemConstants.PowerTransformer)){
									maintrans.put(dev, "0");
								} 
								if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
									monthlines.put(dev, "0");
								} 
								if(dev.getDeviceType().equals(SystemConstants.ElecShock)){
									dks.put(dev, "0");
								} 
								if(dev.getDeviceType().equals(SystemConstants.ElecCapacity)){
									drs.put(dev, "0");
								} 
						}
				}
		}
		
		
		
		// 三、 各种设备状态判定
		
		//开关
		SwitchStatus ssJudge=new SwitchStatus(DevChangeStatus);
		for (Iterator iter = Switchs.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  ssJudge.doJudge(temDev);
		}
		//线路
		 LineStatus lsJudge=new LineStatus();
		for (Iterator iter = lines.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  lsJudge.doJudge(temDev);
			  
		}
		//母线
		MotherLineStatus mlJudge=new MotherLineStatus();
		for (Iterator iter = monthlines.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  mlJudge.doJudge(temDev);
		}
		//主变
		MainTransStatus mtJudge=new MainTransStatus();
		for (Iterator iter = maintrans.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  mtJudge.doJudge(temDev);
		}
        //电抗
		ElecShockStatus esDodge=new ElecShockStatus();
		for (Iterator iter = dks.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  esDodge.doJudge(temDev);
		}
		//电容
		ElecCapacityStatus ecDodge=new ElecCapacityStatus();
		for (Iterator iter = drs.keySet().iterator(); iter.hasNext();) {
			  temDev= (PowerDevice)iter.next();
			  ecDodge.doJudge(temDev);
		}
		
		CBSystemConstants.DevcieStatusCache.remove(stationID);
	}

}

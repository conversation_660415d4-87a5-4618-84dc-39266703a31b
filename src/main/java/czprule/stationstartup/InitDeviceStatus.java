/**
 * 版权声明 : 泰豪软件股份有陝公坸版权所有
 * 项 目 组 ：擝作票专家系统
 * 功能说明 : SVG坘电站图形加载坎初始化设备状思
 * 作    者 : zhangyp
 * 开坑日期 : 2010-09-10
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.stationstartup;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;

import czprule.rule.operationclass.BreadthFirstSearchUtil;
import org.springframework.jdbc.BadSqlGrammarException;

import com.tellhow.czp.app.service.EMSService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.operationcard.dao.TicketDBManager;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.runmode.ExecuteDeviceStatus;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DBManager;

public class InitDeviceStatus implements StationStartup {

	public void StationLoad(String stationID) {
		// TODO 自动生戝方法存根
		if(stationID==null||"".equals(stationID))
			return;
        if(SystemConstants.isInitEMSStatus.equals("0")) { //加载厂站时丝自动坌步实时状思
//        	String sql = "SELECT COUNT(*) FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO A,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO B WHERE A.EQUIPID=B.EQUIP_ID AND A.DEVICESTATUS='-1' AND B.STATION_ID='"+stationID+"'";
    		//edit 2014.6.23
        	String sql = OPEService.getService().getIdrs(stationID);
    		boolean isExistNewEquip = DBManager.queryForInt(sql)==0?false:true;
    		if(isExistNewEquip) { //存在新设备，则針置站内所有设备状思
    			initStatus_CZP(stationID);
    		}
        }
	}
	
	/**
	 * 初始化设备状思，有EMS状思则坖EMS的，坦则根杮自定义方法初始化
	 * @param stationID
	 */
	public void initStatus(String stationID) {
		//if(!initStatus_EMS(stationID))
		initStatus_CZP(stationID);
	}
	
	/**
	 * 加载EMS实时状思并存库
	 * @param stationID
	 */
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public boolean initStatus_EMS(String stationID) {
		try {
//			long start=System.currentTimeMillis();
				
			Map statusMap =EMSService.getService().getMeasPoint(stationID);
			if(statusMap.size() == 0)
				return false;
				
			String equipid="";
			String status="";
			String curstatus="";
			List parameterList = new ArrayList();
			HashMap<PowerDevice,String> deviceStatusMap = new HashMap<PowerDevice,String>();
			for (Iterator iter = statusMap.entrySet().iterator(); iter.hasNext();) {
				Entry enry = (Entry)iter.next();
				if(enry.getValue() == null)
					continue;
				equipid = (String)enry.getKey();
				status = String.valueOf(enry.getValue());
				
				PowerDevice pd = CBSystemConstants.getPowerDevice(stationID,equipid);
				if(pd == null)
					continue;
				curstatus = pd.getDeviceStatus();
				if(!pd.getDeviceStatus().equals("-1"))
					curstatus = pd.getDeviceStatus().equals("0")?"0":"1";
//				if(status.equals(curstatus))
//					continue;
				pd.setDeviceStatus(status);
				parameterList.add(new Object[]{pd.getDeviceStatus(), pd.getPowerDeviceID()});
				deviceStatusMap.put(pd, status);
			}
			//设备状思构建
			if(deviceStatusMap.size() > 0) {
				if(!CBSystemConstants.roleCode.equals("1"))
					ExecuteDeviceStatus.execute(deviceStatusMap, false);
				HashMap<String, PowerDevice> mapDevice = CBSystemConstants.getMapPowerStationDevice().get(stationID);
				
				for(PowerDevice pd : mapDevice.values()) {
					parameterList.add(new Object[]{pd.getDeviceStatus(), pd.getPowerDeviceID()});
				}
				DevicePropertyDB.UpdateDeviceStatus(parameterList);
//				long end=System.currentTimeMillis();
//			    System.out.println("消耗的时间是:"+(end-start)+"ms");
			}
			
//			BZTOperateRule bztRule = new BZTOperateRule();
//			bztRule.execute(stationID);
//			TicketDBManager ticketDB = new TicketDBManager();
//			ticketDB.updateStateRecorCur("-1");
			
			CBSystemConstants.getDtdMap().clear();
			
			HashMap<String, PowerDevice> mapDevice = CBSystemConstants.getMapPowerStationDevice().get(stationID);
			
			if(mapDevice!=null){
				for(PowerDevice pd : mapDevice.values()) {
					
					if(pd.getDeviceStatus().equals("-1"))
						pd.setDeviceStatus("0");
				}
			}
			
			
			
			
		} catch (BadSqlGrammarException e) {
			e.printStackTrace();
		}
		return true;
	}
	
	/**
	 * 坖设备状思到缓存
	 * @param stationID
	 */
	public void initStatus_ToCache(String stationID) {
		if(CBSystemConstants.cardstatus.equals("0"))
			initStatus_CZPToCache(stationID);
		else if(CBSystemConstants.cardstatus.equals("1"))
			initStatus_EMSToCache(stationID);
		else if(CBSystemConstants.cardstatus.equals("2"))
			initStatus_CZPToCache(stationID);
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public void initStatus_EMSToCache(String stationID) {

//		initStatus_CZP(stationID);

		
		try {
			/*if(SystemConstants.AnotherSearch){
				EMSService.getService().freshenMeasPoint(BreadthFirstSearchUtil.getLineAndStationIds(stationID));
			}else{
				}*/
			Map statusMap = EMSService.getService().getMeasPoint(stationID);

			String equipid="";
			String status="";
			String curstatus="";
			HashMap<PowerDevice,String> deviceStatusMap = new HashMap<PowerDevice,String>();
			for (Iterator iter = statusMap.entrySet().iterator(); iter.hasNext();) {
				Entry enry = (Entry)iter.next();
				if(enry.getValue() == null)
					continue;
				equipid = (String)enry.getKey();
				status = String.valueOf(enry.getValue());

				PowerDevice pd =null;
				if(stationID.contains(","))
					pd = CBSystemConstants.getPowerDevice(equipid);
				else
					pd = CBSystemConstants.getPowerDevice(stationID,equipid);

				if(pd == null)
					continue;
				curstatus = pd.getDeviceStatus();
				if(!pd.getDeviceStatus().equals("-1"))
					curstatus = pd.getDeviceStatus().equals("0")?"0":"1";
				//			if(status.equals(curstatus))
				//				continue;
				pd.setDeviceStatus(status);
				deviceStatusMap.put(pd, status);
			}

			//设备状态构建
			if(deviceStatusMap.size() > 0) {
				ExecuteDeviceStatus.execute(deviceStatusMap, false);
			}
		}catch (BadSqlGrammarException e) {
			e.printStackTrace();
		}
		boolean isNormalMode = true;
		HashMap<String, PowerDevice> mapDevice = CBSystemConstants.getMapPowerStationDevice().get(stationID);
		if(mapDevice != null){
			for(PowerDevice pd : mapDevice.values()) {
				if(pd.getPowerDeviceDefaultSta().equals("") || !pd.getDeviceStatus().equals(pd.getPowerDeviceDefaultSta())) {
					isNormalMode = false;
					break;
				}
			}
		}
		
		
	}
	
	/**
	 * 坖模拟思到缓存
	 * @param stationID
	 */
	public void initStatus_CZPToCache(String stationID) {
		try {
		
		String cardstatues = CBSystemConstants.cardstatus;
		String sql="";
		if(cardstatues.equals("0")){
			sql = OPEService.getService().getIdrs0(stationID);
		}else if(cardstatues.equals("1")){
			sql = OPEService.getService().getIdrs1(stationID);
		}else{
			sql = OPEService.getService().getIdrs2(stationID);
		}

		List<Map> StatusList = DBManager.queryForList(sql);
			
		String equipid="";
		String status="";
		String curstatus="";
		HashMap<PowerDevice,String> deviceStatusMap = new HashMap<PowerDevice,String>();
		for (Iterator iter = StatusList.iterator(); iter.hasNext();) {
			Map map = (Map)iter.next();
			equipid = StringUtils.ObjToString(map.get("EQUIPID"));
			status = StringUtils.ObjToString(map.get("DEVICESTATUS"));
//			PowerDevice pd = CBSystemConstants.getPowerDevice(stationID,equipid);
			PowerDevice pd = null;
			if(stationID.contains(",")) {
    			String[] staarr = stationID.split(",");
				for(String sta : staarr) {
					pd = CBSystemConstants.getPowerDevice(sta,equipid);
					if(pd != null)
						break;
				}
			}
			else {
				pd = CBSystemConstants.getPowerDevice(stationID,equipid);
			}
			if(pd == null || status.equals("-1"))
				continue;
			curstatus = pd.getDeviceStatus();
			if(status.equals(curstatus))
				continue;
			pd.setDeviceStatus(status);
		}
		
		} catch (BadSqlGrammarException e) {
			e.printStackTrace();
		}
	}
	
	@SuppressWarnings({ "rawtypes", "unchecked" })
	public void initStatus_CZP(String stationID) { 
		String cardstatues = CBSystemConstants.cardstatus;
		String sql="";
		if(cardstatues.equals("0")){
//			sql="SELECT T.EQUIPID ,T.DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
			//edit 2014.6.23
			sql = OPEService.getService().getIdrs0(stationID);
		}else if(cardstatues.equals("1")){
//			sql="SELECT T.EQUIPID ,T.DISPATCH DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
			//edit 2014.6.23
			sql = OPEService.getService().getIdrs1(stationID);
		}else{
//			sql="SELECT T.EQUIPID ,T.MONITORING DEVICESTATUS FROM "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T,"+CBSystemConstants.opcardUser+"t_e_EQUIPINFO T1 WHERE T.EQUIPID=T1.EQUIP_ID  AND T1.STATION_ID='"+stationID+"'";
			//edit 2014.6.23
			sql = OPEService.getService().getIdrs2(stationID);
		}

		List EquipYLStatus = DBManager.queryForList(sql);
		Map status = new HashMap();
		PowerDevice pd;
		List parameterList = new ArrayList();
		HashMap<String,ArrayList<PowerDevice>> mxKnifeMap = new HashMap<String,ArrayList<PowerDevice>>();
		for (Iterator iter = EquipYLStatus.iterator(); iter.hasNext();) {
			status = (Map) iter.next();
			pd = (PowerDevice) CBSystemConstants.getPowerDevice(stationID, status.get("EQUIPID").toString());
			if (pd == null)
				continue;
			
//zyp:暂时没用		if(status.containsKey("LOADELECSTATUS") && status.get("LOADELECSTATUS")!=null)
//				pd.setIsLoseElec(status.get("LOADELECSTATUS").toString());
		
			if(pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) { //接地刀闸拉开
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)) {
					List<PowerDevice> tfList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.PowerTransformer);
					if(tfList.size() > 0) {
						PowerDevice tf = tfList.get(0);
						if(RuleExeUtil.isTransformerBL(tf)) { //主坘并列违行
							if(StringUtils.getSwitchCode(tf.getPowerDeviceName()).equals("1"))
								pd.setDeviceStatus("0");
							else
								pd.setDeviceStatus("1");
						}
						else { //主坘非并列违行
							if(pd.getPowerVoltGrade() >= 220)
								pd.setDeviceStatus("1");
							else
								pd.setDeviceStatus("0");
						}
					}
					else
						pd.setDeviceStatus("1");
				}
				else
					pd.setDeviceStatus("1");
			}
			else if(pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)) {
					CommonSearch cs=new CommonSearch();
					Map<String, Object> inPara = new HashMap<String, Object>();
					Map<String, Object> outPara = new HashMap<String, Object>();
					inPara.put("oprSrcDevice", pd);
			        inPara.put("tagDevType", SystemConstants.Switch);
			        inPara.put("isSearchDirectDevice", true);
			        cs.execute(inPara, outPara);
					List switchList = (ArrayList) outPara.get("linkedDeviceList");
					if(switchList.size() == 0)
						pd.setDeviceStatus("0");
					else {
						PowerDevice mxSwitch = (PowerDevice)switchList.get(0);
						if(mxSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) ||
								mxSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL))
							pd.setDeviceStatus("0"); //毝蝔开关旝边的刀闸坈上
						else if(mxSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL))
							pd.setDeviceStatus("1"); //旝路开关旝边的刀闸拉开
						else {
							String mxSwitchName = mxSwitch.getPowerDeviceName();
							if(!mxKnifeMap.containsKey(mxSwitchName))
								mxKnifeMap.put(mxSwitchName, new ArrayList());
							mxKnifeMap.get(mxSwitchName).add(pd); //其他刀闸的状思坎面冝设置
							continue;
						}
					}
				}
				else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL))
					pd.setDeviceStatus("1"); //旝路刀闸拉开
				else
					pd.setDeviceStatus("0"); //其他刀闸坈上
			}
			else if(pd.getDeviceType().equals(SystemConstants.Switch)) {
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL))
					pd.setDeviceStatus("2"); //旝路开关冷备用
				else if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML) || 
						pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)) {
					List<PowerDevice> mlList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer, true, true, true);
					if(mlList.size() > 0 && mlList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						pd.setDeviceStatus("0"); //坌毝接线的毝蝔开关违行
					else { //坕毝接线的毝蝔开关
						pd.setDeviceStatus("1"); //毝蝔开关热备用
					}
				}
				else
					pd.setDeviceStatus("0"); //其他开关违行
			}
			else if(pd.getDeviceType().equals(SystemConstants.MotherLine)) {
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					pd.setDeviceStatus("2"); //旝路毝线冷备用
				else
					pd.setDeviceStatus("0");
			}
			else
				pd.setDeviceStatus("0"); //其他设备违行
			parameterList.add(new Object[]{pd.getDeviceStatus(), pd.getPowerDeviceID()});
		}
		//编坷尝的刀闸坈上，编坷大的刀闸拉开
		if(mxKnifeMap.size() > 0) {
			for (Iterator iter = mxKnifeMap.values().iterator(); iter.hasNext();) {
				ArrayList<PowerDevice> knifeList = (ArrayList<PowerDevice>)iter.next();
				if(knifeList.size() == 2) {
					String n0 = StringUtils.getSwitchCode(knifeList.get(0).getPowerDeviceName());
					String n1 = StringUtils.getSwitchCode(knifeList.get(1).getPowerDeviceName());
					if(n0.equals("") || n1.equals("")) {
						knifeList.get(0).setDeviceStatus("0");
						knifeList.get(1).setDeviceStatus("1");
					}
					else if(Integer.valueOf(n0.charAt(n0.length()-1)) > Integer.valueOf(n1.charAt(n1.length()-1))) {
						knifeList.get(0).setDeviceStatus("1");
						knifeList.get(1).setDeviceStatus("0");
					}
					else {
						knifeList.get(0).setDeviceStatus("0");
						knifeList.get(1).setDeviceStatus("1");
					}
				}
				else {
					for(PowerDevice knife : knifeList) {
						knife.setDeviceStatus("0");
					}
				}
				for(PowerDevice knife : knifeList) {
					parameterList.add(new Object[]{knife.getDeviceStatus(), knife.getPowerDeviceID()});
				}
			}
		}
		if(parameterList.size() > 0)
			DevicePropertyDB.UpdateDeviceStatus(parameterList);
	}
}



package czprule.datemodule;

import java.awt.Color;
import java.awt.Component;
import java.awt.Graphics;

import javax.swing.border.AbstractBorder;

/**
 * <p>Title: OpenSwing</p>
 * <p>Description: TopBottomLineBorder只带上下两条线的边界Border</p>
 * <p>Copyright: Copyright (c) 2004</p>
 * <p>Company: </p>
 * <AUTHOR> href="mailto:<EMAIL>">SunKing</a>
 * @version 1.0
 */
public class TopBottomLineBorder extends AbstractBorder{
    /**
	 * 
	 */
	private static final long serialVersionUID = -7562076311488138556L;
	private Color lineColor;
    public TopBottomLineBorder(Color color){
        lineColor = color;
    }

    public void paintBorder(Component c, Graphics g, int x, int y,
                            int width, int height){
        g.setColor(lineColor);
        g.drawLine(0, 0, c.getWidth(), 0);
        g.drawLine(0, c.getHeight() - 1, c.getWidth(),
                   c.getHeight() - 1);
    }
}

package czprule.system;

import czprule.model.PowerDevice;

import java.util.ArrayList;
import java.util.HashMap;


public interface GraphDataService {
	public HashMap<String, HashMap<String, PowerDevice>>  queryLineOrStationDevices(String ids);
	
	public HashMap<String,HashMap<String,ArrayList<String>>>  queryToplogyDevicePoint(String ids);
	
	public HashMap<String,HashMap<String,ArrayList<String>>>  queryToplogyPointDevice(String ids);
}

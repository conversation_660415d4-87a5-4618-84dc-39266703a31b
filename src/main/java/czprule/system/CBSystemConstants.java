package czprule.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Map.Entry;
import java.util.Set;
import java.util.concurrent.CopyOnWriteArrayList;

import javax.swing.tree.TreePath;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.czp.sysconfig.SvgP;
import com.tellhow.czp.user.User;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CheckMessage;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.DeviceStateModel;
import czprule.model.DeviceStatusModel;
import czprule.model.DictionarysModel;
import czprule.model.ProtectiveBoard;
import czprule.model.SkStation;
import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;

public class CBSystemConstants {
	/***************** 设备运行状态 ***********/
	public static final String RunTypeSwitchML  = "monthlinelinkswitch"; // 母联开关
	public static final String RunTypeSwitchPL = "sidelineswitch"; // 旁路开关
	public static final String RunTypeSwitchMLPL = "monthlineorsideswitch"; // 母联兼旁路开关
	public static final String RunTypeSwitchXL = "lineswitch"; // 线路开关
	public static final String RunTypeSwitchDYC = "hightransswitch"; // 电源侧主变开关
	public static final String RunTypeSwitchFHC = "lowtransswitch"; // 负荷侧主变开关
	public static final String RunTypeSwitchQT = "otherswitch"; // 其他开关
	
	public static final String RunTypeSwitchDR = "dianrongswitch"; // 电容开关
	public static final String RunTypeSwitchDK = "diankangswitch"; // 电抗开关
	public static final String RunTypeSwitchJDB = "jiedibianswitch"; // 接地变开关
	public static final String RunTypeSwitchZYB = "zhanyongbianswitch"; //站用变开关
	public static final String RunTypeSwitchDJ = "dianjiswitch"; //电机开关
	public static final String RunTypeSwitchXHXQ = "xiaohuswitch"; //消弧线圈开关
	
	public static final String RunTypeKnifeXL = "lineknife"; // 线路刀闸
	public static final String RunTypeKnifeZB = "transknife"; // 主变刀闸
	public static final String RunTypeKnifeML = "monthlinelinkknife"; // 母联刀闸
	public static final String RunTypeKnifePL = "sidelineknife"; // 旁路刀闸
	public static final String RunTypeKnifeXLS = "lineknifeforswtich"; // 线路刀闸(代替线路开关)
	public static final String RunTypeKnifeZBS = "transknifeforswtich"; // 主变刀闸（代替主变开关）
	public static final String RunTypeKnifeMX = "monthlineknife"; // 母线刀闸
	public static final String RunTypeKnifeDY = "powerknife"; // 母线对侧刀闸
	public static final String RunTypeKnifeZBC = "transsideknife"; // 主变侧刀闸
	public static final String RunTypeKnifePT = "ptknife"; // PT刀闸
	public static final String RunTypeKnifeBLQ = "arresterknife"; // 避雷器刀闸
	public static final String RunTypeKnifeGYDKQ = "reactorknife"; // 高压电抗器刀闸
	public static final String RunTypeKnifeRB = "rbknife"; // 融冰刀闸
	public static final String RunTypeKnifeQT = "otherknife"; // 其它刀闸
	public static final String RunTypeSideMother = "sidemotherline"; // 旁路母线
	public static final String RunTypeGroundZXDDD="neutralgroundknife";//中性点地刀
	public static final String RunTypeGroundKnife="groundknife";//普通接地刀闸
	public static final String DeviceUnset = "unset"; // 未设置
	/****************** 设备子类 *******************/
	public static final String KindKnifeXC = "xcknife"; // 小车刀闸
	/****************** 设备运行状态 *******************/
	/****************** 设备接线方式 *******************/
	public static final String RunModelThreeTwo = "runmodelthreetwo"; // 3/2接线方式
	public static final String RunModelFourThree = "runmodelfourthree";// 4/3接线方式
	public static final String RunModelOneMotherLine = "runmodelonemotherline"; // 单母接线方式
	public static final String RunModelDoubleMotherLine = "runmodeldoublemotherline"; // 双母接线方式
	public static final String RunModelFourCornerLine = "runmodelfourcornerline"; // 四角接线方式
	public static final String RunModelBridgeLine = "runmodelbridgeline"; //桥形接线
	public static final String RunModelOneLine = "runmodeloneline"; //单端电源线路
	public static final String RunModelDoubleLine = "runmodeldoubleline"; //双端电源线路
	public static final String RunModelCableLine = "runmodelcableline"; //电缆线路
	/****************** 设备接线方式 *******************/
	
	public static boolean reverse = false;//反向开票
	public static boolean reverseCancel = false;//反向开票取消，点击取消
	public static String startCode = "";//开始状态
	public static boolean isMultiTicket = false;//多张票开票
	
	public static SVGAddDeviceInf svgAddPd = null;// 点击SVG图形添加设备集合
	public static String currentInitConfig = "";// 当前选择的初始化类型
	public static final String INITDEVICESTATUS = "initDeviceStatus"; // 初始化设备状态按钮

	public static boolean openCheck=false;//开启校核
	public static boolean isRealTime=false;//是否实时校核
	public static boolean isCurrentSys=true; //是否由操作票系统启动
	public static Boolean isLock = true; // 设备规则操作是否闭锁 默认true
	public static Boolean isOutCard = true; // 是否生成术语
	public static Boolean isShowLineWide = true; // 线路操作是否显示变电站解合环选择
	public static String isAutoLoadSVGFile = "1"; // 登录是否自动下载SVG图形
	public static Boolean isvalidateMAC = false; // 登录是否验证MAC地址
	//public static Boolean isUseEmsColor=false; //是否使用ems的颜色
	public static Boolean isUseSysColor=true;//是否使用系统自身颜色渲染方案
	public static Boolean isFlowShow=false;//是否显示厂站潮流
	public static Boolean isInversion = false; // 是否生成术语
	public static Boolean isOpenMessages = true; // 操作过程是否弹出提示
	public static Boolean isOffline = false; // 是否离线模式
	public static Boolean isHrefDoubleClick = false; // 是否双击打开图上链接
	public static Boolean isGroundDisconnectorExist = false; // 是否存在接地刀闸
	public static Boolean isMaxRangeOffTicket = false; // 是否最大范围开票
	public static Boolean isParallelRun = false; // 是否并列运行
	public static Boolean isSame = false; // 是否同设备点图开票
	public static String cardflag = "";   //0：综令票  1：逐项票  值为空的智能区别操作票类型
	public static String cardstatus="1"; //拟票状态0：调度拟票态1：实时态：2监控拟票态
	public static String apptype = "0"; // 业务类型 0:操作票1：监控票
	public static String cardtype = "0"; // 默认开状态令 0:状态令1：元件令
	public static String cardbuildtype = "0"; // 开票方式 0：正常票 1：点图成票
	public static Boolean stateOfTheDrawer = false; 
	public static String pdStart = null;
	public static DeviceMenuModel stateCode = null;
	public static  Map<PowerDevice, PowerDevice> powerDeviceDevMap  = new HashMap<PowerDevice, PowerDevice>();
	public static  List<PowerDevice> zxdOffList  = new ArrayList<PowerDevice>();//中性点地刀拉开操作缓存（广州要求主变操作完成后拉中性点地刀）
	public static String unitCode = "0"; // 区域编码 默认无则为0
	public static String czdwId = ""; //指令关联操作单位id,默认无则为0
	public static String roleCode = ""; // 业务类型 0:主网调度1：配网调度
	public static String opCode = ""; //当前用户的操作码，用于区分操作票
	public static String opRuleCode = ""; //当前用户的规则术语操作码，用于区分规则、术语。对于地调用户，opCode一定各不相同，但opRuleCode可能和省调全是一样的
	public static String projectID = "czp"; // 系统ID
	public static String defaultUser = ""; // 默认用户
	public static String rememberPassword = ""; //记住密码
	public static String password = ""; //明文密码
	public static Map  passWordMap = new HashMap();  //记住密码
	public static String FLOW_NUM = ""; // 操作票票号
	public static final String SYSTEM_TITLE = "图形化智能操作票系统";
	public static final String SYSTEM_VERSION = "- V2.0";
	public static final String SENDTRAN = "S";
	public static final String LOADTRAN = "T";
	public static final String SYS_CONFIG_XML_FILE = "config/SYSTEMPARAMSBUILD.xml";
	public static final String AUTH_CONFIG_XML_FILE = System.getProperty("user.dir")+ "/config/auth.txt";
	public static String omsurl = "";
	public static String czrw = "0"; // 状态成票操作任务生成
	public static String bzsx = ""; //
	public static String czlx = "";
	public static int openJK = 0;
	public static final String role_zw = "0"; //主网调度
	public static final String role_pw = "1";//配网调度
	public static final String role_jk = "2";//监控
	public static final String role_cz = "3";//厂站
	public static final String role_jc = "4";//基础权限
	public static String userid = "";
	public static boolean filterSomeSQL = false;//过滤部分SQL，如 GetDifferStatusDevices类里面DBManager.execute(OPEService.getService().getDsd2())清除数据，sql执行会影响效率
	public static boolean executeDeviceStatusNew =  false;//是否启用新的状态识别
	public static boolean jxDtd = true;//检修dtd
	public static boolean isjxSign = false;//是否检修设备挂检修牌
	public static boolean zttb = false;//状态同步
	public static boolean usePwRole = true; 
	public static boolean useOldRole = false; //是否使用旧规则（就规则为：未配置状态转换接线方式的，默认出单母接线方式规则）
	//数据库SQL全大写
	public static boolean sqlUpperCase = false;//数据库SQL是否大写
	//设备防误信息
	public static List<CheckMessage> lcm = new ArrayList<CheckMessage>();
	//判断是否处于校核状态 0 非校核 1校核
	public static int jh_tai = 0;
	//web端校核
	public static boolean jh_qd = false;
	//用户爱好
	public static Boolean isHrefOnLine = false; // 是否双击线路打开对侧厂站图
	//成票是否全屏展示
	public static Boolean isTicketFullScreen =false;
	//EQ表达式同类设备分行出术语（默认不分开，用、分隔）
	public static Boolean splitEQ=false;
	public static Boolean addXH=false;//  操作指令替换类\r\n是否需要增加项号

	//多选成票开多张票模式
	public static boolean samepdMultipleTicket = false;
	
	//一键成票功能常用参数
	public static Boolean oneClickOpenerTicket = false; // 一建成票
	public static String oneClickString = "";//一键成票传入参数，1、开票用户 2、开票票号
	public static String oneClickReturn = "";//返回结果
	public static String oneClickRoleType = "";//调度类型
	
	//浙江金华变电站成票
	public static Boolean bdzTicket = false; 

	//安徽监控成票(开关自动操作:解环、合环、拉空线路)变量
	public static String operationType = "";//操作类型
	public static Map<String,String>  mapPara = new HashMap<String, String>();
	public static Map<String,String>  pjzMap = new HashMap<String, String>();
	//武汉母联开关状态提示（涉及开关倒母、母线更改供电方式，母线恢复运行方式等操作，如果母联开关不在运行状态，提示XX母联开关在XX状态，是否短时合上XX开关，选是，则注意事项第一条出相应指令，如：1、短时合上110kV墨118母联开关）
	public static PowerDevice mlSwitchtips = null;
	
	//设置数据库用户
	public static String equipUser = "equip.";
	public static String emsetlUser = "emsetl.";
	public static String platformUser = "platform.";
	public static String opcardUser = "opcard.";
	public static String deviceEquipUser = "opcard.";
	public static String dxp = "platform.";
	public static double tempticketSpit = 0;//0的话不处理，否则右边成票界面比例为（1-tempticketSpit）
	//术语(负)是否按线路名称顺序出指令
	public static boolean isLoadLineSort =false;
	
	//是否关闭校核窗口后关闭成票界面，默认是关闭的
	public static boolean isCheckCloseWindow =true;
	
	//是否启用拓扑着色
	public static boolean isSetToplogySVGColor =false;
	
	//多选成票是否启用设备状态的限制
	public static boolean isLimitStateMultipleTicket = true;
	
	//是否打开厂站时初始化连接关系
	public static boolean isOpeDeviceTypeInit =false;
	public static boolean isProcessSwitch = true;	
	public static boolean isSpecialMenu = false;

	public static String curLine = ""; //当前线路

	public static String username = ""; //校核服务传入的用户名

	public static boolean isCheckTelemetry = false;	//是否校核遥测 --海南
	public static boolean isSimulation = false;	//判断是否仿真发令 --海南

//	public static String cardkind = "";//操作票管理界面中查询的数据，开票类型，宗令票和逐项票
	
//	public static String getCardkind() {
//		return cardkind;
//	}
//	public static void setCardkind(String cardkind) {
//		CBSystemConstants.cardkind = cardkind;
//	}

	/****************** 系统缓存 *******************/
	public static List<String> bztMLOperatedList = new ArrayList<String>();
	public static List<String> bztRelationOperatedList = new ArrayList<String>();//备自投跳闸关系母联操作缓存
	public static Map<String,Integer> bztOrganRecord=new HashMap<String,Integer>();//备自投原本跳投关系状态缓存
	public static Map<String,Integer> bztStateRecord =  new HashMap<String, Integer>();//备自投跳闸关系更改状态缓存
	public static Map<String,Integer> bztRelationRecord =new HashMap<String, Integer>();//备自投跳投关系缓存
	//多选开票设备集合
	public static List<PowerDevice> samepdlist= new ArrayList<PowerDevice>();
	//传入操作
	public static List<RuleBaseMode> rbmList = new ArrayList<RuleBaseMode>();//传入调度令的操作对象（用于监控）
	public static String jxpID = ""; //
	public static String ylflag = ""; //
	public static String jxpnr = ""; //
	public static String ddzl = ""; //传入调度令（用于监控）
	public static String ddzlDW = ""; //传入调度令单位（用于监控）
	public static String ddzlID = ""; //传入调度令ID（用于监控）
	public static String ddpID = ""; //传入调度票ID（用于监控）
	public static String qybm = ""; //传入OMS区域编码
	public static String qymc = ""; //传入OMS区域名称
	public static String appConfig = ""; //AppConfig文件名
	public static String ydczsj ="";//预定操作时间
	public static String RealTimeService = ""; //实时状态服务地址
	public static String wlflAddress = ""; //网络发令服务地址
	public static Map<PowerDevice,String[]> tagStatusMap =new LinkedHashMap<PowerDevice,String[]>();

	//河南储存220kV线路转检修监控（供）第一条organ_name，用于第二条是否重复
	public static String firstJXZYXorgan = "";//变电站所属监控名称
	public static String isCXHCZ ="0";//0:常规操作；1：程序化操作；2：EMS程序化操作（一键顺控）；3：OMS程序化操作(单步遥控)
	public static String[] KGHHYX = {"",""};//河南电厂合环运行开关
	public static boolean iszxl = false;//逐项令成票（武汉）
	public static boolean isBlankModel = false;//点图成票空白模板（武汉）

	public static List<PowerDevice> stationMLswitchList = new ArrayList<PowerDevice>();//厂站母联开关集合
	
	public static List<String> checkList = new CopyOnWriteArrayList<String>();
	
	public static  Map<PowerDevice,String> tagStatusBeginMap = new HashMap<PowerDevice, String>();
	
	public static Map<RuleBaseMode, String> dcchzMap =new LinkedHashMap<RuleBaseMode, String>();//储存对侧线路操作重合闸许可票
	
	public static Map<RuleBaseMode, String> multipleTicketMap =new LinkedHashMap<RuleBaseMode, String>();
	
	/*
	 * 广州配网校核传入设备状态
	 */
	public static Map<String,String>  deviceStatusmap = new HashMap<String, String>();

	/*
	 * 挂牌信息
	 */
	public static Map<String, String> signDeviceMap = new HashMap<String, String>();
	public static boolean isChangeFH;
	public static long lastRealTimeDifference;

	public static  void setSampdlist(PowerDevice pd){
		samepdlist.add(pd);
	}
	public static List<PowerDevice> getSamepdlist(){
		return samepdlist;
	}
	public static void clearSamepdlist(){
		samepdlist.clear();
	}
	public static Map<PowerDevice, String> MXYXFSmap = new LinkedHashMap<PowerDevice, String>();//母线运行方式集合

	public static void setMXYXFSmap(Map<PowerDevice, String> map) {
		MXYXFSmap=map;
	}

	public static Map<PowerDevice, String> getMXYXFSmap() {
		return MXYXFSmap;
	}
	//存放当前线路树的选择
	private static TreePath simplePath=null;
	
	public static int sdkz = 0;	//送电空载：0非/1是
	public static int sdkz_gz = 0;	//(规则)送电空载：0非/1是
	public static void setTreePath(TreePath _simplePath){
		simplePath=_simplePath;
	}
	
	public static TreePath getTreePath(){
		return simplePath;
	}
	
	//所点树集合
	public static Map<String,SvgP> svgps=new HashMap<String,SvgP>();
	//是否销毁集合
	public static Map<String,Boolean> destrysvg=new HashMap<String, Boolean>();
	//运维队缓存
	public static Map<String,String>  yunweidui = new HashMap<String, String>();
	
	
	// 禁止MAC地址集合
	public static final List<String> macAddresss = new ArrayList<String>();
	// 存放当前登入用户信息
	private static User user = new User();

	public static void setUser(User _user) {
		user = _user;
	}

	public static User getUser() {
		return user;
	}

	// curOperateDevice当前操作设备对象 key 序号 vlaues:操作设备
	private static Map<Integer, PowerDevice> curOperateDevice = new HashMap<Integer, PowerDevice>();
	// transDevMap 本次右键操作对应的设备操作集合 key 序号 vlaues:设备操作记录对象
	private static Map<Integer, DispatchTransDevice> curDtdMap = new LinkedHashMap<Integer, DispatchTransDevice>();
	
	private static Map<Integer, DispatchTransDevice> chzDtdMap = new LinkedHashMap<Integer, DispatchTransDevice>();
	//缓存规则配置模型
	private static RuleBaseMode curRBM = new RuleBaseMode();
	private static List<RuleBaseMode> curRBMList = new ArrayList<RuleBaseMode>();

	public static void putDtdMap(DispatchTransDevice dtd) {
		int size = curDtdMap.size();
		int aa=1;
		if(dtd.getTransDevice().getPowerDeviceName().contains("7-1"))
			aa=2;
		curDtdMap.put(++size, dtd);
	}
	
	public static void putCHZDtdMap(DispatchTransDevice dtd) {
		int size = chzDtdMap.size();
		chzDtdMap.put(++size, dtd);
	}

	public static Map<Integer, DispatchTransDevice> getDtdMap() {
		return curDtdMap;
	}
	
	public static void setDtdMap(Map<Integer, DispatchTransDevice> map) {
		curDtdMap=map;
	}
	
	//当前操作
	public static RuleBaseMode getCurRBM() {
		return curRBM;
	}
	//当前操作名称
	public static String getCurRBMValue(){
		String code=curRBM.getStateCode();
		return DevicePropertyDB.getDeviceopName(code);
	}
	
	public static List<RuleBaseMode> getCurRBMList() {
		return curRBMList;
	}

	public static void setCurRBM(RuleBaseMode curRBM) {
		CBSystemConstants.curRBM = curRBM;
	}

	public static PowerDevice getParentDev() {
		int size = curOperateDevice.size();
		if (size == 1)
			return curOperateDevice.get(1);
		if (size > 1)
			return curOperateDevice.get(size - 1);
		return null;
	}
	
	public static PowerDevice getSourceDev() {
		int size = curOperateDevice.size();
		if (size >= 1)
			return curOperateDevice.get(1);
		else
			return null;
	}
	
	public static PowerDevice getPreDev() {
		int size = curOperateDevice.size();
		if (size >= 2)
			return curOperateDevice.get(size-1);
		else
			return null;
	}

	public static void putCurOperateDev(PowerDevice pd) {
		int size = curOperateDevice.size();
		curOperateDevice.put(++size, pd);
	}

	public static Map<Integer, PowerDevice> getCurOperateDevs() {
		return curOperateDevice;
	}
	
	public static void setCurOperateDevs(Map<Integer, PowerDevice> devices){
		curOperateDevice=devices;
	}
	

	//线路进出线状态缓存
	private static HashMap<String, String> lineFlowInfo=new HashMap<String, String>();
	// 设备运行状态关系集合 String:DictionarysModel.name
	private static List<DictionarysModel> Dictionarys = new ArrayList<DictionarysModel>();
	// 设备通用状态
	public static Map<String, String> DeviceStateModel = new HashMap<String, String>();
	// 设备电压字典
	public static Map<String, String> VoltMap=new HashMap<String, String>();
	// 设备状态表
	public static Map<String, Map<String, DeviceStatusModel>> DeviceStatusModel = new HashMap<String, Map<String, DeviceStatusModel>> ();
	// 设备遥信库与操作票系统设备状态库差异设备缓存
	public static Map<String, Map<String, String>> DevcieStatusCache = new HashMap<String, Map<String, String>>();
	// 线路电源侧缓存
	public static Map<String, PowerDevice> LineSource = new HashMap<String, PowerDevice>();
	// 线路负荷侧缓存
	public static Map<String, List<PowerDevice>> LineLoad = new HashMap<String, List<PowerDevice>>();
	// 线路送电空载状态缓存
	public static Map<PowerDevice, String> LineTagStatus = new LinkedHashMap<PowerDevice, String>();
	// 线路接带缓存
	public static List<PowerDevice> LineTransform = new ArrayList<PowerDevice>();
	// 保护缓存
	static final private HashMap<String, HashMap<String, PowerDevice>> mapPowerStationProtect = new HashMap<String, HashMap<String, PowerDevice>>();
	// 压板缓存
	static final private HashMap<String, HashMap<String, ProtectiveBoard>> mapPowerStationProtectiveBoard = new HashMap<String, HashMap<String, ProtectiveBoard>>();
	// 临时设备缓存
	private static HashMap<String, ArrayList<PowerDevice>> RMDevice = new HashMap<String, ArrayList<PowerDevice>>();
	// 状态发生改变的临时设备缓存
	//private static HashMap<String, HashMap<PowerDevice,String>> ActiveRMDevice=new HashMap<String, HashMap<PowerDevice,String>>();
	private static String importInfo; //存放OMS操作票ID
	//EMS与OMS厂站名称对应,传入EMS厂站全称，获取OMS厂站名称
	public static Map<String, String> omsStationMap = new HashMap<String, String>();

	//EMS与顺控厂站名称对应,传入EMS厂站全称，获取对应顺控厂站名称
	public static Map<String, SkStation> skStationMap = new HashMap<String, SkStation>();
	public static String approval = "";
	public static String restoration = "";
	public static List<Map<String, String>> globalMainDevList = new ArrayList<Map<String,String>>();
	public static List<Map<String, String>> globalSlaveDevList = new ArrayList<Map<String,String>>();
	public static boolean isztcp = false;
	public static String pwOperate = "";
	public static boolean continueTicketResult = false;
	public static boolean isContinueTicket = false;
	
	
	
	public static void putLineSource(String lineID, PowerDevice line) {
		LineSource.put(lineID, line);
	}

	public static void putLineLoad(String lineID, List<PowerDevice> list) {
		LineLoad.put(lineID, list);
	}
	public static void clearLineSourceAndLoad(){
		LineLoad.clear();
		LineSource.clear();
	}

	public static void putLineTagStatus(PowerDevice equip, String tagStatus) {
		LineTagStatus.put(equip, tagStatus);
	}
	
	public static String getLineTagStatus(PowerDevice equip) {
		if(LineTagStatus.containsKey(equip))
			return LineTagStatus.get(equip);
		else 
			return "-1";
	}

	public static HashMap<String,String> getLineFlowInfo(){
		return lineFlowInfo;
	}
	
	public static  void setLineFlowInfo(HashMap<String,String> flowinfo){
		lineFlowInfo=flowinfo;
	}
	
	public static String getDictionaryName(String code, String codetype) {
		DictionarysModel dm = null;
		String returnName = "";
		for (int i = 0; i < Dictionarys.size(); i++) {
			dm = Dictionarys.get(i);
			if (dm.getCode().equals(code) && dm.getCodetype().equals(codetype))
				returnName = dm.getName();
		}
		return returnName;
	}

	public static String getDictionaryName(String code) {
		DictionarysModel dm = null;
		String returnName = "";
		for (int i = 0; i < Dictionarys.size(); i++) {
			dm = Dictionarys.get(i);
			if (dm.getCode().equals(code))
				returnName = dm.getName();
		}
		return returnName;
	}

	public static List<DictionarysModel> getDictionary(String codetype) {
		List<DictionarysModel> dictionarys = new ArrayList<DictionarysModel>();
		DictionarysModel dm = null;
		for (int i = 0; i < Dictionarys.size(); i++) {
			dm = Dictionarys.get(i);
			if (dm.getCodetype().equals(codetype))
				dictionarys.add(dm);
		}
		return dictionarys;
	}

	public static void addDictionary(DictionarysModel dm) {
		Dictionarys.add(dm);
	}

	public static String getDeviceStateValue(String stateCode) {// 根据执行动作返回状态值
		return stateCode;
	}

	public static String getDeviceStateName(String stateCode) { // 返回设备执行动作中文名称
		String stateName = DeviceStateModel.get(stateCode);
		if (stateName == null)
			return "";
		return stateName;
	}
	
	public static String getDeviceStateName(String devType, String stateCode) { // 返回设备执行动作中文名称
		String stateName = "";
		if(devType.equals(SystemConstants.SwitchSeparate) || (devType.equals(SystemConstants.SwitchFlowGroundLine)))
			stateName =  DeviceStateModel.get(stateCode);
		else if(devType.equals(SystemConstants.Switch)) {
			if(stateCode.equals("0") || stateCode.equals("1"))
				stateName =  DeviceStateModel.get(stateCode);
			else
				stateName = DeviceStateModel.get(stateCode);
		}
		else if(devType.equals(SystemConstants.BztDevice) || devType.equals(SystemConstants.BztRelate)) {
			stateName = stateCode.equals("0")?"投入":"退出";
		}
		
		else
			stateName = DeviceStateModel.get(stateCode);
		if (stateName == null)
			stateName = "";
		return stateName;
	}
	
	
	

	public static String getDeviceStatusName(String devType, String stateCode) {// 返回状态对应的中文名称
		String StatusName = "";
		if(!DeviceStatusModel.containsKey(devType))
			return StatusName;
		DeviceStatusModel dsm = DeviceStatusModel.get(devType).get(stateCode);
		if(dsm == null)
			StatusName = "";
		else if (StringUtils.compareStr(devType, SystemConstants.SwitchSeparate
				+ "," + SystemConstants.SwitchFlowGroundLine) == 0) {
			StatusName = dsm.getStateNameSwitch();
		} 
		else {
			StatusName = dsm.getStateName();
		}
		
		return StatusName;
	}

	// 机构集合{机构ID,机构对象}
	static final private HashMap<String, PowerDevice> mapPowerOrgan = new HashMap<String, PowerDevice>();
	// 变电站集合{变电站ID,变电站对象}
	static final private HashMap<String, PowerDevice> mapPowerStation = new HashMap<String, PowerDevice>();
	// 全网线路集合{线路ID,线路对象}
	static final private HashMap<String, PowerDevice> mapPowerLine = new HashMap<String, PowerDevice>();
	// 全网线路集合{线路ID,线路对象}
		static final private HashMap<String, PowerDevice> mapPowerFeeder = new HashMap<String, PowerDevice>();
	// 存放当前所加载的变电站信息，该map为双层，第一层为("变电站ID",变电站设备集合Map),第二层为("电网设备ID",电网设备对象),当为单个变电站内部操作时使用该map记录设备数据
	static final private HashMap<String, HashMap<String, PowerDevice>> mapPowerStationDevice = new HashMap<String, HashMap<String, PowerDevice>>();

	//全网线路id和场站id
	static final private HashMap<String,ArrayList<String>> lineStationMap = new HashMap<String,ArrayList<String>>();
	//场站id和全网线路id
	static final private HashMap<String,ArrayList<String>> stationLineMap = new HashMap<String,ArrayList<String>>();
	//全网线路id和站内线路id
	static final private HashMap<String,ArrayList<String>> lineEquipMap = new HashMap<String,ArrayList<String>>();
	//站内线路id和全网线路id
	static final private HashMap<String,ArrayList<String>> equipLineMap = new HashMap<String,ArrayList<String>>();
	
	
	public static HashMap<String, ArrayList<String>> getLinestationmap() {
		return lineStationMap;
	}

	public static HashMap<String, ArrayList<String>> getStationlinemap() {
		return stationLineMap;
	}

	public static HashMap<String, ArrayList<String>> getLineequipmap() {
		return lineEquipMap;
	}

	public static HashMap<String, ArrayList<String>> getEquiplinemap() {
		return equipLineMap;
	}

	public static HashMap<String, PowerDevice> getMapPowerOrgan() {
		return mapPowerOrgan;
	}
	
	public static HashMap<String, PowerDevice> getMapPowerStation() {
		return mapPowerStation;
	}

	public static PowerDevice getPowerStation(String powerStationID) {
		PowerDevice st = mapPowerStation.get(powerStationID);
		if(st == null)
			st = mapPowerFeeder.get(powerStationID);
		return st;
	}

	public static void putMapPowerStation(String powerStationID,
			PowerDevice station) {
		mapPowerStation.put(powerStationID, station);
	}

	public static HashMap<String, PowerDevice> getMapPowerLine() {
		return mapPowerLine;
	}

	public static PowerDevice getPowerLine(String lineCode) {
		return mapPowerLine.get(lineCode);
	}

	public static void putMapPowerLine(String lineCode, PowerDevice line) {
		mapPowerLine.put(lineCode, line);
	}
	
	public static HashMap<String, PowerDevice> getMapPowerFeeder() {
		return mapPowerFeeder;
	}

	public static PowerDevice getPowerFeeder(String lineCode) {
		return mapPowerFeeder.get(lineCode);
	}

	public static void putMapPowerFeeder(String lineCode, PowerDevice line) {
		mapPowerFeeder.put(lineCode, line);
	}

	public static Map<String,HashMap<String, PowerDevice>> getMapPowerStationDevice() {
		return mapPowerStationDevice;
	}

	public static HashMap<String, PowerDevice> getStationPowerDevices(
			String powerStationID) {
		HashMap<String, PowerDevice> stationDevs = mapPowerStationDevice
				.get(powerStationID);
		if (stationDevs != null) {
			return stationDevs;
		} else {
			return null;
		}
	}

	public static PowerDevice getPowerDevice(String powerStationID,String powerEquipID) {
		Map stationDevs = mapPowerStationDevice.get(powerStationID);
		if (stationDevs != null) {
			return (PowerDevice) stationDevs.get(powerEquipID);
		} else {
			return null;
		}
	}
	
	public static PowerDevice getPowerDeviceCache(String powerEquipID) {
		return CBSystemConstants.getPowerDevice(CBSystemConstants.getPowerDevice(powerEquipID).getPowerStationID(), powerEquipID);
	}
	//只获取设备，不关联厂站，//为全网图服务。zxs 14-06-12
	public static PowerDevice getPowerDevice(String powerEquipID) {
//		List<Map> list=DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_e_equipinfo e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerEquipID+"' ");
		
		HashMap<String, PowerDevice> linemap = CBSystemConstants.getMapPowerLine();
		for(PowerDevice line : linemap.values()) {
			if(line.getPowerDeviceID().equals(powerEquipID))
			return line;
		}
		
		HashMap<String, PowerDevice> stationmap = CBSystemConstants.getMapPowerStation();
		for(PowerDevice station : stationmap.values()) {
			if(mapPowerStationDevice.containsKey(station.getPowerDeviceID()) && mapPowerStationDevice.get(station.getPowerDeviceID()).containsKey(powerEquipID))
			return mapPowerStationDevice.get(station.getPowerDeviceID()).get(powerEquipID);
		}
		
		if(CBSystemConstants.roleCode.equals("1")){
			HashMap<String, PowerDevice> feedermap = CBSystemConstants.getMapPowerFeeder();
			for(PowerDevice line : feedermap.values()) {
				if(mapPowerStationDevice.containsKey(line.getPowerDeviceID()) && mapPowerStationDevice.get(line.getPowerDeviceID()).containsKey(powerEquipID))
				return mapPowerStationDevice.get(line.getPowerDeviceID()).get(powerEquipID);
			}
		}
		
		List<Map> list=DBManager.queryForList(OPEService.getService().getPowDevice(powerEquipID));
		if(list==null||list.size()==0)
			return null;
		Map map=list.get(0);
		PowerDevice pd=new PowerDevice();
		pd.setDeviceStatus(StringUtils.ObjToString(map.get("DEVICESTATUS")));
		if(VoltMap.get(StringUtils.ObjToString(map.get("VOLTAGE_ID")))!=null)
			pd.setPowerVoltGrade(Double.parseDouble(VoltMap.get(StringUtils.ObjToString(map.get("VOLTAGE_ID")))));
		pd.setPowerDeviceName(StringUtils.ObjToString(map.get("EQUIP_NAME")));
		pd.setOrgaId(StringUtils.ObjToString(map.get("ORGA_ID")));
		pd.setPowerDeviceID(powerEquipID);
		pd.setPowerStationID(StringUtils.ObjToString(map.get("STATION_ID")));
		pd.setDeviceType(StringUtils.ObjToString(map.get("EQUIPTYPE_FLAG")));
		if(StringUtils.ObjToString(map.get("ISPW")).equals("1")){
			pd.setPW(true);
		}else{
			pd.setPW(false);
		}
		
		return pd;
	}
	//根据设备id从数据库查找orgaid //为全网图服务 zxs 14-06-12
	public static String getOrgaID(String powerDeviceID) {
//		List<Map> list=DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_e_equipinfo e,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO s where e.equip_id =s.EQUIPID and e.equip_id='"+powerDeviceID+"' ");
		//edit 2014.6.30
		List<Map> list=DBManager.queryForList(OPEService.getService().getOrga(powerDeviceID));
		if(list==null||list.size()==0)
			return "";
		Map map=list.get(0);
		return StringUtils.ObjToString(map.get("orga_id"));
	}
	
	public static PowerDevice getPowerDeviceByCIMID(String powerStationID,String powerEquipCIMID) {
		Map stationDevs = mapPowerStationDevice.get(powerStationID);
		if (stationDevs != null) {
			Set<Entry<String, PowerDevice>> set=stationDevs.entrySet();
			 for (Iterator<Map.Entry<String, PowerDevice>> it = set.iterator(); it.hasNext();) {
				 Map.Entry<String, PowerDevice> entry = (Map.Entry<String, PowerDevice>) it.next();
				 if(entry.getValue().getCimID().equals(powerEquipCIMID))
					 return entry.getValue();
			 }
			return null;
		} else {
			return null;
		}
	}

	public static PowerDevice getPowerDeviceByName(String powerStationID,String powerDeviceName) {
		Map stationDevs = mapPowerStationDevice.get(powerStationID);
		if (stationDevs != null) {
			Set<Entry<String, PowerDevice>> set=stationDevs.entrySet();
			 for (Iterator<Map.Entry<String, PowerDevice>> it = set.iterator(); it.hasNext();) {
				 Map.Entry<String, PowerDevice> entry = (Map.Entry<String, PowerDevice>) it.next();
				 if(entry.getValue().getPowerDeviceName().equals(powerDeviceName))
					 return entry.getValue();
			 }
			return null;
		} else {
			return null;
		}
	}
	
	public static void putMapPowerStationDevices(String powerStationID,
			HashMap<String, PowerDevice> mapPowerDevice) {
		mapPowerStationDevice.put(powerStationID, mapPowerDevice);
	}
	public static void removeMapPowerStationDevices(String powerStationID) {
		mapPowerStationDevice.remove(powerStationID);
	}

	public static void putMapPowerStationProtects(String powerStationID,
			HashMap<String, PowerDevice> mapPowerDevice) {
		mapPowerStationProtect.put(powerStationID, mapPowerDevice);
	}
	//DriverPorpery类删除原缓存
	public static void delProtects(PowerDevice pd){
		HashMap<String, PowerDevice> before=mapPowerStationProtect.get(pd.getPowerStationID());
		List<String> deletepds=new ArrayList<String>();
		for(int i=0;i<before.size();i++){
			Set<Entry<String, PowerDevice>> set=before.entrySet();
			  for (Iterator<Map.Entry<String, PowerDevice>> it = set.iterator(); it.hasNext();) {
				  Map.Entry<String, PowerDevice> entry = (Map.Entry<String, PowerDevice>) it.next();
				  String key=entry.getKey();
				  PowerDevice bhpd= before.get(key);
				  if(bhpd.getDevice().equals(pd.getPowerDeviceID())){
					  deletepds.add(key);
//					  before.remove(key);
				  }
			  }
		}
		for(int j=0;j<deletepds.size();j++){
			before.remove(deletepds.get(j));
		}
	}
	//DriverPorpery类保存
	public static void putMaptoProtects(String powerStationID,HashMap<String, PowerDevice> mapPowerDevice){
		HashMap<String, PowerDevice> before=mapPowerStationProtect.get(powerStationID);
		for(int i=0;i<mapPowerDevice.size();i++){
			Set<Entry<String, PowerDevice>> set=mapPowerDevice.entrySet();
			  for (Iterator<Map.Entry<String, PowerDevice>> it = set.iterator(); it.hasNext();) {
				  Map.Entry<String, PowerDevice> entry = (Map.Entry<String, PowerDevice>) it.next();
				  String key=entry.getKey();
				  PowerDevice pd= before.get(key);
				  if(pd!=null){
//					  before.remove(key);
					  before.put(key, mapPowerDevice.get(key));
				  }else{
					  before.put(key, mapPowerDevice.get(key));
				  }
			  }
		}
		int n=0;
	}

	public static PowerDevice getProtect(String powerStationID,
			String powerEquipID) {
		Map stationDevs = mapPowerStationProtect.get(powerStationID);
		if (stationDevs != null) {
			if((PowerDevice) stationDevs.get(powerEquipID) == null){
				return new PowerDevice();
			}else{
				return (PowerDevice) stationDevs.get(powerEquipID);
			}
		} else {
			return new PowerDevice();
		}
	}

	public static  HashMap<String, PowerDevice> getProtect(String powerStationID) {
		HashMap<String, PowerDevice> stationDevs = mapPowerStationProtect.get(powerStationID);
		if (stationDevs == null) {
		    stationDevs=new HashMap<String, PowerDevice>();
		    mapPowerStationProtect.put(powerStationID, stationDevs);
		} 
		return stationDevs;
	}
	
	public static  HashMap<String, PowerDevice> getEquipProtect(String powerStationID, String powerEquipID) {
		HashMap<String, PowerDevice> stationDevs = mapPowerStationProtect.get(powerStationID);
		if (stationDevs == null) {
		    stationDevs=new HashMap<String, PowerDevice>();
		    mapPowerStationProtect.put(powerStationID, stationDevs);
		} 
		return stationDevs;
	}

	public static HashMap<String, ArrayList<PowerDevice>> getRMDevice() {
		return RMDevice;
	}
	
	// 二次设备术语缓存
	private static final List<String> twoDeviceList = new ArrayList<String>();
	public static void setTwoDevice(String word) {
		twoDeviceList.add(word);
	}
	public static List getTwoDevice() {
		return twoDeviceList;
	}

	// 设备编码缓存
	private static final Map<String, String> deviceTypeCodes = new HashMap<String, String>();
	public static final List<RuleBaseMode> chooseDeviceStatusPw = new ArrayList<RuleBaseMode>();
	public static final boolean isDevMultiLine = false;

	public static void putDeviceTypeCodes(String key, String value) {
		deviceTypeCodes.put(key, value);
	}

	public static String getDeviceTypeCode(String key) {
		return deviceTypeCodes.get(key);
	}



	public static String getImportInfo() {
		return importInfo;
	}

	public static void setImportInfo(String importInfo) {
		CBSystemConstants.importInfo = importInfo;
	}
	
	
	
	/**
	 * 根据厂站id 关联设备id 刀闸id 拆除类型获取可拆卸设备
	 * */
    public static PowerDevice getRMDeviceByDetailAndType(int type, String stationID, String knifeID, String deviceID){
    	if(stationID==null||deviceID==null||knifeID==null){
			return null;
		}
		ArrayList<PowerDevice> rms = RMDevice.get(stationID);
		if(rms==null){
			return null;
		}
		for (PowerDevice rm : rms) {
			if(rm.getRmType()==type){
				if(rm.getDevice().equals(deviceID)&&rm.getKnife().equals(knifeID)){
					return rm;
				}
			}
		}
		return null;
    }

    /**
	  * 创建时间 2013年11月23日 下午3:08:17
	  * 根据厂站id 设备id 获取关联的接地线
	  * <AUTHOR>
	  * @Title getGroundLineByStationAndDevice
	  * @param stationID
	  * @param deviceID
	  * @return
	  */
	public static List<PowerDevice> getGroundLineByStationAndDevice(
			String stationID, String deviceID) {
		List<PowerDevice> rms=RMDevice.get(stationID);
		ArrayList<PowerDevice> returns = new ArrayList<PowerDevice>();
		if(rms==null){
			return returns;
		}
		for (PowerDevice rm : rms) {
			if(rm.getDevice().equals(deviceID)&&rm.getRmType()==PowerDevice.GROUNDLINE){
				returns.add(rm);
			}
			
		}
		return returns;
	}
	 
    /**
     * 从两个缓存中查找临时设备
     * */
	public static PowerDevice getRMDeviceByDetailAndTypeFromAllRMCache(
			int rmType, String stID, String knID, String pdID) {
		PowerDevice rm = getRMDeviceByDetailAndType(rmType, stID, knID, pdID);
		/*if(rm==null){
			rm=getActiveRMByDetailAndType(pdID, stID, knID, rmType);
		}*/
		return rm;
	}

	public static List<PowerDevice> getlinkRMDevice(PowerDevice pd) {
		String stID=pd.getPowerStationID();
		String pdID=pd.getPowerDeviceID();
		List<PowerDevice> link=new ArrayList<PowerDevice>();
		ArrayList<PowerDevice> list = RMDevice.get(stID);
		for (PowerDevice rm : list) {
			if(rm.getDevice().equals(pdID)){
				link.add(rm);
			}
		}
		return link;
	}
	
	public static String getStatusField() {
		String statusField = "";
		if(cardstatus.equals("0"))
			statusField = "DEVICESTATUS";
		else if(cardstatus.equals("1"))
			statusField = "DISPATCH";
		else if(cardstatus.equals("2"))
			statusField = "MONITORING";
		return statusField;
	}
	
	
	public static Map<Integer, DispatchTransDevice> getChzDtdMap() {
		return chzDtdMap;
	}
	public static void setChzDtdMap(Map<Integer, DispatchTransDevice> chzDtdMap) {
		CBSystemConstants.chzDtdMap = chzDtdMap;
	}
	public static HashMap<String, HashMap<String, ProtectiveBoard>> getMapPowerStationProtectiveBoard() {
		return mapPowerStationProtectiveBoard;
	}
	
	public static void setMapPowerStationProtectiveBoard(String stationid,HashMap<String, ProtectiveBoard> map) {
		mapPowerStationProtectiveBoard.put(stationid, map);
	}
	
	/****************** 系统缓存 *******************/

	/*********** 操作任务面板中数据操作类型 **********************/

}

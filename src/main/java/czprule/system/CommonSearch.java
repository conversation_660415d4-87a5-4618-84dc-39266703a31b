package czprule.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.model.PowerDeviceListAndMapPaths;
import czprule.rule.operationclass.BreadthFirstSearchUtil;
import czprulepw.PWSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有陝公坸版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开坑日期: 2010-7-1 下坈03:51:27 
 */
@SuppressWarnings("unchecked")
public class CommonSearch{
    
	public static final String LINKEDDEVICELIST="linkedDeviceList";
	/**
	 * {@code HashMap<PowerDevice,ArrayList<PowerDevice>>} 
	 * */
	public static final String PATHLIST="pathList"; 
	public static final String ALLPATHLIST="allPathList"; 
	
	public static  int cou=0; 
	//public static  long a = System.currentTimeMillis();
	//public static long b = System.currentTimeMillis();
	private HashMap<String,PowerDevice> StationDeviceMap;
	private HashMap<String,ArrayList<String>> pointDeviceMap;
	private HashMap<String,ArrayList<String>> devicePointMap;
	private HashMap<String,String> busbarSectionPointList;
	private static HashMap<String,String> equipLineMap = new HashMap<String,String>();
	
	private HashMap<String,ArrayList<PowerDevice>> pathMap = new HashMap<String,ArrayList<PowerDevice>>();
	private List linkedDeviceList;
	private HashMap<PowerDevice,ArrayList<PowerDevice>> resultPath ;//最短通路集坈
	private HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>> resultAllPath  = new HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>(); //所有通路集坈
	
	private PowerDevice srcDevice = null;   //起始设备对象
	private PowerDevice tagDevice = null;   //目标设备对象
	private PowerDevice excDevice = null;   //排除设备对象
	private boolean isSearchDirectDevice = false; //是否只搜索直接连接设备
	private boolean isSearchOffPath = true; //是否搜索断开路径
	private boolean isStopOnBusbarSection = true; //是否遇到母线停止搜索
	private boolean isStopOnTagDevType = true; //遇到目标设备类型停止搜索
	private boolean isStopOnDiffVolt = false; //是否遇到非主变的不同电压等级设备停止搜索
	private boolean isPrintPath = false; //是否打印通路
	private boolean isIgnoreDzlinked = false; //是否忽略刀闸连通性，即如果是true则认为刀闸都是合上状态
	private int validPort = 0; //0:全部有效  1:1号端口有效  2:2号端口有效
	private String tagDevType = "";
	private String excDevType = "";
	private String tagDevRunType = "";
	private String excDevRunType = "";
	private String tagStatus = "";
	private List excDevList = new ArrayList(); //排除设备集合
	private boolean isStop = false;
	private int sameCount = 0;
	
	public void execute(Map inParaMap, Map outParaMap) {
		// TODO Auto-generated method stub
		if(SystemConstants.AnotherSearch){
			this.clearList();
			initParams(inParaMap);
			if(excDevice!=null){
				excDevList.add(excDevice);
			}
			PowerDeviceListAndMapPaths powerDeviceListAndMapPaths = BreadthFirstSearchUtil.getPowerDeviceListAndMapPaths(srcDevice,(ArrayList<PowerDevice>) excDevList , tagDevType, excDevType, tagDevRunType, excDevRunType, isSearchDirectDevice, isSearchOffPath, isStopOnBusbarSection, isStopOnTagDevType,isStopOnDiffVolt,validPort,isPrintPath);
			outParaMap.put("linkedDeviceList", powerDeviceListAndMapPaths.getPowerDeviceList()); //List
			outParaMap.put("pathList", powerDeviceListAndMapPaths.getMapPath());
		}else{
			this.clearList();
			linkedDeviceList = new ArrayList();
			resultPath  = new HashMap<PowerDevice,ArrayList<PowerDevice>>();
			initParams(inParaMap);
			if(srcDevice==null){
				return;
			}



//		StationDeviceMap = (HashMap<String, PowerDevice>) CBSystemConstants.getStationPowerDevices(srcDevice.getPowerStationID());
//		pointDeviceMap = SystemConstants.getMapToplogyPointDevice().get(srcDevice.getPowerStationID());
//		devicePointMap = SystemConstants.getMapToplogyDevicePoint().get(srcDevice.getPowerStationID());
			//CBSystemConstants.roleCode.equals("1")||
			if(CBSystemConstants.roleCode.equals("1")||CBSystemConstants.pjzMap.containsKey(srcDevice.getPowerStationID())) {

				StationDeviceMap = new HashMap<String,PowerDevice>();
				pointDeviceMap = new HashMap<String,ArrayList<String>>();
				devicePointMap = new HashMap<String,ArrayList<String>>();
				for(Iterator it = CBSystemConstants.getMapPowerStationDevice().keySet().iterator();it.hasNext();){
					String stid = (String)it.next();
					if(CBSystemConstants.getMapPowerStationDevice().get(stid).size() > 0) {

						StationDeviceMap.putAll(CBSystemConstants.getMapPowerStationDevice().get(stid));

						if(SystemConstants.getMapToplogyPointDevice().get(stid)!=null&&SystemConstants.getMapToplogyDevicePoint().get(stid)!=null){
							mergeMap(pointDeviceMap,SystemConstants.getMapToplogyPointDevice().get(stid));
							mergeMap(devicePointMap,SystemConstants.getMapToplogyDevicePoint().get(stid));
						}
					}
				}

			}
			else {
				StationDeviceMap = (HashMap<String, PowerDevice>) CBSystemConstants.getStationPowerDevices(srcDevice.getPowerStationID());
				if(StationDeviceMap == null) {
					CreatePowerStationToplogy.loadFacData(srcDevice.getPowerStationID());
					StationDeviceMap = (HashMap<String, PowerDevice>) CBSystemConstants.getStationPowerDevices(srcDevice.getPowerStationID());
				}
				if(StationDeviceMap==null){
					System.out.println("未获取到厂站"+srcDevice.getPowerStationID()+"的设备");
					return;
				}
				pointDeviceMap = SystemConstants.getMapToplogyPointDevice().get(srcDevice.getPowerStationID());
				devicePointMap = SystemConstants.getMapToplogyDevicePoint().get(srcDevice.getPowerStationID());
			}


		/*

		if(CBSystemConstants.roleCode.equals("1")) { //酝网覝由馈线ID找厂站ID，加载厂站设备
			if(srcDevice.isPW()) {
				String stID = "";
				if(equipLineMap.containsKey(srcDevice.getPowerStationID())) {
					stID = equipLineMap.get(srcDevice.getPowerStationID());
				}
				else {
					PowerDevice line = CBSystemConstants.getPowerDevice(srcDevice.getPowerStationID());
					if(line != null) {
		            	stID = line.getPowerStationID();
		            }
				}
				if(!stID.equals("")) {
	            	//StationDeviceMap.putAll(CBSystemConstants.getStationPowerDevices(stID));
	            	mergeMap(pointDeviceMap,SystemConstants.getMapToplogyPointDevice().get(stID));
	            	mergeMap(devicePointMap,SystemConstants.getMapToplogyDevicePoint().get(stID));
	            	equipLineMap.put(srcDevice.getPowerStationID(), stID);

	            }
			}
			else {
				String line_id = "";
				if(equipLineMap.containsKey(srcDevice.getPowerDeviceID())) {
					line_id = equipLineMap.get(srcDevice.getPowerDeviceID());
				}
				else if(srcDevice.getDeviceType().equals(SystemConstants.Switch) && CBSystemConstants.getCurRBM().getPd() !=null){
					//System.out.println("当剝设备："+CBSystemConstants.getPowerStation(srcDevice.getPowerStationID())+": "+srcDevice.getPowerDeviceName());
					List<Map> list = DBManager.queryForList("select distinct b.line_id from "+CBSystemConstants.equipUser+"T_C_TERMINAL a,"+CBSystemConstants.equipUser+"T_PD_EQUIPINFO b where CONNECTIVITYNODE_ID in(select CONNECTIVITYNODE_ID from "+CBSystemConstants.equipUser+"T_C_TERMINAL where equip_id='"+srcDevice.getPowerDeviceID()+"') and a.equip_id= b.equip_id and line_id is not null");
		            if(list.size() > 0 && list.get(0).get("line_id")!= null && !list.get(0).get("line_id").equals("")) {
		            	line_id = list.get(0).get("line_id").toString();
		            }
		            equipLineMap.put(srcDevice.getPowerDeviceID(), line_id);
				}
				if(!line_id.equals("") && CBSystemConstants.getStationPowerDevices(line_id)!= null) {
	            	StationDeviceMap.putAll(CBSystemConstants.getStationPowerDevices(line_id));

	            	mergeMap(pointDeviceMap,SystemConstants.getMapToplogyPointDevice().get(line_id));
	            	mergeMap(devicePointMap,SystemConstants.getMapToplogyDevicePoint().get(line_id));
            	}
			}
		}

		*/
			//List deviceList2 = pointDeviceMap.get("CN20000180f0007ac:0");

//		ArrayList<String> map3 = SystemConstants.getMapToplogyPointDevice().get("SS203010007000294").get("CN200001807004771:0");
//		ArrayList<String> map4 = SystemConstants.getMapToplogyPointDevice().get("FD20c010007000448").get("CN200001807004771:0");
//
			busbarSectionPointList = new HashMap();
			for( ArrayList<String> li : SystemConstants.getMapMotherlinePoint().values()) {
				for(String p : li){
					busbarSectionPointList.put(p,p);
				}

			}
			//busbarSectionPointList = SystemConstants.getMapMotherlinePoint().get(srcDevice.getPowerStationID());

			linkedDeviceList = getLinkedDevices(srcDevice.getPowerStationID(),srcDevice.getPowerDeviceID());
			outParaMap.put("linkedDeviceList", linkedDeviceList); //List
			outParaMap.put("pathList", resultPath); //HashMap<PowerDevice,ArrayList<PowerDevice>>
			outParaMap.put("allPathList", resultAllPath); //HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>
			//PowerDevice:linkedDeviceList中的柝个目标设备  ArrayList<PowerDevice>:起始设备到该目标设备的最短通路
			//ArrayList<ArrayList<PowerDevice>>:起始设备到该目标设备的所有通路

			if(isPrintPath)
			{
				for(Iterator iter = linkedDeviceList.iterator();iter.hasNext();) {
					PowerDevice pd = (PowerDevice) iter.next();
					for(Iterator iter2 = resultPath.get(pd).iterator();iter2.hasNext();) {
						PowerDevice device = (PowerDevice) iter2.next();
					}
					for(Iterator iter3 = resultAllPath.get(pd).iterator();iter3.hasNext();) {
						ArrayList<PowerDevice> pathList = (ArrayList<PowerDevice>) iter3.next();
						for(Iterator iter4 = pathList.iterator();iter4.hasNext();) {
							PowerDevice device = (PowerDevice) iter4.next();
						}
					}
				}
			}
		}
	}
	
	public void mergeMap(HashMap<String,ArrayList<String>> map1,HashMap<String,ArrayList<String>> map2)
	{
		for(Map.Entry<String,ArrayList<String>> entry : map2.entrySet()){
			String key = entry.getKey();
			ArrayList<String> val = entry.getValue();
			if(map1.containsKey(key)) {
				for(String v : val) {
					if(!map1.get(key).contains(v)) {
						map1.get(key).add(v);
					}
				}
				
			}
			else {
				map1.put(key, val);
			}
		}
		
	}
	
	public List getLinkedDevices(String powerStationID,String powerEquipID)
	{
		List linkedDeviceList = new ArrayList();
		linkedDeviceList = getDevices(linkedDeviceList, powerStationID, powerEquipID, "");
		return linkedDeviceList;
		
	}
	
	public List getDevices(List linkedDeviceList, String powerStationID,String powerEquipID, String lastPointID)
	{
		boolean isPointWithBusbarSection = false;
		boolean isPointLineBusbarSection = false;
		if(devicePointMap == null)
			return linkedDeviceList;
		List pointList = devicePointMap.get(powerEquipID);
		if(pointList==null)
			return linkedDeviceList;
		PowerDevice device = StationDeviceMap.get(powerEquipID);
		for(int i = 0; i < pointList.size(); i++) {
			if(isStop)
				break;
			if(device==null)
				continue;
			if(lastPointID.equals("") && validPort != 0 && validPort != (i+1))
				continue;
			String connectPointID = pointList.get(i).toString();
			
			
			
			if(connectPointID.equals(lastPointID) && !device.getDeviceType().equals(SystemConstants.MotherLine))
				continue; //丝能连续通过坌样的连接关系杜索
			if(busbarSectionPointList.containsKey(connectPointID) && !device.getDeviceType().equals(SystemConstants.MotherLine))
				isPointWithBusbarSection = true; //当剝是非毝线设备杜索毝线相关的连接关系
			else
				isPointWithBusbarSection = false;
			
			List deviceList = pointDeviceMap.get(connectPointID);
			
//			if(isPointWithBusbarSection == false) {
//				boolean isdz = true;
//				for(Iterator iter2 = deviceList.iterator();iter2.hasNext();) {
//					
//					String equipID = (String) iter2.next();
//					PowerDevice pd = StationDeviceMap.get(equipID);
//					if(!pd.getDeviceType().equals(SystemConstants.SwitchSeparate) ||pd.isPW())
//						isdz = false;
//				}
//				if(isdz) {
//					isPointWithBusbarSection = true;
//				}
//			}
			
//			int mlcount = 0;
//			int lncount = 0;
//			for(Iterator iter2 = deviceList.iterator();iter2.hasNext();) {
//				
//				String equipID = (String) iter2.next();
//				PowerDevice pd = StationDeviceMap.get(equipID);
//				if(pd.getDeviceType().equals(SystemConstants.MotherLine))
//					mlcount++;
//				if(pd.getDeviceType().equals("ACLineSegmentPW"))
//					lncount++;
//			}
//			if(mlcount>=2) {
//				isPointLineBusbarSection = true;
//			}
					
			
			List devList = new ArrayList();
			for(Iterator iter2 = deviceList.iterator();iter2.hasNext();) {
				if(isStop)
					break;
				String equipID = (String) iter2.next();
				if(devList.contains(equipID))
					continue;
				else
					devList.add(equipID);
				PowerDevice pd = StationDeviceMap.get(equipID);
			//System.out.println("找到："+pd.getPowerDeviceName());
				
//				int aa2=1;
//				if(pd.getPowerDeviceName().contains("村民委员会"))
//					aa2=2;
				
				if(pd == null)
					continue;
	
				if(isPointLineBusbarSection && pd.getDeviceType().equals(SystemConstants.MotherLine) && device.getDeviceType().equals(SystemConstants.MotherLine))
					continue;
				
				if(isPointWithBusbarSection && !pd.getDeviceType().equals(SystemConstants.MotherLine))
					continue; //若当剝是非毝线设备杜索毝线相关的连接关系,坪有毝线是有效目标设备
				if(equipID.equals(powerEquipID))
					continue;
				else if(pathMap.get(powerEquipID) != null && pathMap.get(powerEquipID).contains(pd))
					continue; //通路已包坫当剝设备坜止杜索
				else if(excDevList.contains(pd)) //靇排除设备坜止杜索
					continue;
				else if(isStopOnDiffVolt && pd.getPowerVoltGrade() != srcDevice.getPowerVoltGrade() && !pd.getDeviceType().equals(SystemConstants.PowerTransformer)) //靇排除设备坜止杜索
					continue;
				else if( (tagDevice!=null||!tagDevType.equals("")) &&
						tagDevType.indexOf(pd.getDeviceType()) == -1 && 
						excDevType.indexOf(pd.getDeviceType()) >= 0 &&
								(tagDevice==null || 
								!pd.getPowerDeviceID().equals(tagDevice.getPowerDeviceID())) ) //靇排除设备类型坜止杜索
					continue;
				else if( (tagDevice!=null||!tagDevType.equals("")) &&
						tagDevType.indexOf(pd.getDeviceType()) == -1 && 
						"DistributionPowerTransform".indexOf(pd.getDeviceType()) >= 0 &&
								(tagDevice==null || 
								!pd.getPowerDeviceID().equals(tagDevice.getPowerDeviceID())) ) //靇排除设备类型坜止杜索
					continue;
				else if(!pd.getDeviceRunType().equals("") && 
						( (excDevRunType.contains("groundknife")&&excDevRunType.equals(pd.getDeviceRunType()))
								||(!excDevRunType.contains("groundknife")&&excDevRunType.contains(pd.getDeviceRunType())))
						&& 
						(tagDevice==null || !pd.getPowerDeviceID().equals(tagDevice.getPowerDeviceID())) ) //靇排除设备类型坜止杜索
					continue;
				else if( excDevice!=null && pd.getPowerDeviceID().equals(excDevice.getPowerDeviceID()) ) //靇排除设备坜止杜索
					continue;
				else {
					//判断device和pd是坦为在毝蝔开关的坌一侧
					if(device.getDeviceType().equals(SystemConstants.SwitchSeparate) && (pd.getDeviceType().equals(SystemConstants.SwitchSeparate))){
						if(isSameType(deviceList))
							continue;
					}
				}
				
				
				ArrayList<PowerDevice> pathDeviceList = new ArrayList<PowerDevice>();
				if(pathMap.get(powerEquipID) == null)
					pathDeviceList.add(srcDevice);
				else
				{
					for(Iterator iter3 = pathMap.get(powerEquipID).iterator();iter3.hasNext();) {
						pathDeviceList.add((PowerDevice) iter3.next());
					}
				}
				pathDeviceList.add(pd); //将新杜索到的设备加入通路
				pathMap.put(pd.getPowerDeviceID(), pathDeviceList); //以新杜索到的设备的ID为通路索引
				
				if(tagDevice!=null && tagDevice.getPowerDeviceID().equals(pd.getPowerDeviceID())) {
				    if(tagStatus.equals("") || (!tagStatus.equals("") && tagStatus.contains(pd.getDeviceStatus()))){
				    	operateDevice(pd);
						if(!linkedDeviceList.contains(pd))
							linkedDeviceList.add(pd);
				    }
				}
				else if(tagDevice==null) 
				{
					if(tagDevType.equals("") || StringUtils.compareStr(pd.getDeviceType(), tagDevType) == 0) { //设备类型过滤
						if(tagDevRunType.equals("") || 
								(!tagDevRunType.equals("") && !pd.getDeviceRunType().equals("") && StringUtils.compareStr(pd.getDeviceRunType(), tagDevRunType) == 0)) {
							
							if(tagStatus.equals("") || (!tagStatus.equals("") && tagStatus.contains(pd.getDeviceStatus()))){
								operateDevice(pd);
								if(!linkedDeviceList.contains(pd)) {
									linkedDeviceList.add(pd);
									sameCount = 0;
								}
								else {
									if(sameCount == 300) {
										isStop = true;
										sameCount = 0;
										//return linkedDeviceList;
									}
									sameCount++;
								}
							}
						}
					}
				}
				if(isSearchDirectDevice)
					continue;
				else if(isStopOnTagDevType && comparetagDevTypeContainDeviceType(tagDevType,pd.getDeviceType()) && !tagDevRunType.equals("") && !pd.getDeviceRunType().equals("") && tagDevRunType.contains(pd.getDeviceRunType())) { //遇排除设备类型停止搜索
					continue;
				}
				else if(isStopOnTagDevType && comparetagDevTypeContainDeviceType(tagDevType,pd.getDeviceType()) && tagDevRunType.equals("")){
						continue;
				}
				else if(tagDevType.equals("") && excDevType.indexOf(pd.getDeviceType()) >= 0)
					continue;
				else if(pd.getDeviceType().equals(SystemConstants.InOutLine)) //靇线路坜止杜索
					continue;
				else if(pd.getDeviceType().equals(PWSystemConstants.PWTypePDBYQ)) //靇酝坘坜止杜索
					continue;
				else if(pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) //靇接地刀闸坜止杜索
					continue;
				else if(isStopOnBusbarSection && pd.getDeviceType().equals(SystemConstants.MotherLine) && !pd.isPW()) //是坦靇毝线坜止杜索
					continue;
				else if(!isSearchOffPath && !pd.getDeviceStatus().equals("0")  && 
						!pd.getDeviceType().equals("ACLineSegmentPW")  && 
						(!pd.getDeviceType().equals(SystemConstants.SwitchSeparate) || !isIgnoreDzlinked))
					continue;
				else if(tagDevice != null && tagDevice.getPowerDeviceID().equals(pd.getPowerDeviceID()))
					continue;
				else{
//					if(powerStationID.equals("1B281E447E2A4ED5808C52E442492C71")||powerStationID.equals("0C4D85244D50489DB355D4F32EAB0853")){
//						return linkedDeviceList;
//					}
					if(sameCount == 9000) {
						isStop = true;
						sameCount = 0;
						//return linkedDeviceList;
					}
					sameCount++;
					getDevices(linkedDeviceList, powerStationID, equipID,connectPointID);
				}
			}
		}
		return linkedDeviceList;
	}
	
	//判断连个设备是坦处在直接连接的毝蝔开关的坌一侧
	private boolean isSameType( List deviceList) {
		for(Iterator iter2 = deviceList.iterator();iter2.hasNext();){
			String pdTempId = (String) iter2.next();
			PowerDevice pdTemp = StationDeviceMap.get(pdTempId);
			if(pdTemp != null && pdTemp.getDeviceType().equals(SystemConstants.Switch) && 
					(pdTemp.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine) || pdTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)))
				return true;
		}
		return false;
	}

	private void operateDevice(PowerDevice pd)
	{
		ArrayList<PowerDevice> path = pathMap.get(pd.getPowerDeviceID());
//		System.out.println(path);
//		
//		System.out.println((cou++)+"次");
//		b = System.currentTimeMillis();
//	
//		System.out.println("耗时："+(b-a));
//		a = b;
		
		if(resultAllPath.get(pd) == null)
		{
			ArrayList<ArrayList<PowerDevice>> pathList = new ArrayList<ArrayList<PowerDevice>>();
			pathList.add(path);
			resultAllPath.put(pd, pathList);
			resultPath.put(pd, path);
		}
		else
		{
			resultAllPath.get(pd).add(path);
			if(resultPath.get(pd).size() > path.size())
				resultPath.put(pd, path);
		}
	}
	
	public void initParams(Map inParaMap) {
	       
		srcDevice = (PowerDevice) inParaMap.get("oprSrcDevice");
		if (inParaMap.get("tagDevice") != null) {
			tagDevice = (PowerDevice)inParaMap.get("tagDevice");
        }
		if (inParaMap.get("excDevice") != null) {
			excDevice = (PowerDevice)inParaMap.get("excDevice");
        }
        if (inParaMap.get("tagDevType") != null && !inParaMap.get("tagDevType").toString().equals("")) {
            tagDevType = inParaMap.get("tagDevType").toString().trim();
        }
        if (inParaMap.get("excDevType") != null && !inParaMap.get("excDevType").toString().equals("")) {
            excDevType = inParaMap.get("excDevType").toString().trim();
        }
        if (inParaMap.get("tagDevRunType") != null && !inParaMap.get("tagDevRunType").toString().equals("")) {
        	tagDevRunType = inParaMap.get("tagDevRunType").toString().trim();
        }
        if (inParaMap.get("tagStatus") != null && !inParaMap.get("tagStatus").toString().equals("")) {
        	tagStatus = inParaMap.get("tagStatus").toString().trim();
        }
        if (inParaMap.get("excDevRunType") != null && !inParaMap.get("excDevRunType").toString().equals("")) {
        	excDevRunType = inParaMap.get("excDevRunType").toString().trim();
        }
        if (inParaMap.get("isSearchDirectDevice") != null && !inParaMap.get("isSearchDirectDevice").toString().equals("")) {
        	isSearchDirectDevice = java.lang.Boolean.parseBoolean(inParaMap.get("isSearchDirectDevice").toString().trim());
        }
        if (inParaMap.get("isSearchOffPath") != null && !inParaMap.get("isSearchOffPath").toString().equals("")) {
        	isSearchOffPath = java.lang.Boolean.parseBoolean(inParaMap.get("isSearchOffPath").toString().trim());
        }
        if (inParaMap.get("isStopOnBusbarSection") != null && !inParaMap.get("isStopOnBusbarSection").toString().equals("")) {
        	isStopOnBusbarSection = java.lang.Boolean.parseBoolean(inParaMap.get("isStopOnBusbarSection").toString().trim());
        }
        if (inParaMap.get("isIgnoreDzlinked") != null && !inParaMap.get("isIgnoreDzlinked").toString().equals("")) {
        	isIgnoreDzlinked = java.lang.Boolean.parseBoolean(inParaMap.get("isIgnoreDzlinked").toString().trim());
        }
        if (inParaMap.get("isStopOnTagDevType") != null && !inParaMap.get("isStopOnTagDevType").toString().equals("")) {
        	isStopOnTagDevType = java.lang.Boolean.parseBoolean(inParaMap.get("isStopOnTagDevType").toString().trim());
        }
        if (inParaMap.get("isStopOnDiffVolt") != null && !inParaMap.get("isStopOnDiffVolt").toString().equals("")) {
        	isStopOnDiffVolt = java.lang.Boolean.parseBoolean(inParaMap.get("isStopOnDiffVolt").toString().trim());
        }
        if (inParaMap.get("isPrintPath") != null && !inParaMap.get("isPrintPath").toString().equals("")) {
        	isPrintPath = java.lang.Boolean.parseBoolean(inParaMap.get("isPrintPath").toString().trim());
        }
        if (inParaMap.get("excDevList") != null) {
        	excDevList = (List)inParaMap.get("excDevList");
        }
        if (inParaMap.get("validPort") != null) {
        	validPort = Integer.parseInt(inParaMap.get("validPort").toString());
        }
	}
	public void clearList(){
		this.validPort = 0;
		this.pathMap.clear();
		this.resultAllPath.clear();
		this.srcDevice = null;   //起始设备对象
		this.tagDevice = null;   //目标设备对象
		this.excDevice = null;   //排除设备对象
		this.isSearchDirectDevice = false; //是坦坪杜索直接连接设备
		this.isSearchOffPath = true; //是坦杜索断开路径
		this.isStopOnBusbarSection = true; //是坦靇到毝线坜止杜索
		this.isStopOnTagDevType = true; //靇到目标设备类型坜止杜索
		this.isStopOnDiffVolt = false;
		this.isIgnoreDzlinked = false;
		this.tagDevType = "";
		this.excDevType = "";
		this.tagDevRunType = "";
		this.excDevRunType = "";
		this.tagStatus = "";
		this.excDevList=new ArrayList<PowerDevice>();
		
	}
	public Boolean comparetagDevTypeContainDeviceType(String tagDevType , String deviceType){
		if(tagDevType.contains(",")){
			String[] tagArr = tagDevType.split(",");
			for(String str:tagArr){
				if(str.equals(deviceType)){
					return true;
				}
			}
		}else if(tagDevType.equals(deviceType)){
			return true;
		}
		return false;
	}

}

package czprule.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;

public class DeviceSearchManager {
	/**
	 * 
	 * @param motherLine 输入母线
	 * @param excDevs 排除设备集合
	 * @param flag True：有效通路  ；false：所有通路
	 * @return 搜索母线连接的其他母线集合
	 */
	public List<PowerDevice> getMotherLinesByML(PowerDevice motherLine,List<PowerDevice> excDevs,boolean flag){
		
		   List<PowerDevice> searchMontherLines=new ArrayList<PowerDevice>(); //母联开关连接的断开母线集合
		   CommonSearch cs=new CommonSearch();
	       Map<String,Object> inPara = new HashMap<String,Object>();
	       Map<String,Object> outPara = new HashMap<String,Object>();
	       PowerDevice tempDev=null;
		   //搜索闭合母线连接的母联开关 
	       List<PowerDevice> mlSwitchs=new ArrayList<PowerDevice>(); 
		   inPara.put("oprSrcDevice", motherLine);
	       inPara.put("tagDevType", SystemConstants.Switch); //目标设备母联开关
	       if(flag){
	           inPara.put("isSearchOffPath", "false"); //搜索闭合通路
	       }
	       if(excDevs.size()>0){
	           inPara.put("excDevList", excDevs);
	       }
	       inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变  
	       cs.execute(inPara, outPara);
	       List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
	       for (int j = 0; j < searchDevs.size(); j++) {
	    	   tempDev=(PowerDevice)searchDevs.get(j);
	    	   if((CBSystemConstants.RunTypeSwitchML.equals(tempDev.getDeviceRunType())
	    			   ||CBSystemConstants.RunTypeSwitchMLPL.equals(tempDev.getDeviceRunType()))){
	    		   if(flag){
	    			   if(tempDev.getDeviceStatus().equals("0"))
	    				   mlSwitchs.add(tempDev);
	    		   }
	    		   else
	    		        mlSwitchs.add(tempDev);
	    		   continue;
	    	   }
	       }
	       inPara.put("tagDevType", SystemConstants.SwitchSeparate); //目标设备母联刀闸
	       cs.execute(inPara, outPara);
	       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
	       for (int j = 0; j < searchDevs.size(); j++) {
	    	   tempDev=(PowerDevice)searchDevs.get(j);
	    	   if(CBSystemConstants.RunTypeKnifeML.equals(tempDev.getDeviceRunType()) ){//母联刀闸
	    		   if(flag){
	    			   if(tempDev.getDeviceStatus().equals("0"))
	    				   mlSwitchs.add(tempDev);
	    		   }
	    		   else
	    		        mlSwitchs.add(tempDev);
	    		   continue;
	    	   }
	       }
	       
	       
	       for (int i = 0; i < mlSwitchs.size(); i++) {
	    	   tempDev=mlSwitchs.get(i);
	    	   inPara.put("oprSrcDevice", tempDev);
	    	   if(flag){
		           inPara.put("isSearchOffPath", "false"); //搜索闭合通路
		       }
		       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
		       inPara.put("excDevType", SystemConstants.PowerTransformer); //排除主变  
		       if(excDevs.size()>0){
		           inPara.put("excDevList", excDevs);
		       }
		       cs.execute(inPara, outPara);
		       inPara.clear();
		       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		       for (int j = 0; j < searchDevs.size(); j++) {
		    	   tempDev=(PowerDevice)searchDevs.get(j);
		    	   if(flag){
			    	   if(tempDev.getDeviceStatus().equals("0"))
			    		   searchMontherLines.add(tempDev);
		    	   }
		    	   else
		    		   searchMontherLines.add(tempDev);
			   }
		   }
	       //递归调用母线搜索
	       excDevs.addAll(mlSwitchs);
	       excDevs.addAll(searchMontherLines);
	       List<PowerDevice>  motherlines=new ArrayList<PowerDevice>();  //母线连接的其他母线
	       for (int i = 0; i < searchMontherLines.size(); i++) {
	    	   tempDev=searchMontherLines.get(i);
	    	   DeviceSearchManager dsm=new DeviceSearchManager();
	    	   motherlines.addAll(dsm.getMotherLinesByML(tempDev, excDevs, flag));
		   }
	       searchMontherLines.addAll(motherlines);
	       return searchMontherLines;
	}
	

}

/**
* 版权声明 : 泰豪软件股份有限公司版权所有
* 项 目 组 ：操作票专家系统
* 功能说明 : 根据变电站编码到数据库中搜索该站内的电网设备，并根据CIM数据库模型生成连接拓补关系
* 作    者 : 张俊
* 开发日期 : 2008-06-17
* 修改日期 ：
* 修改说明 ：
* 修 改 人 ：
**/
package czprule.system;
import java.io.File;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import org.w3c.dom.Element;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.svg.listener.AllMapMouseEventListener;
import com.tellhow.czp.svg.listener.DefaultGVTTreeBuilderListener;
import com.tellhow.czp.svg.listener.DefaultGVTTreeRendererListener;
import com.tellhow.czp.svg.listener.DefaultSVGDocumentLoaderListener;
import com.tellhow.czp.svg.listener.StationMouseEventListener;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.AddGroundLineAction;
import com.tellhow.graphicframework.action.impl.AttachAnnotationSvgAction;
import com.tellhow.graphicframework.action.impl.AttachFlagSvgAction;
import com.tellhow.graphicframework.action.impl.ChooseDeviceRectFlashingAction;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.stationstartup.StationStartupManager;
/**
 *
 * <AUTHOR>
 */
public class CreatePowerStationToplogy {
  
	public static SVGCanvasPanel createSVGPanel(String stationID) {
		String filePath = "";
        String fileName = "";
        String stationName = "";
		List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(stationID);
        if(fileList.size() == 0) {
        	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
        	else if(CBSystemConstants.ddzl.equals(""))
        		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在一次接线图！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
            return null;
        }
        if(fileList.size()!=1){
 		 	CZPService.getService().filterMap(fileList, stationID);
      	}

        if(fileList.size() == 1) {
        	filePath = fileList.get(0).getFilePath();
            fileName = fileList.get(0).getFileName();
        }
        else {
        	Object[] options = fileList.toArray(); 
        	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
        	if(i == -1)
        		return null;
        	filePath = fileList.get(i).getFilePath();
            fileName = fileList.get(i).getFileName();
        }
        stationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stationID));
        return CreatePowerStationToplogy.createSVGPanel(stationID, stationName, fileName, filePath);
	}
	
	public static SVGCanvasPanel createSVGPanel(String stationID, String tabName, String fileName, String filePath) {
		return createSVGPanel(stationID, tabName, fileName, filePath, true);
    }
	
	public static SVGCanvasPanel createSVGPanel(String stationID, String tabName, String fileName, String filePath, boolean isActive) {
		if(isActive) {
			if(SystemConstants.getGuiBuilder()!=null && SystemConstants.getGuiBuilder().activateTabbedPageByName(filePath)) {
				if(CBSystemConstants.rbmList.size() > 0) {
		        	for(RuleBaseMode rbm :CBSystemConstants.rbmList){
		        		PowerDevice pd = rbm.getPd();
		    	        if(SystemConstants.getGuiBuilder().getActivateSVGPanel().getStationID().equals(pd.getPowerStationID())) {
		    	        	//边框效果
		    	        	ChooseDeviceRectFlashingAction action = new ChooseDeviceRectFlashingAction(pd);
		    	        	action.backexecute();
		    	    		action.execute();
		    	        }
		        	}
		        }
				return null;
			}
		}
		else {
			if(SystemConstants.getGuiBuilder().isTabbedPageExist(filePath))
				return null;
		}
		SVGCanvasPanel svgCanvasPanel = null;
		File svgMapFile = new File(filePath);
        if (!filePath.equals("") && !svgMapFile.exists()) {
            JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在["+fileName+"]一次接线图文件！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
        } 
        else {
        	//避免打开信息没有加载好的图形
        	if(tabName.lastIndexOf(".svg")==-1||tabName.lastIndexOf("线.svg")>0||tabName.lastIndexOf("环网图")>0||tabName.lastIndexOf("压板")>0||tabName.lastIndexOf("保护定值")>0||tabName.lastIndexOf("自投")>0){
	            svgCanvasPanel = new SVGCanvasPanel(tabName);
	            svgCanvasPanel.addSVGDocumentLoaderListener(new DefaultSVGDocumentLoaderListener(svgCanvasPanel));
	            svgCanvasPanel.addGVTTreeRendererListener(new DefaultGVTTreeRendererListener(svgCanvasPanel));
	            svgCanvasPanel.addGVTTreeBuilderListener(new DefaultGVTTreeBuilderListener(svgCanvasPanel));
	            svgCanvasPanel.setStationID(stationID);
	            if(!filePath.equals("")) {
		            if (stationID.equals(""))
		            	svgCanvasPanel.addSvgMouseEventListener(new AllMapMouseEventListener());
		            else {
		            	svgCanvasPanel.addSvgMouseEventListener(new StationMouseEventListener());
		    			if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
							List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+stationID+"'");
				            if(list.size() > 0 && list.get(0).get("st_id")!=null) {
				            	String sid = list.get(0).get("st_id").toString();
				            	if(CBSystemConstants.getStationPowerDevices(sid)==null)
				            		stationID = stationID + "," + sid;
			         
				            }
				        }
			            
					
		            	loadFacData(stationID);
		            }
		            svgCanvasPanel.loadSvgFile(svgMapFile, isActive);
	            }
	            else
	            	svgCanvasPanel.addSvgMouseEventListener(new AllMapMouseEventListener());
	        }
        }
        return svgCanvasPanel;
    }
	

	public static void openHref(Element evtElement) {
		Element deviceElement = (Element)evtElement.getParentNode();
        SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
        String fileName = resolver.getHref(deviceElement);
        String deviceName = resolver.getDeviceName(deviceElement);
        String deviceCode = resolver.getDeviceID(deviceElement);
        if(deviceCode.equals("") && !fileName.equals("")) {
        	for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
   				SVGFile svgFile = (SVGFile)it.next();
   				if(svgFile.getFileName().equals(fileName)) {
   					deviceCode = svgFile.getStationID();
   					break;
   				}
        	}
        }
        if(!deviceCode.equals("") && CBSystemConstants.getPowerStation(deviceCode)!= null)
        	deviceName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(deviceCode));
    	else
    		deviceName = fileName;
        if(fileName.equals("") && !deviceCode.equals("")) {
        	List<SVGFile> fileList = SystemConstants.getSVGFileByStationID(deviceCode);
            if(fileList.size() == 0) {
            	if(SystemConstants.threadLoadFile != null && SystemConstants.threadLoadFile.isAlive())
            		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
            	else
            		JOptionPane.showMessageDialog(SystemConstants.getMainFrame(), "不存在" + deviceName + "设备或链接图形！", SystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
                return;
            }
            else if(fileList.size() == 1) {
                fileName = fileList.get(0).getFileName();
            }
            else {
            	Object[] options = fileList.toArray(); 
            	int i = JOptionPane.showOptionDialog(SystemConstants.getMainFrame(), "选择要打开的图形", SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION, JOptionPane.WARNING_MESSAGE,null, options, options[0]);
            	if(i == -1)
            		return;
                fileName = fileList.get(i).getFileName();
            }
        }
        if(!fileName.equals("")) {
			String filePath = SystemConstants.FILE_SVGMAP_PATH + fileName;
			String tabName = "";
			if(fileName.split("\\.").length >= 2)
				tabName = deviceName;
			else
				tabName = fileName.substring(0, fileName.indexOf("."));
			
			if(fileName.contains("线.svg")){
				tabName = fileName.split("\\.")[0];
				tabName = tabName.replaceAll("_", "");
				tabName = tabName.replaceAll("gz", "");
			}
			CreatePowerStationToplogy.createSVGPanel(deviceCode, tabName, fileName, filePath);
        }
	}
	
	public static void loadSysData() {
		if(CBSystemConstants.getMapPowerStation().size() == 0) {
			CreatePowerStationToplogy.buildEquipType();
			CreatePowerStationToplogy.buildOrgan();
			CreatePowerStationToplogy.buildStation();
			CreatePowerStationToplogy.buildLine();
			CreatePowerStationToplogy.buildFeeder();
			CreatePowerStationToplogy.buildToplogy();
		}
    }

	public static void loadFacData(String stationID) {
		loadFacData(stationID,true);
	}

	public static void loadFacData(String stationID,boolean isLoadBySql) {
		if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
//			if(CBSystemConstants.roleCode.equals("1")) { //配网要由馈线ID找厂站ID，加载厂站设备
//				List<Map> list = DBManager.queryForList("select st_id from "+CBSystemConstants.equipUser+"t_c_aclineend where id='"+stationID+"'");
//	            if(list.size() > 0 && list.get(0).get("st_id")!=null) {
//	            	if(CBSystemConstants.getStationPowerDevices(list.get(0).get("st_id").toString())==null)
//	            		StationStartupManager.startup(list.get(0).get("st_id").toString());
//	            }
//	            
//			}
			StationStartupManager.startup(stationID,isLoadBySql);
    	}
    }
	
	public static void loadFacEquip(String stationID) {
		if(CBSystemConstants.getPowerStation(stationID)!=null && CBSystemConstants.getStationPowerDevices(stationID)==null) {
			if(stationID==null||stationID.equals("")){
			}else{
				StationStartupManager.startup(stationID);
			}
    	}
//		if(CBSystemConstants.getStationPowerDevices(stationID)==null) {
//			StationDeviceToplogy.loadStationPowerDevices(stationID);
//			StationDeviceToplogy.loadConnectCache(stationID);
//    	}
    }
	
	public static void buildOrgan()
    {
		List organList = PowerSystemDBOperator.getOrganList();
    	for(Iterator iter = organList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String powerDeviceID = String.valueOf(map.get("organid"));
			String powerDeviceName = String.valueOf(map.get("organname"));
			String powerDeviceCode = String.valueOf(map.get("shortname"));
			String cimID = map.get("cim_id")==null?"":String.valueOf(map.get("cim_id"));
			PowerDevice pd = new PowerDevice();
			pd.setPowerDeviceID(powerDeviceID);
			pd.setPowerDeviceName(powerDeviceName);
			pd.setPowerDeviceCode(powerDeviceCode);
			pd.setCimID(cimID);
			CBSystemConstants.getMapPowerOrgan().put(powerDeviceID, pd);
		}
    }
	
    public static void buildStation()
    {
    	List stationList = PowerSystemDBOperator.getStationList();
    	for(Iterator iter = stationList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String powerDeviceID = String.valueOf(map.get("STATION_ID"));
			String orgaId = String.valueOf(map.get("ORGA_ID"));
//			String powerDeviceCode = String.valueOf(map.get("STATION_CODE"));
			String powerDeviceName = String.valueOf(map.get("STATION_NAME"));
			double powerVoltGrade = 0;
			try{
				String volt = String.valueOf(map.get("VOLTAGE_CODE"));//避免数据出现带有kV的情况
				
				if(volt.contains("kV")){
					volt  = volt.replace("kV", "");
				}
				
				powerVoltGrade = Double.valueOf(volt);
			}
			catch(Exception ex) {
				ex.printStackTrace();
			}
			String deviceType = String.valueOf(map.get("STATION_FLAG"));
			String cimID =  String.valueOf(map.get("CIM_ID"));
			String graphFileName =  map.get("STATION_GRAPH")==null?"":String.valueOf(map.get("STATION_GRAPH"));
			PowerDevice pd = new PowerDevice();
			pd.setPowerDeviceID(powerDeviceID);
			pd.setOrgaId(orgaId);
//			pd.setPowerDeviceCode(powerDeviceCode);
			pd.setPowerDeviceName(powerDeviceName);
			pd.setPowerStationName(powerDeviceName);
			pd.setPowerVoltGrade(powerVoltGrade);
			
			pd.setDeviceType(deviceType.equals("1")?SystemConstants.PowerFactory:SystemConstants.PowerStation);
			pd.setCimID(cimID);
			pd.setGraphFileName(graphFileName);
			CBSystemConstants.putMapPowerStation(powerDeviceID, pd);
			
			for (Iterator it = SystemConstants.getMapSVGFile().values().iterator(); it.hasNext();) {
   				SVGFile svgFile = (SVGFile)it.next();
   				if(svgFile.getFileName().equals(graphFileName)) {
   					svgFile.setStationID(powerDeviceID);
   					svgFile.setMapType("fac");
   					break;
   				}
			}
		}
    }
    
    public static void buildLine()
    {
    	List lineList = PowerSystemDBOperator.getLineList();
    	for(Iterator iter = lineList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String powerDeviceID = String.valueOf(map.get("LINE_ID"));
			String powerDeviceCode = String.valueOf(map.get("LINE_CODE"));
			String powerDeviceName = String.valueOf(map.get("LINE_NAME"));
			double powerVoltGrade = 0;
			try{
				powerVoltGrade = Double.valueOf(String.valueOf(map.get("VOLTAGE_CODE")));
			}catch(Exception ex) {
				
			}
			String cimID =  String.valueOf(map.get("CIM_ID"));
			String deviceStatus = String.valueOf(map.get("status"));
			PowerDevice pd = new PowerDevice();
			pd.setPowerDeviceID(powerDeviceID);
			pd.setPowerDeviceCode(powerDeviceCode);
			pd.setPowerDeviceName(powerDeviceName);
			pd.setPowerVoltGrade(powerVoltGrade);
			pd.setPowerStationID(String.valueOf(map.get("ST_ID")));
			pd.setCimID(cimID);
			pd.setDeviceStatus(deviceStatus);
			pd.setDeviceType(SystemConstants.InOutLine);
			pd.setPowerDeviceName(CZPService.getService().getDevNameInit(pd));
			CBSystemConstants.putMapPowerLine(powerDeviceID, pd);
		}
    }
    
    public static void buildFeeder()
    {
    	List lineList = PowerSystemDBOperator.getFeederList();
    	for(Iterator iter = lineList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String powerDeviceID = String.valueOf(map.get("LINE_ID"));
			String powerDeviceCode = String.valueOf(map.get("LINE_CODE"));
			String powerDeviceName = String.valueOf(map.get("LINE_NAME"));
			double powerVoltGrade = 0;
			String orga_id = String.valueOf(map.get("orga_id"));
			try{
			powerVoltGrade = Double.valueOf(String.valueOf(map.get("VOLTAGE_CODE")));
			
			}catch(Exception ex) {
				
			}
			String cimID =  String.valueOf(map.get("CIM_ID"));
			String deviceStatus = String.valueOf(map.get("status"));
			String powerStationID = String.valueOf(map.get("st_id"));
			PowerDevice pd = new PowerDevice();
			pd.setPowerDeviceID(powerDeviceID);
			pd.setPowerDeviceCode(powerDeviceCode);
			pd.setPowerDeviceName(powerDeviceName);
			pd.setPowerVoltGrade(powerVoltGrade);
			pd.setCimID(cimID);
			pd.setOrgaId(orga_id);
			pd.setDeviceStatus(deviceStatus);
			pd.setDeviceType(SystemConstants.InOutLine);
			pd.setPowerDeviceName(CZPService.getService().getDevNameInit(pd));
			pd.setPowerStationID(powerStationID);
			CBSystemConstants.putMapPowerFeeder(powerDeviceID, pd);
		}
    }
    
    public static void buildToplogy()
    {
    	List list = PowerSystemDBOperator.getToplogyList();
    	for(Iterator iter = list.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String LINE_ID = String.valueOf(map.get("LINE_ID"));
			String STATION_ID = String.valueOf(map.get("STATION_ID"));
			String EQUIP_ID = String.valueOf(map.get("EQUIP_ID"));
			if(CBSystemConstants.getLinestationmap().get(LINE_ID) == null)
				CBSystemConstants.getLinestationmap().put(LINE_ID, new ArrayList());
			CBSystemConstants.getLinestationmap().get(LINE_ID).add(STATION_ID);
			if(CBSystemConstants.getStationlinemap().get(STATION_ID) == null)
				CBSystemConstants.getStationlinemap().put(STATION_ID, new ArrayList());
			CBSystemConstants.getStationlinemap().get(STATION_ID).add(LINE_ID);
			
			if(CBSystemConstants.getLineequipmap().get(LINE_ID) == null)
				CBSystemConstants.getLineequipmap().put(LINE_ID, new ArrayList());
			CBSystemConstants.getLineequipmap().get(LINE_ID).add(EQUIP_ID);
			if(CBSystemConstants.getEquiplinemap().get(EQUIP_ID) == null)
				CBSystemConstants.getEquiplinemap().put(EQUIP_ID, new ArrayList());
			CBSystemConstants.getEquiplinemap().get(EQUIP_ID).add(LINE_ID);
		}
    }
    
    public static void buildEquipType()
    {
    	List equipTypeList = PowerSystemDBOperator.getEquipType();
    	for(Iterator iter = equipTypeList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String equipTypeFlag = String.valueOf(map.get("EQUIPTYPE_FLAG"));
			String equipTypeName = String.valueOf(map.get("EQUIPTYPE_NAME"));
			SystemConstants.putMapEquipType(equipTypeFlag, equipTypeName);
		}
    }
    
//    public static void buildDeviceState(String stationID)
//    {
//    	List lineList = PowerSystemDBOperator.getDeviceState(stationID);
//    	for(Iterator iter = lineList.iterator();iter.hasNext();) {
//			Map map = (Map) iter.next();
//			String equipID = String.valueOf(map.get("EQUIP_ID"));
//			String equipState = String.valueOf(map.get("EQUIP_STATE"));
//			PowerDevice pd = (PowerDevice)SystemConstants.getMapPowerStationDevice(stationID, equipID);
//			if(pd != null)
//				pd.setDeviceStatus(equipState);
//		}
//    }
    
    public static Map<String,Map> buildTelemetering(String stationID, String type)
    {
    	Map<String,Map> telemeteringMap = new HashMap<String,Map>();
    	List telemeteringList = null;
    	if(stationID.equals(""))
    		telemeteringList = PowerSystemDBOperator.getSysTelemetering();
    	else
    		telemeteringList = PowerSystemDBOperator.getFacTelemetering(stationID);
    	if(type.equals("1")) {
    		for(Iterator iter = telemeteringList.iterator();iter.hasNext();) {
				Map map = (Map) iter.next();
				String measID = String.valueOf(map.get("EQUIPID"))+"_"+String.valueOf(map.get("MEASTYPE"));
				telemeteringMap.put(measID, map);
	    	}
    	}
    	else if(type.equals("2")) {
	    	for(Iterator iter = telemeteringList.iterator();iter.hasNext();) {
				Map map = (Map) iter.next();
				String measID = String.valueOf(map.get("MEASID"));
				telemeteringMap.put(measID, map);
	    	}
    	}
    	return telemeteringMap;
    }
    
    public static void buildActionGroundline(String stationID)
    {
    	List actionGroundLineList = PowerSystemDBOperator.getActionGroundLine(stationID);
    	for(Iterator iter = actionGroundLineList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String tObjectID = String.valueOf(map.get("TOBJECTID"));
			String groundline_type = String.valueOf(map.get("GROUNDLINE_TYPE"));
			int groundline_x = Integer.valueOf(map.get("GROUNDLINE_X").toString());
			int groundline_y = Integer.valueOf(map.get("GROUNDLINE_Y").toString());
			SvgAction action = new AddGroundLineAction((PowerDevice)CBSystemConstants.getPowerDevice(stationID, tObjectID), groundline_type,true,groundline_x,groundline_y);
			action.execute();
    	}
    }
    
    public static void buildActionCard(String stationID)
    {
    	List actionGroundLineList = PowerSystemDBOperator.getActionCard(stationID);
    	for(Iterator iter = actionGroundLineList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String tObjectID = String.valueOf(map.get("TOBJECTID"));
			String card_type = String.valueOf(map.get("CARD_TYPE"));
			int card_x = Integer.valueOf(map.get("CARD_X").toString());
			int card_y = Integer.valueOf(map.get("CARD_Y").toString());
			SvgAction action = new AttachFlagSvgAction((PowerDevice)CBSystemConstants.getPowerDevice(stationID, tObjectID), card_type,true,card_x,card_y);
			action.execute();
    	}
    }
    
    public static void buildActionNote(String stationID)
    {
    	List actionGroundLineList = PowerSystemDBOperator.getActionNote(stationID);
    	for(Iterator iter = actionGroundLineList.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String tObjectID = String.valueOf(map.get("TOBJECTID"));
			String note = String.valueOf(map.get("NOTE"));
			int note_x = Integer.valueOf(map.get("NOTE_X").toString());
			int note_y = Integer.valueOf(map.get("NOTE_Y").toString());
			SvgAction action = new AttachAnnotationSvgAction((PowerDevice)CBSystemConstants.getPowerDevice(stationID, tObjectID), note,true,note_x,note_y);
			action.execute();
    	}
    }

}

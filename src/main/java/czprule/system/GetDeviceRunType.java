package czprule.system;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;

/**
 * 母联兼旁路开关运行状态转换
 * 
 * <AUTHOR>
 * 
 */

public class GetDeviceRunType {
	public GetDeviceRunType(PowerDevice stationDev) {
		Search(stationDev);
	}

	/**
	 * Search the device
	 * 
	 * @param stationDev
	 */
	public void Search(PowerDevice stationDev) {
		List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
		// If the param's device type is switch search knife-switches
		// ,otherwise search the switch at first
		if (stationDev.getDeviceType().equals(SystemConstants.Switch)) {
			setRuntype(stationDev);
		} else if (stationDev.getDeviceType().equals(
				SystemConstants.SwitchSeparate)) {
			pdlist = RuleExeUtil.getDeviceDirectList(stationDev,SystemConstants.Switch);
			if (pdlist.size() > 0 && pdlist.get(0).getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchMLPL)) {
				Search(pdlist.get(0));
			} else {
				return;
			}
		}

	}

	/**
	 * 更新母联兼旁路开关的实际运行类型
	 * 
	 * @param pdlist
	 * @param stationDev
	 */
	public void setRuntype(PowerDevice stationDev) {
		PowerDevice pdpl = null;
		PowerDevice pdmxpl = null;
		PowerDevice pdmx = null;
		
		List<PowerDevice> pdlist1 = RuleExeUtil.getDeviceDirectByPortList(stationDev,SystemConstants.SwitchSeparate, "1");
		List<PowerDevice> pdlist2 = RuleExeUtil.getDeviceDirectByPortList(stationDev,SystemConstants.SwitchSeparate, "2");
		
		if(pdlist1.size() > 0 && pdlist2.size() > 0) {
			for (int i = 0; i < pdlist1.size(); i++) {
				PowerDevice pd = pdlist1.get(i);
				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
					pdpl = pd;
					pdlist1.remove(pd);
					if(pdlist1.size() > 0)
						pdmxpl = pdlist1.get(0);
					if(pdlist2.size()>0)
						pdmx = pdlist2.get(0);
					break;
				}
			}
			for (int i = 0; i < pdlist2.size(); i++) {
				PowerDevice pd = pdlist2.get(i);
				if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
					pdpl = pd;
					pdlist2.remove(pd);
					if(pdlist2.size() > 0)
						pdmxpl = pdlist2.get(0);
					if(pdlist1.size()>0)
						pdmx = pdlist1.get(0);
					break;
				}
			}
		}
		
		// Avoid Nullpointerexception return
		if (pdpl == null || pdmx == null || pdmxpl == null) {
			return;
		}
		if (pdpl.getDeviceStatus().equals("0")
				&& pdmx.getDeviceStatus().equals("0")
				&& pdmxpl.getDeviceStatus().equals("1")) {
			stationDev.setDeviceRunType(CBSystemConstants.RunTypeSwitchPL); //旁路刀闸和旁路对侧母线刀闸在合位，旁路侧母线刀闸在分位，开关起旁路作用
		} else if (pdpl.getDeviceStatus().equals("1")
				&& pdmx.getDeviceStatus().equals("0")
				&& pdmxpl.getDeviceStatus().equals("0")) {
			stationDev.setDeviceRunType(CBSystemConstants.RunTypeSwitchML);  //旁路刀闸在分位，两个母线刀闸在合位，开关起母联作用
		} else {
			stationDev.setDeviceRunType(CBSystemConstants.RunTypeSwitchMLPL);
		}
	}
}

package czprule.system;

import java.awt.Component;

import javax.swing.JFrame;
import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;
import org.apache.log4j.Logger;

public class ShowMessage {

	private final static Logger logger = Logger.getLogger(ShowMessage.class);

	public static void view(String viewValue) {
		if (!CBSystemConstants.isCurrentSys) {
			logger.info(viewValue);
			return;
		}
		JFrame mf = SystemConstants.getMainFrame();
		String title = SystemConstants.SYSTEM_TITLE;
		JOptionPane.showMessageDialog(mf, viewValue, title,
				javax.swing.JOptionPane.INFORMATION_MESSAGE);
	}

	public static void view(Component parenFrame, String viewValue) {
		if (!CBSystemConstants.isCurrentSys) {
			return;
		}
		String title = SystemConstants.SYSTEM_TITLE;
		JOptionPane.showMessageDialog(parenFrame, viewValue, title,
				javax.swing.JOptionPane.INFORMATION_MESSAGE);
	}

	public static void viewWarning(Component parenFrame, String viewValue) {
		if (!CBSystemConstants.isCurrentSys) {
			return;
		}
		String title = SystemConstants.SYSTEM_TITLE;
		JOptionPane.showMessageDialog(parenFrame, viewValue, "提醒",
				javax.swing.JOptionPane.WARNING_MESSAGE);
	}
}

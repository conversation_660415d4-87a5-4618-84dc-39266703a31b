
package czprule.system;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.sql.DataSource;

import org.apache.commons.collections.map.CaseInsensitiveMap;
import org.springframework.context.support.FileSystemXmlApplicationContext;
import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.BatchPreparedStatementSetter;
import org.springframework.jdbc.core.BeanPropertyRowMapper;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.support.AbstractLobCreatingPreparedStatementCallback;
import org.springframework.jdbc.support.lob.DefaultLobHandler;
import org.springframework.jdbc.support.lob.LobCreator;
import org.springframework.jdbc.support.lob.LobHandler;
import org.springframework.jdbc.support.rowset.SqlRowSet;
import org.springframework.jdbc.support.rowset.SqlRowSetMetaData;

import tbp.common.config.constants.SysConstants;
import tbp.common.exception.sys.dao.DaoSysException;
import tbp.common.jdbc.sql.JDBCUtil;

import com.tellhow.common.spring.BeanFactory;
import com.tellhow.czp.Robot.ObjectRowMapper;
import com.tellhow.czp.util.DesUtil;

public final class DBManager {
	private final static String DATASOURCE_BEAN_NAME = "tbp.sys.DataSource1";
	private final static String DATASOURCE_BEAN_NAME_OMS = "tbp.sys.DataSourceOMS";
	private final static String DATASOURCE_BEAN_NAME_EMS = "tbp.sys.DataSourceEMS";
	private final static String DATASOURCE_BEAN_NAME_HISDB = "tbp.sys.DataSourceHISDB";
	private static FileSystemXmlApplicationContext context = null;
	private static JdbcTemplate czpTemplate = null;
	private static JdbcTemplate omsTemplate = null;
	private static JdbcTemplate emsTemplate = null;
	private static JdbcTemplate hisdbTemplate = null;
	private static JdbcTemplate locTemplate = null;
	private static boolean isConnected;
	public static boolean isPrintSQL = false;
	private static long start;
	//是否开启dubbo接口，默认不启用
	public static boolean isInterfaceOpened = false;
	
	static {
		try {
			//System.out.println(context);
			if(context == null) {
				context = new FileSystemXmlApplicationContext(
						"file:" + System.getProperty("user.dir")
						+ SysConstants.SYS_CONFIG_DIR + "/springContainer.xml");
				
				isConnected = true;
			}
		}
		catch(Exception ex) {
			ex.printStackTrace();
			ShowMessage.view("网络异常，不能连接到数据库！");
			//System.out.println("网络异常，不能连接到数据库！");
			isConnected = false;
			CBSystemConstants.isOffline=true;
			System.exit(0);
		}
		//System.out.println(context);
	}
	
	/**
	 * 检查数据库是否能连接
	 * @return
	 */
	public static boolean isConnected() {
		return isConnected;
	}
	
	public static JdbcTemplate getCZPJdbcTemplate() {
		if(czpTemplate == null)
			czpTemplate = new JdbcTemplate(getCZPDataSource());
		return czpTemplate;
	}
	
	public static JdbcTemplate getOMSJdbcTemplate() {
		if(omsTemplate == null)
			omsTemplate = new JdbcTemplate(getOMSDataSource());
		return omsTemplate;
	}
	
	public static JdbcTemplate getEMSJdbcTemplate() {
		if(emsTemplate == null)
			emsTemplate = new JdbcTemplate(getEMSDataSource());
		return emsTemplate;
	}
	
	public static JdbcTemplate getHISDBJdbcTemplate() {
		if(hisdbTemplate == null)
			hisdbTemplate = new JdbcTemplate(getHISDBDataSource());
		return hisdbTemplate;
	}
	
	/*public static JdbcTemplate getLOCJdbcTemplate() {
		if(locTemplate == null)
			locTemplate = new JdbcTemplate(getLOCDataSource());
		return locTemplate;
	}*/
	
	public static DataSource getCZPDataSource() {
		//System.out.println(context);
		DataSource dataSource = (DataSource)context.getBean(DATASOURCE_BEAN_NAME);
		return dataSource;
	}
	
	public static DataSource getOMSDataSource() {
		DataSource dataSource = (DataSource)context.getBean(DATASOURCE_BEAN_NAME_OMS);
		return dataSource;
	}
	
	public static DataSource getEMSDataSource() {
		DataSource dataSource = (DataSource)context.getBean(DATASOURCE_BEAN_NAME_EMS);
		return dataSource;
	}
	
	public static DataSource getHISDBDataSource() {
		DataSource dataSource = (DataSource)context.getBean(DATASOURCE_BEAN_NAME_HISDB);
		return dataSource;
	}
	
	/*public static DataSource getLOCDataSource() {
		DataSource dataSource = (DataSource)context.getBean(DATASOURCE_BEAN_NAME_LOC);
		return dataSource;
	}*/
	
	/**
	 * 	获得操作票数据库连接
	 * @return
	 */
	public static Connection getConnection() {
		if(isConnected) {
			try {
				return getCZPDataSource().getConnection();
			} catch (SQLException e) {
				e.printStackTrace();
			}
		}
		return null;
	}
	/**
	 * 	获得OMS数据库连接
	 * @return
	 */
	public static Connection getOMSConnection()
	  {
		try {
			return getOMSDataSource().getConnection();
		} catch (SQLException e) {
			e.printStackTrace();
		}
	    return null;
	  }
	/**
	 * 	获得本地文件数据库连接
	 * @return
	 */
	public static Connection getLOCConnection(){
		try {
			Class.forName("org.sqlite.JDBC");
			Connection conn=DriverManager.getConnection("jdbc:sqlite:czp.db");
			return conn;
		} catch (SQLException e) {
			e.printStackTrace();
			System.exit(0);
		} catch (ClassNotFoundException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			System.exit(0);
		}
	    return null;
	}
	@SuppressWarnings("unchecked")
	public static <T> List<T> query(Class clazz, String sql) {
		List<T> list = null;
		if(CBSystemConstants.sqlUpperCase){
			sql=changeSqltoUpperCase(sql);
		}
		if(isInterfaceOpened){
//			sql = DesUtil.encrypt(sql);
			DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.query(clazz,sql);
    	}else{
    		list = getCZPJdbcTemplate().query(sql, new BeanPropertyRowMapper(clazz));
    	}
		return list;
	}
	
	public static Object queryForObject(String sql,ObjectRowMapper orm)
	{
		Object obj = null;
		if(CBSystemConstants.sqlUpperCase){
			sql=changeSqltoUpperCase(sql);
		}
		if(isInterfaceOpened){
//			sql = DesUtil.encrypt(sql);
			DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
			obj = dBManagerService.queryForObject(sql,orm);
    	}else{
    		obj = getCZPJdbcTemplate().queryForObject(sql, orm);
    	}
		
		return obj;
	}
	
	
	@SuppressWarnings("unchecked")
	public static <T> List<T> query(String sql) {
		List<T> list = null;
		if(CBSystemConstants.sqlUpperCase){
			sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForList(sql);
    		for(int i = 0; i < list.size(); i++) {
    			CaseInsensitiveMap cimap = new CaseInsensitiveMap((Map)list.get(i));
    			list.set(i, (T)cimap);
    		}
    	}else{
    		list = (List<T>)getCZPJdbcTemplate().queryForList(sql);
    	}
		return list;
	}
	
    @SuppressWarnings("unchecked")
	public static Map<String, Object> queryForMap(String sql, Object...params) {
    	Map<String, Object> map = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		map = dBManagerService.queryForMap(sql,params);
    	}else{
    		map = getCZPJdbcTemplate().queryForMap(sql, params);
    	}
    	return map;
    }
    
    public static int queryForInt(String sql) {
    	int i = 0;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		i = dBManagerService.queryForInt(sql);
    	}else{
    		i = getCZPJdbcTemplate().queryForInt(sql);
    	}
    	return i;
    }
    
    public static int queryForInt(String sql, Object...params) {
    	int i = 0;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		i = dBManagerService.queryForInt(sql,params);
    	}else{
    		i = getCZPJdbcTemplate().queryForInt(sql, params);
    	}
    	return i;
    }
    
    public static String queryForString(String sql) {
    	String str = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		str = dBManagerService.queryForString(sql);
    	}else{
    		Object object =  getCZPJdbcTemplate().queryForObject(sql, String.class);
    		str = (object == null) ? null : object.toString();
    	}
		return str;
    }
    
    public static String queryForString(String sql, Object...params) {
    	String str = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		str = dBManagerService.queryForString(sql,params);
    	}else{
    		Object object = getCZPJdbcTemplate().queryForObject(sql, params, String.class);
    		str = (object == null) ? null : object.toString();
    	}
		return str;
    }
    
    @SuppressWarnings("unchecked")
	public static Object queryForObject(Class requiredType, String sql, Object...params) {
    	Object obj = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		obj = dBManagerService.queryForObject(requiredType,sql,params);
    	}else{
    		obj = getCZPJdbcTemplate().queryForObject(sql, params, requiredType);
    	}
    	
    	return obj;
    }
    
    public static long queryForLong(String sql, Object...params) {
    	long l = 0;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		l = dBManagerService.queryForLong(sql,params);
    	}else{
    		l = getCZPJdbcTemplate().queryForLong(sql, params);
    	}		
    	
    	return l;
    }
    
    @SuppressWarnings("unchecked")
	public static <T> List<T> queryForList(Class requiredType, String sql, Object...params) {
    	List<T> list = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForList(requiredType,sql,params);
    	}else{
    		list = getCZPJdbcTemplate().queryForList(sql, params, requiredType);
    	}		
    	return list;
    }
    
    public static <T> List<T> queryForList(String sql, Object...params) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isPrintSQL) {
	    	System.out.println("正在执行的sql语句"+sql);
	    	start=System.currentTimeMillis();
    	}
    	List<T> list = null;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForList(sql,params);
    	}else{
    		list = (List<T>)getCZPJdbcTemplate().queryForList(sql, params);
    	}	
    	if(isPrintSQL) {
    		long end=System.currentTimeMillis();
    		System.out.println("所消耗的时间是:"+(end-start)+"ms");
    	}
        return list;
    }

    public static <T> List<T> queryForList(String sql) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isPrintSQL) {
    		System.out.println("正在执行的sql语句"+sql);
    		start=System.currentTimeMillis();
    	}
    	List<T> list = null;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForList(sql);
    		
    		for(int i = 0; i < list.size(); i++) {
    			CaseInsensitiveMap cimap = new CaseInsensitiveMap((Map)list.get(i));
    			list.set(i, (T)cimap);
    		}
    	}else{
    		list = (List<T>)getCZPJdbcTemplate().queryForList(sql);
    	}
    	
    	if(isPrintSQL) {
    		long end=System.currentTimeMillis();
    		System.out.println("所消耗的时间是:"+(end-start)+"ms");
    	}
    	return list;
    }
    //数据库插入Clod字段
    public static void execute(String sql,final String ret) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isPrintSQL) {
    		System.out.println("正在执行的sql语句"+sql);
    		start=System.currentTimeMillis();
    	}
//    	sql=sql.replaceAll("NULL", "null").replaceAll("null", "''");
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		dBManagerService.execute(sql, ret);
    	}else{
    		LobHandler lobHandler = new DefaultLobHandler();
    		getCZPJdbcTemplate().execute(sql,
    		new AbstractLobCreatingPreparedStatementCallback(lobHandler) {
    			@Override
    			protected void setValues(PreparedStatement ps, LobCreator lobCreator) throws SQLException {
    				lobCreator.setClobAsString(ps, 1, ret);
    			}

    		});
    	}
    	if(isPrintSQL) {
    		long end=System.currentTimeMillis();
    		System.out.println("所消耗的时间是:"+(end-start)+"ms");
    	}
    	
    }
    
    public static <T> SqlRowSet queryForRowSet(String sql) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	SqlRowSet set = null;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		set = dBManagerService.queryForRowSet(sql);
    	}else{
    		set = getCZPJdbcTemplate().queryForRowSet(sql);
    	}
    	return set;
    }

    public static <T> List<T> queryMetaData(String sql) {
    	List list = new ArrayList();
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryMetaData(sql);
    	}else{
    		SqlRowSet sqlRowSet = getCZPJdbcTemplate().queryForRowSet(sql);   
        	SqlRowSetMetaData sqlRsmd = sqlRowSet.getMetaData();   
        	int columnCount = sqlRsmd.getColumnCount();   
        	for (int i = 1; i <= columnCount; i++) {   
        		Map<String,String> fieldMap = new HashMap<String,String>();   
        		fieldMap.put("name", sqlRsmd.getColumnName(i));   
        		fieldMap.put("fieldType", String.valueOf(sqlRsmd.getColumnTypeName(i)));
        		list.add(fieldMap);
        	}
    	}
    	return list;
    }
    
    public static int update(String sql, Object...params) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	int i = 0;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		i = dBManagerService.update(sql,params);
    	}else{
    		i = getCZPJdbcTemplate().update(sql, params);
    	}
    	return i;
    }
    
    public static int[] update(String sql, final List parameterList) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	int[] intArr = null;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		intArr = dBManagerService.update(sql,parameterList);
    	}else{
    		BatchPreparedStatementSetter setter = new BatchPreparedStatementSetter() {

    			public int getBatchSize() {
    				return parameterList.size();
    			}

    			public void setValues(PreparedStatement ps, int i) {
    				Object[] parameters = (Object[]) parameterList.get(i);
    				try {
    					JDBCUtil.setParameters(ps, parameters);
    				} catch (SQLException e) {
    					throw new DaoSysException(e);
    				}
    			}
    		};
    		intArr = getCZPJdbcTemplate().batchUpdate(sql, setter);
    	}
		return intArr;
    }
    
    public static void batchUpdate(String[] sql) {
    	if(CBSystemConstants.sqlUpperCase){
			for(int i=0;i<sql.length;i++){
				sql[i]=changeSqltoUpperCase(sql[i]);
			}
		}
    	if(isInterfaceOpened){
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
//    		for(int i = 0; i < sql.length; i++) {
//    			sql[i] = DesUtil.encrypt(sql[i]);
//    		}
    		dBManagerService.batchUpdate(sql);
    	}else{
    		getCZPJdbcTemplate().batchUpdate(sql);
    	}
    }
    
    public static void execute(String sql) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isPrintSQL) {
    		System.out.println("正在执行的sql语句"+sql);
    		start=System.currentTimeMillis();
    	}
//    	sql=sql.replaceAll("NULL", "null").replaceAll("null", "''");
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		dBManagerService.execute(sql);
    	}else{
    		getCZPJdbcTemplate().execute(sql);
    	}
    	if(isPrintSQL) {
    		long end=System.currentTimeMillis();
    		System.out.println("所消耗的时间是:"+(end-start)+"ms");
    	}
    	
    }
    public static int update(String sql) {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isPrintSQL) {
    		System.out.println("正在执行的sql语句"+sql);
    		start=System.currentTimeMillis();
    	}
    	int result = 0;
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		result = dBManagerService.update(sql);
    	}else{
    		result = getCZPJdbcTemplate().update(sql);
    	}
    	if(isPrintSQL) {
    		long end=System.currentTimeMillis();
    		System.out.println("所消耗的时间是:"+(end-start)+"ms");
    	}
    	return result;
    }
    
    public static void executeOMS(String sql)
    {
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		dBManagerService.executeOMS(sql);
    	}else{
    		getOMSJdbcTemplate().execute(sql);
    	}
    }
    
    public static <T> List<T> queryForListOMS(String sql) {
    	List<T> list = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForListOMS(sql);
    	}else{
    		list = (List<T>)getOMSJdbcTemplate().queryForList(sql);
    	}
    	return list;
    }
	public static <T> List<T> queryForListOMS(String sql, Object...params) {
		if(CBSystemConstants.sqlUpperCase){
			sql=changeSqltoUpperCase(sql);
		}
		if(isPrintSQL) {
			System.out.println("正在执行的sql语句"+sql);
			start=System.currentTimeMillis();
		}
		List<T> list = null;
		if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
			DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
			list = dBManagerService.queryForListOMS(sql,params);
		}else{
			list = (List<T>)getOMSJdbcTemplate().queryForList(sql, params);
		}
		if(isPrintSQL) {
			long end=System.currentTimeMillis();
			System.out.println("所消耗的时间是:"+(end-start)+"ms");
		}
		return list;
	}
    
    public static <T> List<T> queryForListEMS(String sql) {
    	List<T> list = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForListEMS(sql);
    	}else{
    		list = (List<T>)getEMSJdbcTemplate().queryForList(sql);
    	}
    	return list;
    }
    
    public static <T> List<T> queryForListHISDB(String sql) {
    	List<T> list = null;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		list = dBManagerService.queryForListHISDB(sql);
    	}else{
    		list = (List<T>)getHISDBJdbcTemplate().queryForList(sql);
    	}
    	return list;
    }
    
    public static int queryForIntOMS(String sql) {
    	int i = 0;
    	if(CBSystemConstants.sqlUpperCase){
    		sql=changeSqltoUpperCase(sql);
		}
    	if(isInterfaceOpened){
//    		sql = DesUtil.encrypt(sql);
    		DBManagerService dBManagerService = (DBManagerService) BeanFactory.getBean("DBManagerService");
    		i = dBManagerService.queryForIntOMS(sql);
    	}else{
    		i = getOMSJdbcTemplate().queryForInt(sql);
    	}
    	return i;
    }
    
  /*  public static void executeLOC(String sql)
    {
    	getLOCJdbcTemplate().execute(sql);
    }
    
    public static <T> List<T> queryForListLOC(String sql) {
    	return getLOCJdbcTemplate().queryForList(sql);
    }*/
    
    public static void closeConnect(){
    	try {
    		if(isConnected){
    			getCZPDataSource().getConnection().close();
			}
		} catch (SQLException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
		}
    }
    public static String changeSqltoUpperCase(String sql){
//    	String str = sql.toLowerCase();
//    	if(str.contains("insert")){
//    		if(str.contains("values")){
//    			int index = str.indexOf("values");
//    			String str1 = sql.substring(0,index+6);
//    			String str2 = sql.substring(index+6);
//    			sql = str1.toUpperCase() + str2;
//    			
//    		}
//    	}
//    	String str = "this is 'Tom' and 'Eric'， this is 'Bruce lee', he is a chinese, name is '李小龙'。";
    	Pattern p1=Pattern.compile("\'(.*?)\'");
    	Matcher m = p1.matcher(sql);
    	StringBuilder stringBuilder = new StringBuilder();
    	List<String> list = new ArrayList<String>();
    	while (m.find()) {
    		list.add(m.group().trim().replace("\"","")+"");
    		//stringBuilder.append(m.group().trim().replace("\"","")+" ");
    	}
    	// System.out.println(stringBuilder.toString());
//    	System.out.println(list.toString());
    	sql=sql.toUpperCase();
    	for(String str:list){
    		sql= sql.replaceAll(str.toUpperCase(), str);
    	}

    	return sql;
    }
    
}

/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 系统操作获取相关数据信息类
 * 作    者 : 张俊
 * 开发日期 : 2008-07-18
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/

package czprule.system;

import java.sql.Connection;
import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.staticsql.OpeInfo;

/**
 * 
 * <AUTHOR>
 */
public final class PowerSystemDBOperator {
	private static Connection pocDeviceConn = null;

	private PowerSystemDBOperator() {
	}

	public static String getPowerStationNameByID(String powerStationID) {
//		return DBManager
//				.queryForString(
//						"SELECT STATION_NAME FROM "+CBSystemConstants.opcardUser+"T_e_SUBSTATION  WHERE STATION_ID=?",
//						powerStationID);
		//edit 2014.6.25
		return DBManager.queryForString(OPEService.getService().PowerSystemDBOperatorSql1(),powerStationID);
	}

	public static List getStationDeviceList(String powerStationID) {
		return DBManager.queryForList(OPEService.getService().getStationDeviceList(powerStationID));
	}

	public static List getStationProtectList(String powerStationID) {
//		return DBManager.queryForList(
//						"SELECT A.equipid,A.PROTECTID,A.PROTECTNAME,C.DEVICETYPEID,C.PROTECTTYPENAME,A.PROTECTSTATUS FROM "+CBSystemConstants.opcardUser+"T_A_PROTECTEQUIP A,"+CBSystemConstants.opcardUser+"T_E_EQUIPINFO B,"+CBSystemConstants.opcardUser+"T_A_PROTECTINFO C WHERE A.EQUIPID=B.EQUIP_ID AND A.PROTECTTYPEID=C.PROTECTTYPEID AND B.STATION_ID=?",powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getStationProtectList(powerStationID));
	}

	public static List getStationToplogy(String powerStationID) {
//		return DBManager.queryForList("SELECT A.CONNECTIVITYNODE_ID,B.EQUIP_ID,B.EQUIP_NAME,B.STATION_ID FROM "+CBSystemConstants.opcardUser+"T_e_EQUIPTERMINAL A join "+CBSystemConstants.opcardUser+"T_e_EQUIPINFO B on A.EQUIP_ID=B.EQUIP_ID where B.STATION_ID='"+powerStationID+"'");
		//edit 2014.6.23
		List list1= DBManager.queryForList(OPEService.getService().getStationToplogy(powerStationID));
		List list2= DBManager.queryForList(OPEService.getService().getStationToplogyLine(powerStationID));
		list1.addAll(list2);
		return list1;
	}
	
	public static List getOrganList() {
		//return DBManager.queryForList("select t.organid,t.organname,t.shortname from "+CBSystemConstants.opcardUser+"T_a_powerorgan t where t.isenabled='0'");
		List list = new ArrayList();
		try{
			list = DBManager.queryForList(OPEService.getService().OrganListSql());
		}
		catch(Exception ex) {
			
		}
		return list;
	}

	public static List getStationList() {
//		return DBManager
//				.queryForList("SELECT A.STATION_ID,A.STATION_NAME,A.STATION_FLAG,A.CIM_ID,B.VOLTAGE_CODE,A.STATION_GRAPH FROM "+CBSystemConstants.opcardUser+"T_e_SUBSTATION A,"+CBSystemConstants.opcardUser+"T_e_VOLTAGELEVEL B WHERE A.VOLTAGE_ID=B.VOLTAGE_ID");
		//edit 2014.6.25
		return DBManager.queryForList(OPEService.getService().PowerSystemDBOperatorSql2());
	}

	public static List getLineList() {
//		return DBManager
//				.queryForList("SELECT A.LINE_ID,A.LINE_CODE,A.LINE_NAME,A.CIM_ID,B.VOLTAGE_CODE,C.DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"T_e_LINEINFO A,"+CBSystemConstants.opcardUser+"T_e_VOLTAGELEVEL B,(SELECT D.LINE_ID,MIN(F.DEVICESTATUS) DEVICESTATUS FROM "+CBSystemConstants.opcardUser+"T_e_LINEINFO D,"+CBSystemConstants.opcardUser+"T_e_SUBSTATIONTOPOLOGY E,"+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO F WHERE D.LINE_ID=E.LINE_ID AND E.EQUIP_ID=F.EQUIPID GROUP BY D.LINE_ID) C WHERE A.VOLTAGE_ID=B.VOLTAGE_ID AND A.LINE_ISDEL=0 AND A.LINE_ID=C.LINE_ID");
		//edit 2014.6.26
		return DBManager.queryForList(OPEService.getService().PowerSystemDBOperatorSql());
	}
	
	public static List getFeederList() {
		return DBManager.queryForList(OPEService.getService().PowerFeederOperatorSql());
	}
	
	public static List getToplogyList() {
//		return DBManager.queryForList("SELECT T.LINE_ID,T.STATION_ID,T.EQUIP_ID FROM "+CBSystemConstants.opcardUser+"T_E_SUBSTATIONTOPOLOGY T where t.station_id is not null");
		//edit 2014.6.26
		return DBManager.queryForList(OPEService.getService().PowerSystemDBOperatorSql3());
	}

	public static List getEquipType() {
//		return DBManager
//				.queryForList("SELECT T.EQUIPTYPE_FLAG, T.EQUIPTYPE_NAME FROM "+CBSystemConstants.opcardUser+"T_e_EQUIPTYPE T WHERE T.EQUIPTYPE_ISDEL=0 AND T.EQUIPTYPE_FLAG IS NOT NULL AND T.CIM_ID IS NOT NULL ORDER BY T.EQUIPTYPE_ORDER");
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getEquipType());
	}

	public static List getSysTelemetering() {
//		return DBManager
//				.queryForList("select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"T_a_telemetering a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b,"+CBSystemConstants.opcardUser+"T_e_substationtopology c where a.equipid=b.equip_id and b.equip_id=c.equip_id");
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getSysTelemetering());
	}

	public static List getFacTelemetering(String powerStationID) {
//		return DBManager.queryForList(
//						"select a.equipid,a.meastype,a.measid,a.measvalue,a.measname from "+CBSystemConstants.opcardUser+"T_a_telemetering a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b,"+CBSystemConstants.opcardUser+"T_e_substation c where a.equipid=b.equip_id and b.station_id=c.station_id and c.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getFacTelemetering(),powerStationID);
	}
	//获取测量值
	public static List getMeasure(String equipID,String relatedType){
		return DBManager.queryForList("select t.measname,t.measvalue,t.meastype from "+CBSystemConstants.opcardUser+"T_a_telemetering t where t.equipid=?and t.meastype=?",equipID,relatedType);
	}

	public static List getDeviceState(String powerStationID) {
//		return DBManager.queryForList(
//						"select a.EQUIP_ID,a.EQUIP_STATE from "+CBSystemConstants.opcardUser+"T_e_equipstate a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b where a.equip_id=b.equip_id and b.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getDeviceState(),powerStationID);
	}

	public static void addActionNote(String tObjectID, String note, int note_x,
			int note_y) {
		DBManager
				.update(
						"insert into "+CBSystemConstants.opcardUser+"T_g_actionNOTE values(GRAPH.SEQ_ACTIONID.NEXTVAL,?,?,?,?)",
						tObjectID, note, note_x, note_y);
	}

	public static void removeActionNote(String tObjectID) {
		DBManager.update("delete from "+CBSystemConstants.opcardUser+"T_g_actionNOTE where tobjectid=?",
				tObjectID);
	}

	public static List getActionNote(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_g_actionnote a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getActionNote(),powerStationID);
	}

	public static void addActionGroundLine(String tObjectID,
			int groundline_type, int groundline_x, int groundline_y) {
		DBManager
				.update(
						"insert into "+CBSystemConstants.opcardUser+"T_g_actionGROUNDLINE values(GRAPH.SEQ_ACTIONID.NEXTVAL,?,?,?,?)",
						tObjectID, groundline_type, groundline_x, groundline_y);
	}

	public static void removeActionGroundLine(String tObjectID) {
		DBManager.update(
				"delete from "+CBSystemConstants.opcardUser+"T_g_actionGROUNDLINE where tobjectid=?",
				tObjectID);
	}

	public static List getActionGroundLine(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_g_actiongroundline a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getActionGroundLine(),powerStationID);
	}

	public static void addActionCard(String tObjectID, String card_type,
			int card_x, int card_y) {
		DBManager
				.update(
						"insert into "+CBSystemConstants.opcardUser+"T_g_actionCARD values(GRAPH.SEQ_ACTIONID.NEXTVAL,?,?,?,?)",
						tObjectID, card_type, card_x, card_y);
	}

	public static void removeActionCard(String tObjectID) {
		DBManager.update("delete from "+CBSystemConstants.opcardUser+"T_g_actionCARD where tobjectid=?",
				tObjectID);
	}

	public static List getActionCard(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_g_actioncard a,"+CBSystemConstants.opcardUser+"T_e_equipinfo b where a.tobjectid=b.equip_id and b.station_id=?",
//						powerStationID);
	//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getActionCard(),powerStationID);
	}

	public static List getStationRMDevice(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_a_removabledevice g where ? in  (select e.station_id from "+CBSystemConstants.opcardUser+"T_e_equipinfo e where e.equip_id=g.equipid) and unitcode='"+CBSystemConstants.unitCode+"'",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getStationRMDevice(),powerStationID,powerStationID);
	}

	public static void addStationGroundLine(String groundLineID,String equipID, String terminalID,
			String state,int type) {
		String sql="insert into  "+CBSystemConstants.opcardUser+"T_A_REMOVABLEDEVICE(removeid,equipid,terminalid,romvestatus,removetype,opcode) values(?,?,?,?,?,?)";
		DBManager.update(sql,
				groundLineID,equipID, terminalID, state,type,CBSystemConstants.opCode);
	}

	public static List getStationBZTDevice(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE a,"+CBSystemConstants.opcardUser+"T_A_ECSAUTRECORD b, "+CBSystemConstants.opcardUser+"T_e_equipinfo c,"+CBSystemConstants.opcardUser+"T_e_VOLTAGELEVEL v where c.VOLTAGE_ID=v.VOLTAGE_ID and a.device_type='1' and a.scs_id=b.scs_id and a.scs_obj_code=c.equip_id and c.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getStationBZTDevice(powerStationID));
	}
	
	public static List getStationRelateDevice(String powerStationID) {
//		return DBManager
//				.queryForList(
//						"select * from "+CBSystemConstants.opcardUser+"T_A_ECSAUTODEVICE t,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRELATE a,"+CBSystemConstants.opcardUser+"T_A_ECTRIPRECORD b, "+CBSystemConstants.opcardUser+"T_e_equipinfo c,"+CBSystemConstants.opcardUser+"T_e_VOLTAGELEVEL v where c.VOLTAGE_ID=v.VOLTAGE_ID and t.scs_id=a.scs_id and a.relation_id=b.device_id and a.scs_obj=c.equip_id and c.station_id=?",
//						powerStationID);
		//edit 2014.6.23
		return DBManager.queryForList(OPEService.getService().getStationRelateDevice(),powerStationID,powerStationID);
	}

}

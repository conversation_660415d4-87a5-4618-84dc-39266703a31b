package czprule.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import org.springframework.jdbc.support.rowset.SqlRowSet;

import com.tellhow.czp.Robot.ObjectRowMapper;
import com.tellhow.graphicframework.model.PowerDevice;

//import czprule.model.PowerDevice;

public interface DBManagerService {

	public  <T> List<T> query(Class clazz, String sql);
	
	public  Object queryForObject(String sql,ObjectRowMapper orm);
	
	public  List query(String sql);
	
	public  Map<String, Object> queryForMap(String sql, Object...params);
	
	public  int queryForInt(String sql);
	
	public  int queryForInt(String sql, Object...params);
	
	public  String queryForString(String sql);
	
	public  String queryForString(String sql, Object...params);
	
	public  Object queryForObject(Class requiredType, String sql, Object...params);
	
	public  long queryForLong(String sql, Object...params);
	
	public  <T> List<T> queryForList(Class requiredType, String sql, Object...params);
	
	public  <T> List<T> queryForList(String sql, Object...params);
	
	public  <T> List<T> queryForList(String sql);
	
	public  <T> SqlRowSet queryForRowSet(String sql);
	
	public  <T> List<T> queryMetaData(String sql);
	
	public  int update(String sql, Object...params);
	
	public  int[] update(String sql, final List parameterList);
	
	public  void batchUpdate(String[] sql);
	
	public  void execute(String sql);
	public  void execute(String sql,final String ret);
	public  int update(String sql);
	
	public  void executeOMS(String sql);
	
	public  <T> List<T> queryForListOMS(String sql);
	public  <T> List<T> queryForListOMS(String sql, Object...params);
	<T> List<T> queryForListMySql(String sql);

	public  <T> List<T> queryForListEMS(String sql);
	
	public  <T> List<T> queryForListHISDB(String sql);

	public  int queryForIntOMS(String sql);

	//查询厂站设备缓存
	public  HashMap<String,PowerDevice> queryDevForStId(String stationId);
	//查询厂站设备缓存
	public  HashMap<String,HashMap<String, PowerDevice>> queryDevMapForStIdList(List<String> stationIdList);
	
	//{设备ID,设备关蝔连接点ID}
	public HashMap<String,ArrayList<String>> getMapToplogyDevPointForStId(String stationId);
	//{坘电站ID,{设备ID,设备关蝔连接点ID}}
	public HashMap<String,HashMap<String,ArrayList<String>>> getMapToplogyDevPointForStIdList(List<String> stationIdList);
	
	//{连接点ID,连接点关蝔设备ID}
	public HashMap<String,ArrayList<String>> getMapToplogyPointDevForStId(String stationId);
	//{坘电站ID,{连接点ID,连接点关蝔设备ID}} 
	public HashMap<String,HashMap<String,ArrayList<String>>> getMapToplogyPointDevForStIdList(List<String> stationIdList);
	
	//{连接了毝线的连接点集坈}
	public List<String> getMotherlinePointForStId(String stationId);
	//{坘电站ID,连接了毝线的连接点集坈}
	public HashMap<String,ArrayList<String>> getMotherlinePointForStIdList(List<String> stationIdList);

	
}

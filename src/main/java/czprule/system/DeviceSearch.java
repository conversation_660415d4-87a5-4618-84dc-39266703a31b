package czprule.system;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.model.PowerDevice;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-4 下午02:11:24 
 */
public class DeviceSearch{

	private String mapType; //搜索类型,分全网和厂站,必需
	private String powerStationID; //厂站ID,mapType是厂站时必需
	private String lineID;
	private String searchStr; //查询字符串,和设备名称模糊匹配,可选
	private String deviceType; //设备类型,可选
	
	public void execute(Map inParaMap, Map outParaMap) {
		// TODO Auto-generated method stub
		initParams(inParaMap);
		ArrayList<PowerDevice> resultList = new ArrayList<PowerDevice>();
		ArrayList<PowerDevice> deviceList = new ArrayList<PowerDevice>();
        if(mapType.equals(SystemConstants.MAP_TYPE_SYS))
        {
        	deviceList.addAll(CBSystemConstants.getMapPowerStation().values());
        	deviceList.addAll(CBSystemConstants.getMapPowerLine().values());
        	if(CBSystemConstants.roleCode.equals("1")) {
	        	for(Iterator iter = CBSystemConstants.getMapPowerStationDevice().values().iterator();iter.hasNext();) 
	    		{
	        		deviceList.addAll(((HashMap<String, PowerDevice>)iter.next()).values());
	    		}
        	}
        }
        else if(mapType.equals(SystemConstants.MAP_TYPE_FAC))
        {
        	HashMap<String, czprule.model.PowerDevice> map = CBSystemConstants.getStationPowerDevices(powerStationID);
        	if(map!=null){
        	   deviceList.addAll(map.values());
        	}
        }
        else if(mapType.equals(SystemConstants.MAP_TYPE_LINE))
        {
        	HashMap<String, czprule.model.PowerDevice> map = CBSystemConstants.getStationPowerDevices(powerStationID);
        	if(map!=null){
         	   deviceList.addAll(map.values());
         	}
        }
        
        for(Iterator iter = deviceList.iterator();iter.hasNext();) 
		{
    		PowerDevice pd = (PowerDevice)iter.next();
    		if(searchStr.equals("") || pd.getPowerDeviceName().indexOf(searchStr) != -1)
    		{
    			if(deviceType.equals("") || deviceType.equals(pd.getDeviceType()))
    				resultList.add(pd);
    		}
		}
        outParaMap.put("resultList", resultList);
	}

	public void initParams(Map inParaMap) {
		mapType = (String) inParaMap.get("MapType");
		searchStr = "";
		deviceType = "";
		if (inParaMap.get("searchStr") != null && !inParaMap.get("searchStr").toString().equals("")) {
			searchStr = inParaMap.get("searchStr").toString().trim();
        }
        if (inParaMap.get("powerStationID") != null && !inParaMap.get("powerStationID").toString().equals("")) {
        	powerStationID = inParaMap.get("powerStationID").toString().trim();
        }
        if (inParaMap.get("deviceType") != null && !inParaMap.get("deviceType").toString().equals("")) {
        	deviceType = inParaMap.get("deviceType").toString().trim();
        }
	}
}

package czprule.system;

import java.awt.geom.Point2D;
import java.io.File;
import java.io.IOException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import org.apache.batik.dom.svg.SAXSVGDocumentFactory;
import org.apache.batik.util.XMLResourceDescriptor;
import org.w3c.dom.Element;
import org.w3c.dom.svg.SVGDocument;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.svg.document.DefaultSVGDocumentResolver;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceOffOnAction;
import com.tellhow.graphicframework.algorithm.ElementSearch;
import com.tellhow.graphicframework.basic.SVGFile;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.SVGCanvasPanel;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.DOMUtil;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationmodel.DrawRemovableDevice;
import czprulepw.PWSystemConstants;

public class DeviceSVGPanelUtil {

	/**
	 * 设置设备SVG开合颜色
	 * 
	 * @param pd
	 */
	public static void setDeviceSVGColor(final PowerDevice pd) {

		if (pd == null)
			return;
		String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
		
		
		// 全网线路
		if (pd.getDeviceType().equals(SystemConstants.InOutLine)) {
			List<PowerDevice> lineList = RuleExeUtil.getLineAllSideList(pd);
			
			for(PowerDevice line : lineList){
				if (line.getDeviceStatus().equals("0")) {
					SvgAction action = new ChangeColorAction(line,SystemConstants.getMapColor().get(powerVolt),"none");
					action.execute();
				} else {
					SvgAction action = new ChangeColorAction(line,
							SystemConstants.getMapColor().get(
									SystemConstants.LOSE_COLOR_CODE),
							"none");
					action.execute();
				}
				DeviceSVGPanelUtil dsu = new DeviceSVGPanelUtil();
				dsu.getPowerDeviceXY(line);
			}
		}else if (SystemConstants.SwitchSeparate.equals(pd.getDeviceType())) {
			if (pd.getDeviceStatus().equals("0")) {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_ON);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(powerVolt), SystemConstants
						.getMapColor().get(powerVolt));
				action.execute();
			} else {
				// 开关的断开svg显示效果
				
				List<PowerDevice> pdl = RuleExeUtil.getDeviceDirectList(pd,
						SystemConstants.SwitchSeparate);
				pdl.add(pd);
				List<PowerDevice> epdl = new ArrayList<PowerDevice>();
				List<PowerDevice> nepdl = new ArrayList<PowerDevice>();
				for (int i = 0; i < pdl.size(); i++) {
					PowerDevice pde = pdl.get(i);
					if (!pde.getDeviceStatus().equals("0")) {
						epdl.add(pde);
					} else {
						nepdl.add(pde);
					}
				}

				for (int i = 0; i < epdl.size(); i++) {
					SvgAction action = new ChangeDeviceOffOnAction(epdl.get(i),
							SvgAction.SWITCH_OFF);
					action.execute();
					action = new ChangeColorAction(epdl.get(i),
							SystemConstants.getMapColor().get(
									SystemConstants.LOSE_COLOR_CODE), "none");
					action.execute();
				}
				for (int i = 0; i < nepdl.size(); i++) {
					SvgAction action = new ChangeDeviceOffOnAction(
							nepdl.get(i), SvgAction.SWITCH_ON);
					action.execute();
					action = new ChangeColorAction(nepdl.get(i),
							SystemConstants.getMapColor().get(powerVolt),
							SystemConstants.getMapColor().get(powerVolt));
					action.execute();
				}
			}
		} else if (SystemConstants.SwitchFlowGroundLine.equals(pd
				.getDeviceType())) {
			if (pd.getDeviceStatus().equals("0")) {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_ON);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
						"none");
				action.execute();
			} else {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_OFF);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(powerVolt), SystemConstants
						.getMapColor().get(powerVolt));
				action.execute();
			}
		} else if (SystemConstants.Switch.equals(pd.getDeviceType())) {
			dropErroGroundline(pd);
			if (pd.getDeviceStatus().equals("0")) {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_ON);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(powerVolt), SystemConstants
						.getMapColor().get(powerVolt));
				action.execute();
			} 
			else if (pd.getDeviceStatus().equals("1")) {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_HOT);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(powerVolt),
						"none");
				action.execute();
			}else {
				SvgAction action = new ChangeDeviceOffOnAction(pd,
						SvgAction.SWITCH_OFF);
				action.execute();
				action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
						"none");
				action.execute();
			}
			
			
  			
//			final DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
//			int count = 0;
//			for (Map.Entry<Integer, DispatchTransDevice> entry : CBSystemConstants.getChzDtdMap().entrySet()) {
//				DispatchTransDevice values = entry.getValue();
//				if (values.getTransDevice().getPowerDeviceID().equals(pd.getPowerDeviceID())) {
//					final String endStatus = values.getEndstate();
//					Runnable r = new Runnable() {
//				        public void run() {
//				        	if (endStatus.equals("0"))
//								svgResolver.addChildCircle(pd, "重合闸运行");
//							else if (endStatus.equals("1"))
//								svgResolver.removeChildNode(pd, "重合闸运行");
//				        }
//					};
//					SVGCanvasPanel curpanel = SystemConstants.getGuiBuilder().getActivateSVGPanel();
//					UpdateManager updateManager = curpanel.getSvgCanvas().getUpdateManager();
//					if(updateManager != null)
//						updateManager.getUpdateRunnableQueue().invokeLater(r);
//					else
//						r.run();
//					
//					count++;
//				}
//			}
//			if(count == 0){
//				String deviceId = pd.getPowerDeviceID();
//				TicketDBManager ticketDB = new TicketDBManager();
//				Map<String, PowerDevice> powerDeviceTemp = CBSystemConstants.getMapPowerStationDevice().get(pd.getPowerStationID());
//				PowerDevice pdTemp = powerDeviceTemp.get(deviceId);
//				// 根据重合闸当前状态使其在SVG文件中的图元恢复到初始样式
//				int cczStatuc = ticketDB.getCCZID(deviceId);
//				if (cczStatuc == 0) {
//					svgResolver.addChildCircle(pdTemp, "重合闸运行");
//				} else
//					svgResolver.removeChildNode(pdTemp, "重合闸运行");
//			}
			
			DeviceSVGPanelUtil dsu = new DeviceSVGPanelUtil();
			dsu.getPowerDeviceXY(pd);
		} else if (SystemConstants.PowerTransformer.equals(pd.getDeviceType())) {
			if (pd.getDeviceStatus().equals("0")) {
				SvgAction action = new ChangeColorAction(pd, "", "none");
				action.execute();
			} else {
				SvgAction action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
						"none");
				action.execute();
			}
			DeviceSVGPanelUtil dsu = new DeviceSVGPanelUtil();
			dsu.getPowerDeviceXY(pd);
		} else if (SystemConstants.RemovableDevice.equals(pd.getDeviceType())) {
			
//			new DrawRemovableDevice().execute(pd.getPowerStationID(),
//					pd.getDevice(), pd.getKnife(), pd.getDeviceStatus(),
//					pd.getRmType());
			new DrawRemovableDevice().execute(pd);
			
		} 
		else if ("ACLineSegmentPW".equals(pd.getDeviceType())) {
			
		}
		else if (PWSystemConstants.PWTypePDBYQ.equals(pd.getDeviceType())) {
			
		}
		else {
			if (pd.getDeviceStatus().equals("0")) {
				SvgAction action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(powerVolt), "none");
				action.execute();
			} else {
				SvgAction action = new ChangeColorAction(pd, SystemConstants
						.getMapColor().get(SystemConstants.LOSE_COLOR_CODE),
						"none");
				action.execute();
			}
			
			DeviceSVGPanelUtil dsu = new DeviceSVGPanelUtil();
			dsu.getPowerDeviceXY(pd);
			if (SystemConstants.InOutLine.equals(pd.getDeviceType()))
				dropErroGroundline(pd);
		}
		 if(CBSystemConstants.isjxSign){
			   try {
			    DefaultSVGDocumentResolver svgResolver = new DefaultSVGDocumentResolver();
			    if(pd.getDeviceType().equals(SystemConstants.Switch)||
			      pd.getDeviceType().equals(SystemConstants.PowerTransformer)||
			      pd.getDeviceType().equals(SystemConstants.InOutLine)
			      ||pd.getDeviceType().equals(SystemConstants.VolsbTransformer)
			      ||pd.getDeviceType().equals(SystemConstants.MotherLine)||pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			     if(pd.getDeviceStatus().equals("3")){
			      if(CBSystemConstants.jxDtd){
			       if(pd.getDeviceType().equals(SystemConstants.Switch)
			         ||pd.getDeviceType().equals(SystemConstants.PowerTransformer)
			         ||pd.getDeviceType().equals(SystemConstants.VolsbTransformer)||pd.getDeviceType().equals(SystemConstants.SwitchSeparate)){
			      
			          svgResolver.addChildCircle(pd,"检修");//向SVG图中添加检修标签
			       }if((pd.getDeviceType().equals(SystemConstants.InOutLine)
			         &&pd.getPowerVoltGrade()>35)||pd.getDeviceType().equals(SystemConstants.MotherLine)
			         ){
			        svgResolver.addChildXL(pd,"检修");//向SVG图中添加检修标签
			       }
			      }
			     }    
			     else{
			      svgResolver.removeChildNode(pd,"检修");
			      if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
			       List<PowerDevice> ortherXL = RuleExeUtil.getLineOtherSideList(pd);
			       for(PowerDevice xl:ortherXL){
			        svgResolver.removeChildNode(xl,"检修");
			       }
			      }
			     }
			    }
			   } catch (Exception e) {
			    // TODO: handle exception
			   }
			   
		}
	}

	/**
	 * 改变设备SVG开合颜色
	 * 
	 * @param pd
	 */
	public static void changeDeviceSVGColor(PowerDevice pd) {
		if(!CBSystemConstants.isCurrentSys){
			return;
		}
		if (pd == null)
			return;
		String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
		
		setDeviceSVGColor(pd);
		// 接地刀闸搜索
		if (!CBSystemConstants.isGroundDisconnectorExist
				&& !pd.getPowerStationID().equals("")
				&& !pd.getDeviceType().equals(SystemConstants.SwitchSeparate)
				&& !pd.getDeviceType().equals(
						SystemConstants.SwitchFlowGroundLine)) {
			Element group = SVGDocumentResolver.getResolver()
					.getDeviceGroupElement(pd);
			if (group != null) {
				ElementSearch es = new ElementSearch();
				Map inMap = new HashMap();
				Map outMap = new HashMap();
				inMap.put("oprSrcElement", group);
				inMap.put("searchType", "LinkEquipElement");
				es.execute(inMap, outMap);
				ArrayList<Element> elementList = (ArrayList<Element>) outMap
						.get("ElementList");
				for (int k = 0; k < elementList.size(); k++) {
					Element element = elementList.get(k);
					if (element!=null && element.getParentNode().getAttributes()
							.getNamedItem("id")!=null && element.getParentNode().getAttributes()
							.getNamedItem("id").getNodeValue()
							.indexOf("GroundDisconnector") >= 0) {
						String linkEquipID = SVGDocumentResolver.getResolver()
								.getDeviceID(element);
						if (!linkEquipID.equals(""))
							break;

						boolean isLinkLine = false;
						Map in = new HashMap();
						Map out = new HashMap();
						in.put("oprSrcElement", element);
						in.put("searchType", "LinkEquipElement");
						es.execute(in, out);
						ArrayList<Element> eleList = (ArrayList<Element>) out
								.get("ElementList");
						for (int l = 0; l < eleList.size(); l++) {
							Element ele = eleList.get(l);
							String equipID = SVGDocumentResolver.getResolver()
									.getDeviceID(ele);
							if (!equipID.equals("")) {
								PowerDevice devive = CBSystemConstants
										.getPowerDevice(pd.getPowerStationID(),
												equipID);
								if (devive != null && devive.getDeviceType().equals(SystemConstants.InOutLine)) {
									isLinkLine = true;
									break;
								}
							}
						}
						if (isLinkLine)
							break;

						if (pd.getDeviceStatus().equals("3")) {
							SvgAction action = new ChangeDeviceOffOnAction(
									element, SvgAction.SWITCH_ON);
							action.execute();
							action = new ChangeColorAction(element,
									SystemConstants.getMapColor()
											.get(powerVolt), SystemConstants
											.getMapColor().get(powerVolt));
							action.execute();
						} else {
							SvgAction action = new ChangeDeviceOffOnAction(
									element, SvgAction.SWITCH_OFF);
							action.execute();
							action = new ChangeColorAction(element,
									SystemConstants.getMapColor().get(
											SystemConstants.LOSE_COLOR_CODE),
									"none", false);
							action.execute();
						}
					}
				}
			}
		}
    	
	}

	/**
	 * 打开变电站SVG图
	 * 
	 * @param stationID
	 *            变电站ID
	 */
	public static void openSVGPanel(String stationID) {
		if (stationID == null || "".equals(stationID))
			return;
		String stationName = "";
		String fileName = "";
		String filePath = "";
		List<SVGFile> SVGFiles = SystemConstants
				.getSVGFileByStationID(stationID);
		if(SVGFiles.size()!=1){
      		 CZPService.getService().filterMap(SVGFiles,stationID);
		}
		if (SVGFiles.size() > 1) {
			Object[] options = SVGFiles.toArray();
			for (int i = 0; i < options.length; i++) {
				if (SystemConstants.getGuiBuilder().isTabbedPageExist(
						SVGFiles.get(i).getFilePath()))
					return;
			}
			int i = JOptionPane.showOptionDialog(
					SystemConstants.getMainFrame(), "选择要打开的图形",
					SystemConstants.SYSTEM_TITLE, JOptionPane.DEFAULT_OPTION,
					JOptionPane.WARNING_MESSAGE, null, options, options[0]);
			if (i == -1)
				fileName = SVGFiles.get(0).getFileName();
			else
				fileName = SVGFiles.get(i).getFileName();
		} else if (SVGFiles.size() == 0) {
			if (SystemConstants.threadLoadFile != null
					&& SystemConstants.threadLoadFile.isAlive())
				JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),
						"接线图正在加载中，请稍后打开！", SystemConstants.SYSTEM_TITLE,
						JOptionPane.WARNING_MESSAGE);
			else
				JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),
						"不存在该接线图！", SystemConstants.SYSTEM_TITLE,
						JOptionPane.WARNING_MESSAGE);
			return;
		} else {
			fileName = SVGFiles.get(0).toString();
		}
		filePath = SystemConstants.FILE_SVGMAP_PATH + fileName;

		PowerDevice pd = CBSystemConstants.getPowerStation(stationID);
		stationName = pd.getPowerDeviceName();
		CreatePowerStationToplogy.createSVGPanel(stationID, stationName,
				fileName, filePath);

	}

	/**
	 * 打开变电站SVG图 ,通过传入的设备ID过滤厂站
	 * 
	 * @param stationID
	 *            变电站ID
	 */
	public static void openSVGPanel(String stationID, String equipID) {
		openSVGPanel(stationID, equipID, true);
	}

	public static SVGCanvasPanel openSVGPanel(String stationID, String equipID,
			boolean isActive) {
		if (stationID == null || "".equals(stationID))
			return null;
		String stationName = "";
		String fileName = "";
		String filePath = "";
		List<SVGFile> SVGFiles = SystemConstants
				.getSVGFileByStationID(stationID);

		if (SVGFiles.size() >= 1) {
			for (Iterator<SVGFile> it = SVGFiles.iterator(); it.hasNext();) {
				filePath = it.next().getFilePath();
				if (SystemConstants.getGuiBuilder().isTabbedPageExist(filePath))
					return null;
			}
			for (Iterator<SVGFile> it = SVGFiles.iterator(); it.hasNext();) {
				filePath = it.next().getFilePath();
				String parser = XMLResourceDescriptor.getXMLParserClassName();
				SAXSVGDocumentFactory factory = new SAXSVGDocumentFactory(
						parser);
				SVGDocument doc = null;
				try {
					File file = new File(filePath);
					doc = factory.createSVGDocument(file.toURI().toString());
				} catch (IOException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
//				if (doc != null) {
//					Element element = SVGDocumentResolver.getResolver()
//							.getDeviceGroupElement(doc, equipID);
//					if (element == null) {
//						it.remove();
//						continue;
//					}
//				}
			}
			if(SVGFiles.size()!=1){
       		 CZPService.getService().filterMap(SVGFiles,stationID);
			}
			if (SVGFiles.size() > 1) {
				Object[] options = SVGFiles.toArray();
				int i = JOptionPane.showOptionDialog(
						SystemConstants.getMainFrame(), "选择要打开的图形",
						SystemConstants.SYSTEM_TITLE,
						JOptionPane.DEFAULT_OPTION,
						JOptionPane.WARNING_MESSAGE, null, options, options[0]);
				if (i == -1)
					fileName = SVGFiles.get(0).getFileName();
				else
					fileName = SVGFiles.get(i).getFileName();
			} else if (SVGFiles.size() == 0) {
				return null;
			} else
				fileName = SVGFiles.get(0).toString();
		} else if (SVGFiles.size() == 1)
			fileName = SVGFiles.get(0).toString();
		else {
			CreatePowerStationToplogy.loadFacData(stationID);
			return null;
		}

		filePath = SystemConstants.FILE_SVGMAP_PATH + fileName;

		PowerDevice st = CBSystemConstants.getPowerStation(stationID);
		if(st.getPowerStationName().indexOf(".") > 0)
			stationName =st.getPowerDeviceName();
		else
			stationName =CZPService.getService().getDevName(st);
		return CreatePowerStationToplogy.createSVGPanel(stationID, stationName,
				fileName, filePath, isActive);
	}

	private void getPowerDeviceXY(PowerDevice pd) {
		
		if(pd==null||SVGDocumentResolver.getResolver().getDeviceGraphElement(pd)==null){
			return;
		}
		Point2D.Double point = DOMUtil.getElementStartPoint(SVGDocumentResolver
				.getResolver().getDeviceGraphElement(pd));
		if (point == null) {
			return;
		}
		SvgAction action = null;
		boolean isHidder = true;
		if ("3".equals(pd.getDeviceStatus())) {
			isHidder = true;
		} else {
			isHidder = false;
		}

	}
	
	public static void dropErroGroundline(PowerDevice pd){
		
		List<PowerDevice> groundline_list = CBSystemConstants.getGroundLineByStationAndDevice(pd.getPowerStationID(), pd.getPowerDeviceID());
		if(groundline_list.size()!=0){
			for (PowerDevice ground_line : groundline_list) {
				String station_id=ground_line.getPowerStationID();
				String main_device_id=ground_line.getDevice();
				String connect_knife_id=ground_line.getKnife();
//				if(!pd.getDeviceStatus().equals("3")){
//					if(ground_line.getDeviceStatus().equals("0")){
//						//拆地线
//						new DrawRemovableDevice().execute(station_id, main_device_id, connect_knife_id, "1", PowerDevice.GROUNDLINE);
//					}
//				}
//				new DrawRemovableDevice().execute(station_id, main_device_id, connect_knife_id, ground_line.getDeviceStatus(), PowerDevice.GROUNDLINE);
				
			}
		}
	}
	
	/**
	 * 拓扑着色
	 * 
	 * @param pd
	 */
	public static void setToplogySVGColor(final String stationID) {
		
	
				//long start=System.currentTimeMillis();
				Map<String, PowerDevice> deviceMap=(Map<String, PowerDevice>)CBSystemConstants.getMapPowerStationDevice().get(stationID);
				
				for (Iterator<PowerDevice> iter = deviceMap.values().iterator(); iter.hasNext();) {
					PowerDevice dev=  iter.next();
					PWSystemConstants.deviceSourceMap.remove(dev);
					
					if(!dev.getDeviceType().equals("ACLineSegmentPW") && !dev.getDeviceType().equals(PWSystemConstants.PWTypePDBYQ) && !dev.getDeviceType().equals(SystemConstants.MotherLine))
						continue;
					
					if(dev.getDeviceType().equals(SystemConstants.MotherLine)&&dev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
						continue;
					}
					
					boolean isOn = false; //是否带电
					boolean isNormal = false; //是否正常电源供电
					int sourceCount = 0; //电源数量
					
					HashMap<PowerDevice, ArrayList<PowerDevice>> devListMap = RuleExeUtil.getPathByDevice(dev, null, SystemConstants.Switch, null,PWSystemConstants.PWRunTypeSwitchZX,null, false, false, false, true);
					
					for(PowerDevice kg : devListMap.keySet()) {
						if(kg.getDeviceStatus().equals("0")) {
							isOn = true;
							//生成供电路径
							if(!PWSystemConstants.deviceSourceMap.containsKey(dev)) {
								PWSystemConstants.deviceSourceMap.put(dev, new ArrayList<ArrayList<PowerDevice>>());
							}
							ArrayList<PowerDevice> pathList = devListMap.get(kg);
							for(Iterator<PowerDevice> it = pathList.iterator();it.hasNext();) {
								PowerDevice path = it.next();
								if(path.getDeviceType().equals("ACLineSegmentPW"))
									it.remove();
							}
							PWSystemConstants.deviceSourceMap.get(dev).add(pathList);
							//根据主线开关找线路ID
							if(kg.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)) {
								String lineID = "";
								List<PowerDevice> linkdevList = RuleExeUtil.getDeviceDirectList(kg, "");
								for(PowerDevice linkdev  : linkdevList) {
									if(linkdev.isPW()) {
										lineID = linkdev.getPowerStationID();
										break;
									}
								}
								if(lineID.equals("")) {
									for(PowerDevice linkdev  : linkdevList) {
										List<PowerDevice> linklinkdevList = RuleExeUtil.getDeviceDirectList(linkdev, "");
										for(PowerDevice linklinkdev  : linklinkdevList) {
											if(linklinkdev.isPW()) {
												lineID = linklinkdev.getPowerStationID();
												break;
											}
										}
									}
								}
								if(lineID.equals(stationID))
									isNormal = true;
							}
								
							sourceCount++;
						}
					}
					String color = "";
					if(isOn) {
						if(isNormal) {
							if(sourceCount >= 2) {
								color = SystemConstants.getMapColor().get("-2"); //正常供电且2个及2个以上电源点
							}
							else {
								String powerVolt = String.valueOf((int) dev.getPowerVoltGrade()); //正常电源且2个以下电源点
								color = SystemConstants.getMapColor().get(powerVolt);
							}
						}
						else {//非正常供电就是紫色
							color = SystemConstants.getMapColor().get("-3"); //其他电源供电
						}
					}
					else {
						color =  SystemConstants.getMapColor().get(SystemConstants.LOSE_COLOR_CODE);
					}
					SvgAction action = new ChangeColorAction(dev, color, color);
					action.execute();
				}
					    	
				//long end=System.currentTimeMillis();
	    		//System.out.println("消耗的时间是:"+(end-start)+"ms");

			


		
		
		
	}

}

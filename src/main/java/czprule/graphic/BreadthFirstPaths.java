package czprule.graphic;

import java.util.*;


import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.*;
import czprule.model.PowerDevice;
import czprule.model.Queue;
import czprule.model.Stack;


/**
 * <AUTHOR>
 *
 *
 */

public class BreadthFirstPaths {
	/*private boolean [] marked;
	private int [] edgeTo;*/
	private TreeMap <Integer,Integer> prev ;
	private TreeMap <Integer,Integer> dist ;
	private SymbolGraph G;
	private int  s;//id 的转义
	private final String SWITCH_ID = "Breaker";
	private final String NeedAttentionType = "BreakerDisconnector";
//	private final String NeedAttentionType = "Breaker";
	private final String MATHER_LINE = "BusbarSection";
	private final String TRANSFORMER = "DistributionPowerTransform";


	@SuppressWarnings("static-access")
	public BreadthFirstPaths(SymbolGraph Graph){
		this.G = Graph;
	}

	/**
	 *
	 * @param zxSwitch_id，任何一个有拓扑关系的设备id,返回连接最近的配网设备,所在线路id
	 * @return 注意 没找到是返回Null,而不是空字符串。
	 */

	public String getLineId(String zxSwitch_id){
		if( G.index(zxSwitch_id) ==-1){
			return null;
		}
		this.s = G.index(zxSwitch_id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice ){
						if(((PowerDevice)G.nodeInfo.get(w)).isPW()){
							if(((PowerDevice)G.nodeInfo.get(w)).getPowerStationID()!=null&&!"".equals(((PowerDevice)G.nodeInfo.get(w)).getPowerStationID())){
								return ((PowerDevice)G.nodeInfo.get(w)).getPowerStationID();
							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}

					}
					queue.enqueue(w);
				}
			}
		}
		return null;
	}
	/**
	 *
	 * @param lineId ,如果是线路id的话，返回该线路通过出线开关，能访问的所有线路。如果是奇奇怪怪的id至少会返回本身。（传入station_id，就会返回本身）,
	 * @return HashMap<String ,HashSet<String>> key 有两个一个line,一个station
	 */
	public HashMap<String ,HashSet<String>> getLineAndStationIds(String lineId){
		HashMap<String, HashSet<String>> retMap = new HashMap<String, HashSet<String>>();
		HashSet<String> lineSet = new HashSet<String>();
		HashSet<String> stationSet = new HashSet<String>();
		//tempLineSet 用来记录一个端点，旁边连接的设备 line集合。假定A线设备，和B线设备公用一个端点。 假如集合大小大于等于2.说明时分解点。如果不包含输入的lineId，说明找到了第三层。程序也就仅找第三层。
		HashSet<String> tempLineSet = new HashSet<String>();
		retMap.put("line", lineSet);
		retMap.put("station", stationSet);
		lineSet.add(lineId);
		stationSet.add(lineId);
		if(InitLine.contains(lineId)&&InitLine.line_info.get(InitLine.index(lineId)).getSwitchID()!=null){
			String zwSwitchId = InitLine.line_info.get(InitLine.index(lineId)).getSwitchID();
			this.s= G.index(zwSwitchId);
			if(this.s==-1){
				return retMap;
			}
			stationSet.add(((PowerDevice)G.nodeInfo.get(s)).getPowerStationID());
			//prev 记录了遍历的路径
			prev = new TreeMap<Integer, Integer>();
			TreeMap <String,String> LinePrev = new  TreeMap<String, String>();
			//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
			dist = new TreeMap<Integer, Integer>();
			dist.put(s, 0);
			//将起点进入队列
			Queue<Integer> queue = new Queue<Integer>();
			queue.enqueue(s);
			PowerDevice tempPowerDevice;
			Boolean containMx = false;
			Boolean containInitLine = false;
			//开始基于队列结构的宽度优先算法。
			while(!queue.isEmpty()){
				int v = queue.dequeue();
				for(int w : G.G().adj(v)){
					if (!dist.containsKey(w)) {
						dist.put(w, 1 + dist.get(v));
		                prev.put(w, v);
						if(G.nodeInfo.get(w) instanceof PowerDevice){
//							tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
//							if(tempPowerDevice.isPW()){
//								if(!lineSet.contains(tempPowerDevice.getPowerStationID())){
//									lineSet.add(tempPowerDevice.getPowerStationID());
//									if(lineSet.size()>100){
//										break;
//									}
//								}
//							}else{
//								if(!stationSet.contains(tempPowerDevice.getPowerStationID())){
//									stationSet.add(tempPowerDevice.getPowerStationID());
//								}
//							}
						}else{
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							containInitLine = false;
							tempLineSet.clear();
							for(int x : G.G().adj(w)){
								tempPowerDevice = (PowerDevice)G.nodeInfo.get(x);
								if(MATHER_LINE.equals((tempPowerDevice).getDeviceType()) &&(! (tempPowerDevice).isPW())){
									containMx = true;
									break;
								}

							}
							for(int x : G.G().adj(w)){
								tempPowerDevice = (PowerDevice)G.nodeInfo.get(x);
								tempLineSet.add(tempPowerDevice.getPowerStationID());
							}
							lineSet.addAll(tempLineSet);
							if(containMx){
								continue;
							}
							if(tempLineSet.size()>1&&(!tempLineSet.contains(lineId))){
								continue;
							}
						}
						queue.enqueue(w);
					}
				}
			}
			for(String line :lineSet){
				if(InitLine.contains(line))
				stationSet.add(((PowerDevice)G.nodeInfo.get(G.index(InitLine.line_info.get(InitLine.index(line)).getSwitchID()))).getPowerStationID());
			}
		}

		return retMap;
	}

	public List<PowerDevice> getDeviceList(PowerDevice pd,
										   PowerDevice excDev, String tagTypes, String excTypes,
										   String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
										   boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {

		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pd.getPowerDeviceID());
		if(this.s==-1){
			return retList;
		}
		//设置断点
		int stopPoint = -1;//这是一共不存在的坐标
		if(!(excDev==null || G.index(excDev.getPowerDeviceID())==null)){
			stopPoint = G.index(excDev.getPowerDeviceID());
		}

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							retList.add(tempPowerDevice);
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}
						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}

							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}

						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){


								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									if(tagTypeList.contains(MATHER_LINE)){
										retList.add((PowerDevice)G.nodeInfo.get(x));
									}
									//x!=s 表示起点设备是母线，防止啥也搜
									if(x!=s){
										containMx = true;
									}
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。  //排查A设备也是要能返回设备，优先返回，后排查，所以continue项都往后挪一下位置。
					if(w==stopPoint){
						continue;
					}

					queue.enqueue(w);
				}
			}
		}
		return retList;
	}

	public List<PowerDevice> getDeviceList(String id,
										   PowerDevice excDev, String tagTypes, String excTypes,
										   String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
										   boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType) {

		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(id);
		if(this.s==-1){
			return retList;
		}
		//设置断点
		int stopPoint = -1;//这是一共不存在的坐标
		if(!(excDev==null || G.index(excDev.getPowerDeviceID())==null)){
			stopPoint = G.index(excDev.getPowerDeviceID());
		}

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {


					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							retList.add(tempPowerDevice);
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}

							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}

						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){
								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									containMx = true;
									if(tagTypeList.contains(MATHER_LINE)){
										retList.add((PowerDevice)G.nodeInfo.get(x));
									}
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。
					if(w==stopPoint){
						continue;
					}

					queue.enqueue(w);
				}
			}
		}
		return retList;


	}


	public List<PowerDevice> getDeviceList(PowerDevice pd,
										   PowerDevice excDev, String tagTypes, String excTypes,
										   String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
										   boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType,boolean printPaths) {

		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pd.getPowerDeviceID());
		if(this.s==-1){
			return retList;
		}
		//设置断点
		int stopPoint = -1;//这是一共不存在的坐标
		if(!(excDev==null || G.index(excDev.getPowerDeviceID())==null)){
			stopPoint = G.index(excDev.getPowerDeviceID());
		}

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {


					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							retList.add(tempPowerDevice);
							if(printPaths){
								printPaths(tempPowerDevice);
							}
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(printPaths){
									printPaths(tempPowerDevice);
								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								retList.add(tempPowerDevice);
								if(printPaths){
									printPaths(tempPowerDevice);
								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								retList.add(tempPowerDevice);
								if(printPaths){
									printPaths(tempPowerDevice);
								}
								if(isStopOnTagType){
									continue;
								}
							}
						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}

						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){
								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									containMx = true;
									if(tagTypeList.contains(MATHER_LINE)){
										retList.add((PowerDevice)G.nodeInfo.get(x));
									}
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。
					if(w==stopPoint){
						continue;
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;


	}

	@SuppressWarnings("static-access")
	public PowerDeviceListAndMapPaths getPowerDeviceListAndMapPaths(PowerDevice pd,
																	ArrayList<PowerDevice> excDevList, String tagTypes, String excTypes,
																	String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
																	boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType, boolean isStopOnDiffVolt, int validPort, boolean isPrintPath){
		PowerDeviceListAndMapPaths tempDeviceNodeAndPath = new PowerDeviceListAndMapPaths();

		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pd.getPowerDeviceID());
		if(this.s==-1){
			return tempDeviceNodeAndPath;
		}
		//设置断点
		ArrayList<Integer> stopPoints = new ArrayList<Integer>();
		if(excDevList!=null){
			for(PowerDevice pdd:excDevList){
				if(G.contains(pdd.getPowerDeviceID())){
					stopPoints.add(G.index(pdd.getPowerDeviceID()));
				}
			}
		}

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。

		while(!queue.isEmpty()){
			int v = queue.dequeue();
			int numPort = 0;
			for(int w : G.G().adj(v)){
				if(v ==s){
					numPort ++;
				}
				if(v == s && validPort!=0){
					if(numPort != validPort){
						continue;
					}
				}
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
							tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
							if(isPrintPath){
								for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
									System.out.println(pdd.toMoreString());
								}
							}
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
								}
								if(isStopOnTagType){
									continue;
								}
							}
						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}

							}
						}
						if(isStopOnDiffVolt){
							if(pd.getPowerVoltGrade()!= tempPowerDevice.getPowerVoltGrade()){
								continue;
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}

						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){

								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									if(tagTypeList.contains(MATHER_LINE)){
										tempDeviceNodeAndPath.addPowerDevice(((PowerDevice)G.nodeInfo.get(x)));
										tempDeviceNodeAndPath.addPaths(((PowerDevice)G.nodeInfo.get(x)),deviceNodePathToByIdList(x));
										if(isPrintPath){
											for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath((PowerDevice)G.nodeInfo.get(x))){
												System.out.println(pdd.toMoreString());
											}
										}
									}
									containMx = true;
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。  //排查A设备也是要能返回设备，优先返回，后排查，所以continue项都往后挪一下位置。
					if(stopPoints.contains(w)){
						continue;
					}

					queue.enqueue(w);
				}
			}
		}
		return tempDeviceNodeAndPath;

	}



	public PowerDeviceListAndMapPaths getPowerDeviceListAndMapPaths(String pdId,
																	String excDevIds, String tagTypes, String excTypes,
																	String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
																	boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType, boolean isStopOnDiffVolt, int validPort, boolean isPrintPath,int resultNum){
		PowerDeviceListAndMapPaths tempDeviceNodeAndPath = new PowerDeviceListAndMapPaths();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pdId);
		if(this.s==-1){
			return tempDeviceNodeAndPath;
		}
		int count = 0;
		PowerDevice pd = (PowerDevice)G.nodeInfo.get(this.s);

				//设置断点
		ArrayList<Integer> stopPoints = new ArrayList<Integer>();
		List<String> excDevIdList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excDevIds)){
			excDevIdList =Arrays.asList(excDevIds.split(","));
			for(String id:excDevIdList){
				if(G.contains(id)){
					stopPoints.add(G.index(id));
				}
			}
		}
		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。

		while(!queue.isEmpty()){
			int v = queue.dequeue();
			int numPort = 0;
			for(int w : G.G().adj(v)){
				if(v ==s){
					numPort ++;
				}
				if(v == s && validPort!=0){
					if(numPort != validPort){
						continue;
					}
				}
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							count++;
							tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
							tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
							if(isPrintPath){
								for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
									System.out.println(pdd.toMoreString());
								}
								System.out.println("----------------------");
							}
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								count++;
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
									System.out.println("----------------------");

								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								count++;
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
									System.out.println("----------------------");

								}
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								count++;
								tempDeviceNodeAndPath.addPowerDevice(tempPowerDevice);
								tempDeviceNodeAndPath.addPaths((tempPowerDevice),deviceNodePathToByIdList(w));
								if(isPrintPath){
									for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath(tempPowerDevice)){
										System.out.println(pdd.toMoreString());
									}
									System.out.println("----------------------");

								}
								if(isStopOnTagType){
									continue;
								}
							}
						}
						if(resultNum>=0 && count>=resultNum){
							return tempDeviceNodeAndPath;
						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}

							}
						}
						if(isStopOnDiffVolt){
							if(pd.getPowerVoltGrade()!= tempPowerDevice.getPowerVoltGrade()){
								continue;
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}

						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){

								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									if(tagTypeList.contains(MATHER_LINE)){
										count++;
										tempDeviceNodeAndPath.addPowerDevice(((PowerDevice)G.nodeInfo.get(x)));
										tempDeviceNodeAndPath.addPaths(((PowerDevice)G.nodeInfo.get(x)),deviceNodePathToByIdList(x));
										if(isPrintPath){
											for(PowerDevice pdd:tempDeviceNodeAndPath.getPdPath((PowerDevice)G.nodeInfo.get(x))){
												System.out.println(pdd.toMoreString());
											}
											System.out.println("----------------------");
										}
									}
									if(resultNum>=0 && resultNum>=count){
										return tempDeviceNodeAndPath;
									}
									containMx = true;
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。  //排查A设备也是要能返回设备，优先返回，后排查，所以continue项都往后挪一下位置。
					if(stopPoints.contains(w)){
						continue;
					}

					queue.enqueue(w);
				}
			}
		}
		return tempDeviceNodeAndPath;

	}


	public List<PowerDevice> getDeviceList(String pdId,
																	String excDevIds, String tagTypes, String excTypes,
																	String tagRunTypes, String excRunTypes, boolean isSearchDirectOnly,
																	boolean isSearchOff, boolean isStopOnBus, boolean isStopOnTagType, boolean isStopOnDiffVolt, int validPort, int resultNum){
		List<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagTypes)){
			tagTypeList =Arrays.asList(tagTypes.split(","));
		}
		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> tagRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagRunTypes)){
			tagRunTypeList =Arrays.asList(tagRunTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pdId);
		if(this.s==-1){
			return retList;
		}
		int count = 0;
		PowerDevice pd = (PowerDevice)G.nodeInfo.get(this.s);

		//设置断点
		ArrayList<Integer> stopPoints = new ArrayList<Integer>();
		List<String> excDevIdList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excDevIds)){
			excDevIdList =Arrays.asList(excDevIds.split(","));
			for(String id:excDevIdList){
				if(G.contains(id)){
					stopPoints.add(G.index(id));
				}
			}
		}
		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。

		while(!queue.isEmpty()){
			int v = queue.dequeue();
			int numPort = 0;
			for(int w : G.G().adj(v)){
				if(v ==s){
					numPort ++;
				}
				if(v == s && validPort!=0){
					if(numPort != validPort){
						continue;
					}
				}
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						if(tagTypeList.size()==0&&tagRunTypeList.size()==0){//表示没有所有设备都返回
							count++;
							retList.add(tempPowerDevice);
							if(isStopOnTagType){
								continue;
							}
						}else if(tagTypeList.size()==0 && tagRunTypeList.size() >0){
							if(tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								count++;
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()==0){//表示一个过滤条件
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())){
								count++;
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}

						}else if(tagTypeList.size()>0&&tagRunTypeList.size()>0){
							if(tagTypeList.contains(tempPowerDevice.getDeviceType())&&tagRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
								count++;
								retList.add(tempPowerDevice);
								if(isStopOnTagType){
									continue;
								}
							}
						}
						if(resultNum>=0 && count>=resultNum){
							return retList;
						}
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}

							}
						}
						if(isStopOnDiffVolt){
							if(pd.getPowerVoltGrade()!= tempPowerDevice.getPowerVoltGrade()){
								continue;
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}
						//搜索直连，意为设备后面就不收索了，就搜一层。
						if(isSearchDirectOnly){
							continue;
						}
					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){
								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									if(tagTypeList.contains(MATHER_LINE)){
										count++;
										retList.add(((PowerDevice)G.nodeInfo.get(x)));
									}
									if(resultNum>=0 && resultNum>=count){
										return retList;
									}
									containMx = true;
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					//通过continue 实现 不执行queue.enqueue(w)，实现遇到排除设备，不往下搜索。  //排查A设备也是要能返回设备，优先返回，后排查，所以continue项都往后挪一下位置。
					if(stopPoints.contains(w)){
						continue;
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;

	}

	/**
	 * zlc
	 * 搜索设备旁边的设备。不考虑断开
	 * @param pd
	 * @param tagDevtype  目标的设备类型如果为空，或空字符串，则不做过滤 。如果不为空，则只返回该类型设备，多类型用竖线 分割 Breaker|Disconnector
	 * @return List<PowerDevice>,确定不会返回null
	 */
	public List<PowerDevice> getDeviceDirectList(PowerDevice pd,
			String tagDevtype) {
		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		List<String> tags = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagDevtype)){
			tags =Arrays.asList(tagDevtype.split(","));
		}
		this.s= G.index(pd.getPowerDeviceID());
		if(this.s==-1){
			return retList;
		}
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(tags.size()>0){
							if(tags.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
								retList.add(((PowerDevice)G.nodeInfo.get(w)));
							}
						}else{
							retList.add(((PowerDevice)G.nodeInfo.get(w)));
						}
						//只搜一层。
						continue;
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;
	}
	public List<PowerDevice> getDeviceDirectList(String pd,
			String tagDevtype) {

		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		List<String> tags = new ArrayList<String>();
		if(!StringUtils.isEmpty(tagDevtype)){
			tags =Arrays.asList(tagDevtype.split("|"));
		}
		this.s= G.index(pd);
		if(this.s==-1)
			return retList;
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(tags.size()>0){
							if(tags.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
								retList.add(((PowerDevice)G.nodeInfo.get(w)));
							}
						}else{
							retList.add(((PowerDevice)G.nodeInfo.get(w)));
						}
						//只搜一层。
						continue;
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;

	}

	public  List<PowerDevice> getPathByDevice(PowerDevice pd,
			PowerDevice tag, String excTypes, String excRunTypes,
			boolean isSearchOff, boolean isStopOnBus) {
		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();

		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pd.getPowerDeviceID());
		if(this.s==-1){
			return retList;
		}

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}
						if(tempPowerDevice.getPowerDeviceID().equals(tag.getPowerDeviceID())){
							retList = getPathDevices(tempPowerDevice);
							return retList;
						}

					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){
								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									containMx = true;
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;
	}

	public  List<PowerDevice> getPathByDevice(String pdId,
			String tagId, String excTypes, String excRunTypes,
			boolean isSearchOff, boolean isStopOnBus) {
		ArrayList<PowerDevice> retList = new ArrayList<PowerDevice>();
		//处理传进来的参数。将字符串转换成数组。
		List<String> tagTypeList = new ArrayList<String>();

		List<String> excTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excTypes)){
			excTypeList =Arrays.asList(excTypes.split(","));
		}
		List<String> excRunTypeList = new ArrayList<String>();
		if(!StringUtils.isEmpty(excRunTypes)){
			excRunTypeList =Arrays.asList(excRunTypes.split(","));
		}
		//设置搜索起点
		this.s= G.index(pdId);
		if(this.s==-1)
			return retList;

		//prev 记录了遍历的路径
		prev = new TreeMap<Integer, Integer>();
		//dist 记录了遍历过的所有节点，防止重复入队列（杜绝节点被搜两侧）,同时存储了该节点与搜索起点的深度
		dist = new TreeMap<Integer, Integer>();
		dist.put(s, 0);
		//将起点进入队列
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		PowerDevice tempPowerDevice;
		Boolean containMx = false;
		//开始基于队列结构的宽度优先算法。
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						tempPowerDevice = (PowerDevice)G.nodeInfo.get(w);
						//如果路径上不允许断开
						if(!isSearchOff){
							if(NeedAttentionType.contains(tempPowerDevice.getDeviceType())){
								if(!"0".equals(tempPowerDevice.getDeviceStatus())){
									continue;
								}
							}
						}
						//先将不搜索的设备 continue掉。
						if(excTypeList.contains(tempPowerDevice.getDeviceType())){
							continue;
						}
						if(excRunTypeList.contains(tempPowerDevice.getDeviceRunType())){
							continue;
						}
						if(tempPowerDevice.getPowerDeviceID().equals(tagId)){
							retList = getPathDevices(tempPowerDevice);
							return retList;
						}

					}else{
						if(isStopOnBus){
							//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
							containMx = false;
							for(int x : G.G().adj(w)){
								if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
									containMx = true;
									break;
								}
							}
							if(containMx){
								continue;
							}
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return retList;
	}



	/**
	 *
	 * @param BreakDeviceId 为避开的设备或端点id
	 * @return 返回潜在的电源出线开关。既已id为出发点，允许断开开关小于1的情况下，能搜到的出线开关。
	 */
	@SuppressWarnings("static-access")
	public ArrayList<PowerDevice> getDGSwitchs(String id,String BreakDeviceId){
		this.s= G.index(id);
		int breakDevice = -1;
		if(G.contains(BreakDeviceId)){
			breakDevice = G.index(BreakDeviceId);
		}
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		ArrayList<PowerDevice> aray = new ArrayList<PowerDevice>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(w==breakDevice){
						continue;
					}
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							int count = 0;
							String id_id = "";
							if((!((PowerDevice)G.nodeInfo.get(w)).isConnected())||(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
								//这里 如果 搜索的起始设备是一个开关，且是断开的，。并没有在逻辑的考虑范围内，约定起始设备是配电变压器。
								//判断是不是唯一一个断开开关，如果不是，就不往下找了。
								for(int number :this.pathToByNumber(w)){
									if(G.nodeInfo.get(number) instanceof PowerDevice  && SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(number)).getDeviceType()) && !((PowerDevice)G.nodeInfo.get(number)).isConnected()){
										count++ ;
										if(count >1){
											continue;
										}
									}
								}
								if(count >1 ){
									continue;
								}
							}
							if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
								aray.add((PowerDevice)G.nodeInfo.get(w));
								continue;
							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;
	}

	/**
	 * 这个方法不考虑开关状态，只搜附近的一层线路的出线开关
	 * @param BreakDeviceId 为避开的设备或端点id
	 * @return 返回潜在的电源出线开关。既已id为出发点，允许断开开关小于1的情况下，能搜到的出线开关。
	 */
	@SuppressWarnings("static-access")
	public ArrayList<PowerDeviceAndPath> getDGSwitchAndPaths(String id,String BreakDeviceId){
		ArrayList<PowerDeviceAndPath> aray = new ArrayList<PowerDeviceAndPath>();
		String lineId ="";
		if(G.contains(id)){
			this.s= G.index(id);
		}else {
			return  aray;
		}
		int breakDevice = -1;
		if(G.contains(BreakDeviceId)){
			breakDevice = G.index(BreakDeviceId);
		}
		//tempLineSet 用来记录一个端点，旁边连接的设备 line集合。假定A线设备，和B线设备公用一个端点。 假如集合大小大于等于2.说明时分界点。如果不包含输入的lineId，说明找到了第三层。程序也就仅找第三层。
		HashSet<String> tempLineSet = new HashSet<String>();
		PowerDevice tempPowerDevice;

		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
					prev.put(w, v);
					if(w==breakDevice){
						continue;
					}
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(((PowerDevice)G.nodeInfo.get(w)).isPW()&&"".equals(lineId)){
							lineId = ((PowerDevice)G.nodeInfo.get(w)).getPowerStationID();
						}
						if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							int count = 0;
							String id_id = "";
							/*不考虑开关状态。
							if((!((PowerDevice)G.nodeInfo.get(w)).isConnected())||(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
								//这里 如果 搜索的起始设备是一个开关，且是断开的，。并没有在逻辑的考虑范围内，约定起始设备是配电变压器。
								//判断是不是唯一一个断开开关，如果不是，就不往下找了。
								for(int number :this.pathToByNumber(w)){
									if(G.nodeInfo.get(number) instanceof PowerDevice  && SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(number)).getDeviceType()) && !((PowerDevice)G.nodeInfo.get(number)).isConnected()){
										count++ ;
										if(count >1){
											continue;
										}
									}
								}
								if(count >1 ){
									continue;
								}
							}*/
							if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
								PowerDeviceAndPath pdap = new PowerDeviceAndPath((PowerDevice)G.nodeInfo.get(w),getPowerDevicePathToById(w));
								aray.add(pdap);
								continue;
							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						tempLineSet.clear();
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
						for(int x : G.G().adj(w)){
							tempPowerDevice = (PowerDevice)G.nodeInfo.get(x);
							if(tempPowerDevice.isPW()){
								tempLineSet.add(tempPowerDevice.getPowerStationID());
							}
						}
						if(tempLineSet.size()>1&&(!tempLineSet.contains(lineId))&&(!"".equals(lineId))){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;
	}









	/**
	 * 如果两端有供电电源，断开开关，不会影响供电，就不返回，
	 * 如果两端都找不到供电电源，那么就搜索断开路径，找供电电源，并返回没有供电电源的那一侧 链接点序号。
	 * @param switch_id
	 * @return 返回停电开关断开后，没有电的那一端 的序号。 如果两端站内开关，就随机返回一边端点，如果两端都没站内开关，就返回null
	 */
	public String getOutageSide(String switch_id){
		if(G.contains(switch_id))
		this.s= G.index(switch_id);

		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		ArrayList<Integer> zxSwitchs = new ArrayList<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
							zxSwitchs.add(w);
							continue;
						}
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!"0".equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceStatus())){
								continue;
							}

						}
					}
					queue.enqueue(w);
				}
			}
		}
		Set<Integer> outageTerminal = new HashSet<Integer>();
		for(int w : G.G().adj(s)){
			outageTerminal.add(w);
		}
		//没找到站内开关，就通过运行断开的方式，搜索站内开关。
		if(zxSwitchs.size()==0){
			prev = new TreeMap<Integer, Integer>();
			dist = new TreeMap<Integer, Integer>();
			queue = new Queue<Integer>();
			zxSwitchs = new ArrayList<Integer>();
			queue.enqueue(s);
			dist.put(s, 0);
			while(!queue.isEmpty()){
				int v = queue.dequeue();
				for(int w : G.G().adj(v)){
					if (!dist.containsKey(w)) {
						dist.put(w, 1 + dist.get(v));
		                prev.put(w, v);
						if(G.nodeInfo.get(w) instanceof PowerDevice){
							if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
								zxSwitchs.add(w);
								continue;
							}
						}
						queue.enqueue(w);
					}
				}
			}
		}
		for(int i=0;i<zxSwitchs.size();i++){
			int temp = 0;//存储起点旁边的端点。
			for(int terminal:pathToByNumber(zxSwitchs.get(i))){
				System.out.println(terminal);
				if(terminal==s){
				}else{
					temp = terminal;
					break;
				}
			}
			//将开关两个端点中，有电的那个去掉。
			outageTerminal.remove(temp);
		}
		if(outageTerminal.size()==1){
			//尴尬通过遍历的方式取值
			for(int w : outageTerminal){
				//返回失电的端点。
				return G.name(w);
			}
			return null;
		}else if(outageTerminal.size()==2){
			//两端都没站内开关返回0
			return null;
		}else if (outageTerminal.size()==0){
			//两端都有站内开关
			for(int w : G.G().adj(s)){
				//随机返回一个端点
				return G.name(w);
			}
			return null;
		}else{
			//(异常)
			return null;
		}
	}
	/**
	 * 通过sorurceId,找targerId,返回目标旁边的连接点
	 * 方法用于情景搜索停电返回，sourcceId,为电源，targetId为开关，
	 * 然后将返回的结果用于另外的算法
	 * @param sourceId
	 * @param targetId
	 * @return
	 */
	public String serachDeivceOtherSide(String sourceId,String targetId){
		String returnStr ="";//当返回空时，表示传入的开关为站内开关，不做排除过滤（原本要排除电源那一侧）
		int intTargetId = 0;
		if(G.index(sourceId)==null){
			return null;
		}
		if(G.index(targetId)==null){
			return null;
		}
		if(sourceId.equals(targetId)){
			return "";
		}
		intTargetId = G.index(targetId);
		this.s= G.index(sourceId);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					if(w==intTargetId){
						return G.nodeInfo.get(v).getId();
					}
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if((!((PowerDevice)G.nodeInfo.get(w)).isConnected())){
								continue;
							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}


		return null;
	}
	/**
	 * 取错方法名了，居然和上面的方法明一样，这个是取设备另一边的某类设备。
	 * @param sourceId
	 * @param excludeId
	 * @param tagetType
	 * @return
	 */
	public ArrayList<PowerDevice> serachDeivceOtherSide(String sourceId,String excludeId,String tagetType){
		int intExcludeId = 0;
		ArrayList<PowerDevice> aray = new ArrayList<PowerDevice>();
		if(G.index(sourceId)==null){
			return aray;
		}
		if("".equals(excludeId)){
			intExcludeId = -1;
		}else if(excludeId == null||G.index(excludeId)==null){
			return aray;
		}else{
			intExcludeId = G.index(excludeId);
		}

		this.s= G.index(sourceId);
		//如果起点是开关，本身是断开的，返回空数组
		if(G.nodeInfo.get(this.s) instanceof PowerDevice){
			if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(s)).getDeviceType())){
				if((!((PowerDevice)G.nodeInfo.get(s)).isConnected())){
					return aray;
				}

			}
		}
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if(w==intExcludeId){
					continue;
				}
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if((!((PowerDevice)G.nodeInfo.get(w)).isConnected())){
								continue;
							}
						}
						if(tagetType.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							aray.add(((PowerDevice)G.nodeInfo.get(w)));
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;

	}



	/**
	 * 指定出发点，指定排除端点，收缩目标类型设备
	 * @param sourceId
	 * @param excludeIds
	 * @param tagetType
	 * @return
	 */
	public ArrayList<PowerDevice> serachDeivceOtherSide(String sourceId,ArrayList<String> excludeIds,String tagetType){
		int intExcludeId = 0;
		ArrayList<PowerDevice> aray = new ArrayList<PowerDevice>();
		if(G.index(sourceId)==null){
			return aray;
		}
		ArrayList<Integer> exeSide = new ArrayList<Integer>();
		for(String str:excludeIds){
			exeSide.add(G.index(str));
		}
//		if("".equals(excludeId)){
//			intExcludeId = -1;
//		}else if(excludeId == null||G.index(excludeId)==null){
//			return aray;
//		}else{
//			intExcludeId = G.index(excludeId);
//		}
//
		this.s= G.index(sourceId);
		//如果起点是开关，本身是断开的，返回空数组
		if(G.nodeInfo.get(this.s) instanceof PowerDevice){
			if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(s)).getDeviceType())){
				if(!"0".equals(((PowerDevice)G.nodeInfo.get(s)).getDeviceStatus())){
					return aray;
				}

			}
		}
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if(exeSide.contains(w)){
					continue;
				}
//				if(w==intExcludeId){
//					continue;
//				}
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!"0".equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceStatus())){
								continue;
							}
						}
						if(tagetType.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							aray.add(((PowerDevice)G.nodeInfo.get(w)));
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;

	}

	@SuppressWarnings("static-access")
	public ArrayList<PowerDevice> getPDBYQs(String id){
		ArrayList<PowerDevice> aray = new ArrayList<PowerDevice>();
		if(G.index(id)==null){
			return aray;
		}
		this.s= G.index(id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if((!((PowerDevice)G.nodeInfo.get(w)).isConnected())){
								continue;
							}
						}
						if(TRANSFORMER.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							aray.add(((PowerDevice)G.nodeInfo.get(w)));
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;
	}


	@SuppressWarnings("static-access")
	public ArrayList<PowerDeviceAndPath> serachPDBYQAndPath(String id){
		ArrayList<PowerDeviceAndPath> aray = new ArrayList<PowerDeviceAndPath>();
		if(G.index(id)==null){
			return aray;
		}
		this.s= G.index(id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		PowerDeviceAndPath tempPowerDeviceAndPath = new PowerDeviceAndPath();
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!"0".equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceStatus())){
								continue;
							}
						}
						if(TRANSFORMER.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							tempPowerDeviceAndPath.setPowerDevice(((PowerDevice)G.nodeInfo.get(w)));
							tempPowerDeviceAndPath.setPath(getPowerDevicePathToById(w));
							aray.add(tempPowerDeviceAndPath);
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return aray;
	}
	/**
	 * 检测图模质量调用的方法
	 * @param device_id
	 * @param include_disconnect
	 * @return
	 */
	@SuppressWarnings("static-access")
	public ArrayList<PowerDevice> serachTopologyEndsPaths(String device_id,Boolean include_disconnect){
		ArrayList<String> excludeLists  = new ArrayList<String>();
		excludeLists.add("BusbarSection");
		excludeLists.add("GroundDisconnector");
		excludeLists.add("DistributionPowerTransform");
		ArrayList<Integer> aray = new ArrayList<Integer>();
		ArrayList<PowerDevice> arayNode = new ArrayList<PowerDevice>();
		if(G.index(device_id)==null){
			return arayNode;
		}
		this.s = G.index(device_id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			Boolean endFlag = true;
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					endFlag = false;
					break;
				}
			}
			if(endFlag){
				aray.add(v);
			}
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!include_disconnect){
								if(!"0".equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceStatus())){
									aray.add(w);
									continue;
								}
							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						int  zwSwitchNum = 0;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
							if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								zwSwitchNum++;
							}
						}
						if((!containMx)&& (zwSwitchNum>1)){
							aray.add(w);
							continue;
						}

					}
					queue.enqueue(w);
				}
			}
		}
		StringBuilder sb = new StringBuilder();
		for(int i:aray){
			if(!(G.nodeInfo.get(i) instanceof PowerDevice)){
				for(int w : G.G().adj(i)){
					if(excludeLists.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
						continue;
					}
					arayNode.add((PowerDevice)G.nodeInfo.get(w) );
					break;
				}
			}else{
				if(excludeLists.contains(((PowerDevice)G.nodeInfo.get(i)).getDeviceType())){
				}else{
					arayNode.add((PowerDevice)G.nodeInfo.get(i) );
				}
			}
		}


		return arayNode;
	}

	@SuppressWarnings("static-access")
	public ArrayList<PowerDevice> serachEndsPaths(String device_id,Boolean include_disconnect){
		ArrayList<Integer> aray = new ArrayList<Integer>();
		ArrayList<PowerDevice> arayNode = new ArrayList<PowerDevice>();

		if(G.index(device_id)==null){
			return arayNode;
		}
		this.s = G.index(device_id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			Boolean endFlag = true;
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					endFlag = false;
					break;
				}
			}
			if(endFlag){
				aray.add(v);
			}
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!include_disconnect){
								if(!"0".equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceStatus())){
									continue;
								}

							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							aray.add(w);
							continue;
						}

					}
					queue.enqueue(w);
				}
			}
		}
		StringBuilder sb = new StringBuilder();
		for(int i:aray){
			if(!(G.nodeInfo.get(i) instanceof PowerDevice)){
				for(int w : G.G().adj(i)){
					arayNode.add((PowerDevice)G.nodeInfo.get(w) );
					break;
				}
			}else{
				arayNode.add((PowerDevice)G.nodeInfo.get(i) );
			}
		}


		return arayNode;
	}



	/**
	 *
	 * @param device_id 设备id,是否包含断开路径
	 * @return 返回符合类型的设备数组。
	 */
	@SuppressWarnings("static-access")
	public ArrayList<PowerDevice> serachPWRunTypeSwitchZXs(String device_id,Boolean include_disconnect){
		ArrayList<PowerDevice> aray = new ArrayList<PowerDevice>();
		if(G.index(device_id)==null){
			return aray;
		}
		this.s = G.index(device_id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						//System.out.println(((PowerDevice)G.nodeInfo.get(w)).getDeviceType());
						if(SWITCH_ID.equals(((PowerDevice)G.nodeInfo.get(w)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(w)).isPW())){
							aray.add(((PowerDevice)G.nodeInfo.get(w)));
							continue;
						}
						if(NeedAttentionType.contains(((PowerDevice)G.nodeInfo.get(w)).getDeviceType())){
							if(!include_disconnect){
								if(!((PowerDevice)G.nodeInfo.get(w)).isConnected()){
									continue;
								}

							}
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}

					}
					queue.enqueue(w);
				}
			}
		}
		return aray;
	}



	/**
	 * 算法设计用来，传递站内开关ID，返回临近的配网ID，然后用于获取设备所在线路。
	 * @param  id，搜索设备起始id
	 * @return 返回1个邻近的配网设备
	 */
	@SuppressWarnings("static-access")
	public String serachPwLineDevice(String id){
		String device = null;
		this.s= G.index(id);
		prev = new TreeMap<Integer, Integer>();
		dist = new TreeMap<Integer, Integer>();
		Queue<Integer> queue = new Queue<Integer>();
		queue.enqueue(s);
		dist.put(s, 0);
		Boolean containMx = false;
		while(!queue.isEmpty()){
			int v = queue.dequeue();
			for(int w : G.G().adj(v)){
				if (!dist.containsKey(w)) {
					dist.put(w, 1 + dist.get(v));
	                prev.put(w, v);
					if(G.nodeInfo.get(w) instanceof PowerDevice){
						if(((PowerDevice)G.nodeInfo.get(w)).isPW()){
							device = G.nodeInfo.get(w).getId();
							return device;
						}
					}else{
						//非设备连接点。判断与该连接点连接的设备是否包含主网母线，如果包含的话，就不往下搜索了。
						containMx = false;
						for(int x : G.G().adj(w)){
							if(MATHER_LINE.equals(((PowerDevice)G.nodeInfo.get(x)).getDeviceType()) &&(! ((PowerDevice)G.nodeInfo.get(x)).isPW())){
								containMx = true;
								break;
							}
						}
						if(containMx){
							continue;
						}
					}
					queue.enqueue(w);
				}
			}
		}
		return device;
	}







	private void printPaths(PowerDevice pd){
		int id = G.index(pd.getPowerDeviceID());
		Stack<PowerDevice> path = new Stack<PowerDevice>();
		if(hasPathToByNumber(id)){
			for(int x = id;x!=s&& dist.containsKey(x);x =  prev.get(x)){
				if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
				path.push((PowerDevice)G.nodeInfo.get(x));
			}
			path.push((PowerDevice)G.nodeInfo.get(s));
		}
		for(PowerDevice pdd:path){
			System.out.println(pdd.toMoreString());
		}
		System.out.println("----------------分隔符----------------");
	}

	private  ArrayList<PowerDevice> getPathDevices(PowerDevice pd){
		ArrayList <PowerDevice> returnList = new ArrayList<PowerDevice>();
		int id = G.index(pd.getPowerDeviceID());
		Stack<PowerDevice> path = new Stack<PowerDevice>();
		if(hasPathToByNumber(id)){
			for(int x = id;x!=s&& dist.containsKey(x);x =  prev.get(x)){
				if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
				path.push((PowerDevice)G.nodeInfo.get(x));
			}
			path.push((PowerDevice)G.nodeInfo.get(s));
		}
		for(PowerDevice pdd:path){
			returnList.add(pdd);
		}
		return returnList;
	}


	public boolean hasPathToById(String id){
		return dist.containsKey(G.index(id));
	}
	public boolean hasPathToByNumber(int number){
		return dist.containsKey(number);
	}

	public Iterable<String> pathToById(String id){
		if(!hasPathToById(id))return null;
		Stack<String> path = new Stack<String>();
		int v = G.index(id);
		for(int x = v;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
			path.push(G.name(x));
		}
		path.push(G.name(s));
		return path;
	}

	public Iterable<String> pathToById2(int id){
		Stack<String> path = new Stack<String>();
		int v = id;
		for(int x = v;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
			path.push(G.name(x));
		}
		path.push(G.name(s));
		return path;
	}
	public Iterable<PowerDevice> deviceNodePathToByIdList(int v){
		ArrayList<PowerDevice> path = new ArrayList<PowerDevice>();
		for(int x = v;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
				path.add((PowerDevice)G.nodeInfo.get(x));
		}
		path.add((PowerDevice)G.nodeInfo.get(s));
		Collections.reverse(path);
		return path;
	}
	public Iterable<PowerDevice> getPowerDevicePathToById(String id){
		if(!hasPathToById(id))return null;
		Stack<PowerDevice> path = new Stack<PowerDevice>();
		int v = G.index(id);
		for(int x = v;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
			path.push((PowerDevice)G.nodeInfo.get(x));
		}
		path.push((PowerDevice)G.nodeInfo.get(s));
		return path;
	}
	public Iterable<PowerDevice> getPowerDevicePathToById(int v){
		Stack<PowerDevice> path = new Stack<PowerDevice>();
		for(int x = v;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			if(G.nodeInfo.get(x) instanceof PowerDevice)//只将设备存到路径里。
			path.push((PowerDevice)G.nodeInfo.get(x));
		}
		path.push((PowerDevice)G.nodeInfo.get(s));
		return path;
	}

	private Iterable<Integer> pathToByNumber(int number){
		if(!hasPathToByNumber(number))return null;
		Stack<Integer> path = new Stack<Integer>();
		for(int x = number;x!=s&& dist.containsKey(x);x =  prev.get(x)){
			path.push(x);
		}
		path.push(s);
		return path;
	}

}

package czprule.graphic;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.HashMap;
import java.util.Map;

import org.springframework.dao.DataAccessException;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.core.ResultSetExtractor;

import com.tellhow.graphicframework.model.Node;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.DeviceNode;
import czprule.model.Graph;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprulepw.PWSystemConstants;

//只加载了设备的id,和连接关系，并把连接关系预处理了一遍形成了“图”数据结构。
//问题配网是有必要加载开关的断开连接状态，还有预处理，设备运行时类型吗?
//有必要，成票服务可能需要。
//不查遥信值，防止存储信息失效。
/**
 *
 * <AUTHOR>
 *
 */
public class SymbolGraph {

	//	public Map<String, HashMap<String, PowerDevice>> oldMapPowerStationDevice ;
	public static HashMap<String , Integer> st;  //存储了id,与序号 。这个不然少，要不然序号就生成不了
	public static HashMap<Integer,Node> nodeInfo;  //存储了序号 与 全信息。这个不能少，要不然信息看不到  ，能用对象数组吗？可以但不方便，要手动阔数组长度，当以后要动态加载数据的话，会有些不方便
	public static String[] keys;     //位置是序号，值是id 。这个好像可以不要。
	public static Graph G;
	private static SymbolGraph symbolGraph = new SymbolGraph();
	public static SymbolGraph getInstance (){
		return symbolGraph;
	}
	public static void freshen(){
		symbolGraph = new SymbolGraph();
	}


	@SuppressWarnings("unchecked")
	private SymbolGraph() {

		try {
			// TODO Auto-generated method stub
//			oldMapPowerStationDevice = CBSystemConstants.getMapPowerStationDevice();
			CBSystemConstants.getMapPowerStationDevice().clear();
			st = new HashMap<String, Integer>();
			nodeInfo = new HashMap<Integer, Node>();
			G = new Graph(3000000);
			String sql = "   select"+
					"                                        b.id equip_id                           ,"+
					"                                        connectivitynode_id                ,"+
					"                                        devicetype                         ,"+
					"                                        ispw                               ,"+
					"                                        equip_name                         ,"+
					"                                        container_id                       ,"+
					"                                        container_name                       ,"+
					"                                        nvl(C.VOLTAGE_CODE, 0) VOLTAGE_CODE,"+
					"                                        nvl(d.DEVICERUNTYPE,'') DEVICERUNTYPE ,"+
					"                                        d.DEVICERUNMODEL                   ,"+
					"                                        d.DEVICESTATUS                     ,"+
					"                                        EQUIPTYPE_FLAG"+
					"                                from"+
					"                                        "+CBSystemConstants.equipUser+"T_C_TERMINAL a"+
					"                                full join"+
					"                                        ("+
					"                                                select"+
					"                                                        equip_id id          ,"+
					"                                                        equip_type devicetype,"+
					"                                                        '1' ispw             ,"+
					"                                                        equip_name           ,"+
					"                                                        T_PD_EQUIPINFO.line_id container_id ,"+
					"                                                        VOLTAGELVL           ,"+
					"                                                        T_C_LINE.LINE_NAME  container_name"+
					"                                                from"+
					"                                                        "+CBSystemConstants.equipUser+"T_PD_EQUIPINFO"+
					"												 left join "+CBSystemConstants.equipUser+"T_C_LINE	  ON T_PD_EQUIPINFO.LINE_ID =  T_C_LINE.LINE_ID    "+
					"                                                "+
					"                                                union all"+
					"                                                "+
					"                                                select"+
					"                                                        equip_id id            ,"+
					"                                                        equiptype_id devicetype,"+
					"                                                        '0' ispw               ,"+
					"                                                        equip_name             ,"+
					"                                                        T_EQUIPINFO.station_id container_id,"+
					"                                                        T_EQUIPINFO.VOLTAGE_ID VOLTAGELVL  ,"+
					"                                                        T_SUBSTATION.STATION_NAME  container_name"+
					"                                                from"+
					"                                                        "+CBSystemConstants.equipUser+"T_EQUIPINFO"+
					"												 left join "+CBSystemConstants.equipUser+"T_SUBSTATION	  ON T_EQUIPINFO.station_id =  T_SUBSTATION.station_id "+
					"                                          union all select  id,'27' devicetype,'0' ispw,   name equi_name,st_id    container_id ,''  VOLTAGELVL, '' container_name from      "+CBSystemConstants.equipUser+"t_c_aclineend"+
					"                                        )"+
					"                                        b"+
					"                                on"+
					"                                        a.equip_id = b. id"+
					"                                left join "+CBSystemConstants.equipUser+"T_VOLTAGELEVEL c"+
					"                                on"+
					"                                        b.VOLTAGELVL = c.VOLTAGE_ID"+
					"                                        left join "+CBSystemConstants.equipUser+"t_a_DEVICEEQUIPINFO d"+
					"                                on"+
					"                                        a.equip_id = d.EQUIPID"+
					"                                        left join "+CBSystemConstants.equipUser+"T_EQUIPTYPE E"+
					"                                on"+
					"                                        devicetype = E.equiptype_id   "+
					"										";
			System.out.println(sql);
//			String className=new Exception().getStackTrace()[1].getClassName();
//			System.out.println(className);
//			System.out.println(new Exception().getStackTrace()[1].getMethodName());
//			DBManagerService dBManagerService =  new DBManagerServiceImpl();
//			List ret =dBManagerService.queryForList(sql);
			JdbcTemplate jdbcTemplate = DBManager.getCZPJdbcTemplate();
			jdbcTemplate.query(sql, new ResultSetExtractor(){
				@Override
				public Object extractData(ResultSet rs) throws SQLException,
						DataAccessException {
					String tempEquipId = "";
					String tempConnectdId = "";
					String devicetype = "";
					String containerId = "";
					String containerName = "";
					String deviceName = "";
					String deviceStatus = "";
					Node node ;
					String roomId;
					String deviceRunType,deviceRunModel;
					String volt ;

					boolean connected ;
					boolean isPw ;
					int tempStSize;
					while (rs.next()) {
						long t1 =  System.currentTimeMillis();
						tempEquipId = rs.getString("equip_id");
						tempConnectdId = rs.getString("connectivitynode_id");
						isPw = "1".equals(rs.getString("ispw"))? true:false;//切换数字
						deviceStatus = rs.getString("devicestatus")==null?"":rs.getString("devicestatus");
						devicetype = rs.getString("equiptype_flag");
						containerId = rs.getString("container_id");
						containerName =  rs.getString("container_name");
						deviceName = rs.getString("equip_name");
						roomId = rs.getString("ROOM_ID");
						deviceRunType = rs.getString("DEVICERUNTYPE")==null?"":rs.getString("DEVICERUNTYPE");
						deviceRunModel = rs.getString("DEVICERUNMODEL")==null?"":rs.getString("DEVICERUNMODEL");
						volt = rs.getString("VOLTAGE_CODE");
						double powerVoltGrade = 0;
						if("Breaker".equals(devicetype)&&(!isPw)){
							deviceRunType = PWSystemConstants.PWRunTypeSwitchZX;
						}
//						if(deviceRunType==null){
//							deviceRunType = "";
//						}
						if(devicetype==null){
							devicetype = "";
						}
						if(roomId==null){
							roomId = "";
						}
						PowerDevice pd = new PowerDevice();
						pd.setPowerDeviceID(tempEquipId);
						pd.setPowerDeviceName(deviceName);
						pd.setDeviceType(devicetype);
						pd.setDeviceRunType(deviceRunType);
						pd.setDeviceRunModel(deviceRunModel);
						pd.setDeviceSetType(deviceRunType);
						pd.setSelfDeviceStatus(deviceStatus);
						pd.setPowerStationName(containerName);
						pd.setRoomID(roomId);
						pd.setPW(isPw);
						volt = volt.replaceAll("(?i)kv", "");
						powerVoltGrade = Double.valueOf(volt);
						pd.setPowerVoltGrade(powerVoltGrade);
						pd.setPowerStationID(containerId);

						if(CBSystemConstants.getMapPowerStationDevice().get(containerId)==null||CBSystemConstants.getMapPowerStationDevice().get(containerId).get(tempEquipId)==null){
							if(!CBSystemConstants.getMapPowerStationDevice().containsKey(containerId))
								CBSystemConstants.getMapPowerStationDevice().put(containerId, new HashMap<String,PowerDevice>());
							CBSystemConstants.getMapPowerStationDevice().get(containerId).put(tempEquipId, pd);
						}
						if(!st.containsKey(tempEquipId)){
							tempStSize = st.size();
							st.put(tempEquipId, tempStSize);
							nodeInfo.put(tempStSize,pd);
						}
						if(tempConnectdId==null){
							continue;
						}
						if(!st.containsKey(tempConnectdId)){
							tempStSize = st.size();
							st.put(tempConnectdId, tempStSize);
							node = new Node(tempStSize, tempConnectdId);
							nodeInfo.put(tempStSize,node);
						}
						int intTempEquipId = st.get(tempEquipId);
						int intTempConnectdId = st.get(tempConnectdId);
						G.addEdge(intTempEquipId,intTempConnectdId);

						if(st.size()%100000==0){
							System.out.println(System.currentTimeMillis());
						}
					}
					return null;
				}
			});
			keys = new String[st.size()];
			for (String name : st.keySet()) {
				keys[st.get(name)] = name;
			}
			System.out.println(st.size()+"个节点信息");
			System.out.println("加载完成");

//			System.out.println("加载1半");
//
//			G = new Graph(st.size());  我给它估摸一个大小，免查两次，更新完之后，如果有必要的话，再调整为正在大小，
//			jdbcTemplate.query(sql, new ResultSetExtractor(){
//				@Override
//				public Object extractData(ResultSet rs) throws SQLException,
//						DataAccessException {
//					// TODO Auto-generated method stub
//					String tempEquipId = "";
//					String tempConnectdId = "";
//					while (rs.next()) {
//			        	tempEquipId = rs.getString("equip_id");
//						tempConnectdId = rs.getString("connectivitynode_id");
//						if(tempConnectdId==null)continue;
//						int intTempEquipId = st.get(tempEquipId);
//						int intTempConnectdId = st.get(tempConnectdId);
//						G.addEdge(intTempEquipId,intTempConnectdId);
//			        }
//					return null;
//				}});
//


			System.out.println("完成");
		} catch (Exception e) {
			// TODO: handle exception

			e.printStackTrace();
		}
	}
	public boolean contains(String s){
		return st.containsKey(s);
	}
	public Integer index(String s){
		return st.get(s)==null?-1:st.get(s);
	}
	public static String name(int v){
		return keys[v];
	}
	public Graph G(){
		return G;
	}

}

package czprule.graphic;

import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.LineNode;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/**
 * 
 * <AUTHOR>
 *
 */

public class InitLine {
	public static HashMap<String , Integer> line_st;  
	public static HashMap<Integer,LineNode> line_info;
	public static String[] line_keys;//
	private static InitLine initLine = new InitLine();
	
	public static void freshen(){
		initLine =  new InitLine();
	}
	
	
	public static InitLine getInstance (){
		return initLine;
	}
	public InitLine() {
		// TODO Auto-generated method stub
		line_st = new HashMap<String, Integer>();
		line_info = new HashMap<Integer, LineNode>();
		String sql  = "select line_id,line_name from  "+CBSystemConstants.equipUser+"T_C_LINE";
		int tempLineStSize;
		String line_name;
		String line_id;
		LineNode lineNode ;
		List list = DBManager.queryForList(sql);
		for(int i=0;i<list.size();i++){
			Map temp = (Map)list.get(i);
			tempLineStSize = line_st.size();
			line_name = StringUtils.ObjToString(temp.get("line_name"));
        	line_id = StringUtils.ObjToString(temp.get("line_id"));
        	line_st.put(line_id, tempLineStSize);
        	lineNode = new LineNode(line_id,line_name);
        	line_info.put(tempLineStSize,lineNode);
		}
        line_keys = new String[line_st.size()];
		for(String name : line_st.keySet()){
			line_keys[line_st.get(name)] = name;
		}
		SymbolGraph G = SymbolGraph.getInstance();
		BreadthFirstPaths breadthFirstPaths = new BreadthFirstPaths(G);
		List zxSwitchIds = DBManager.queryForList("select equip_id from "+ CBSystemConstants.opcardUser+"T_EQUIPINFO where equiptype_id = '26' ");
		String lineId;
		String zxSwtichId;
		for(int j = 0;j<zxSwitchIds.size();j++){
			
			Map map = (Map)zxSwitchIds.get(j);
			zxSwtichId = map.get("equip_id").toString();
			lineId = breadthFirstPaths.getLineId(zxSwtichId);
			if(InitLine.contains(lineId))
			InitLine.line_info.get(InitLine.index(lineId)).setSwitchID(zxSwtichId);
		}
	}
	public static boolean contains(String s){
		return line_st.containsKey(s);
	}
	public static int index(String s){
		return line_st.get(s);
	}
	public static String name(int v){
		return line_keys[v];
	}
	
}

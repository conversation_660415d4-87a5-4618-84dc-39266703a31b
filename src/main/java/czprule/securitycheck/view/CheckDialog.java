package czprule.securitycheck.view;

import java.awt.BorderLayout;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.FileReader;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import javax.swing.JButton;
import javax.swing.JCheckBox;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTextArea;

import org.apache.commons.net.ftp.FTPClient;
import org.apache.commons.net.ftp.FTPFile;
import org.apache.commons.net.ftp.FTPReply;

import com.tellhow.czp.mainframe.JPopupTextArea;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.impl.ReplaceStrXTXLMC;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-12-2 下午04:43:05 
 */
public class CheckDialog extends javax.swing.JDialog{
	
	private JPanel panel1;
	private JPanel panel2;
	private JPanel panel3;
	private JScrollPane panel4;
	private JPanel panel5;
	private JPanel panel6;
	private JCheckBox[] jCheckBoxs;
	private JButton button1;
	private JButton button2;
	private JButton button3;
	private JTextArea logTextArea;
	private JLabel statusLabel;
	private String[] items;
	private Thread checkThread;
	private boolean isStop = false;
	private String tempFile = "D:\\test\\";//临时目录
	private String upfileName = "";

	public CheckDialog (java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setLocationCenter();
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	private void initComponents() {
		this.setSize(660, 300);
		panel1 = new JPanel(new BorderLayout());
		panel2 = new JPanel();
		panel2.setLayout(new FlowLayout());
		panel3 = new JPanel(new BorderLayout());
		panel4 = new JScrollPane();
		panel5 = new JPanel();
		panel6 = new JPanel();
		items = new String[]{"基态安全检验","静态安全检验","暂态稳定检验","电压稳定校验","小干扰稳定校验","短路电流检验"};
		jCheckBoxs = new JCheckBox[items.length];
		for(int i = 0 ; i < items.length; i++) {
			jCheckBoxs[i] = new JCheckBox(items[i]);
			jCheckBoxs[i].setSelected(true);
			panel2.add(jCheckBoxs[i]);
		}
		button1 = new JButton("开始检验");
		button2 = new JButton("停止检验");
		button3 = new JButton("关闭");
		logTextArea = new JPopupTextArea();
		logTextArea.setRows(3);
		logTextArea.setColumns(50);
		logTextArea.setLineWrap(true);
		logTextArea.setEditable(false);
		statusLabel = new JLabel("");
		panel5.add(statusLabel);
		panel6.add(button1);
		panel6.add(button2);
		panel6.add(button3);
		panel4.setViewportView(logTextArea);
		panel1.add(panel2, BorderLayout.NORTH);
		panel1.add(panel4, BorderLayout.CENTER);
		panel1.add(panel3, BorderLayout.SOUTH);
		panel3.add(panel5, BorderLayout.CENTER);
		panel3.add(panel6, BorderLayout.EAST);
		this.setTitle("操作票安全检验");
		this.setContentPane(panel1);
		this.setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		button1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		button2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});
		button3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});
	}
	
	private void jButton1ActionPerformed(ActionEvent evt) {

//		String line = new ReplaceStrXTXLMC().strReplace("系统线路名称", pd, null, "");
		if ((this.checkThread != null) && (this.checkThread.isAlive()))
			return;
		checkThread = new Thread(new Runnable(){
			public void run() {
				PowerDevice pd = CBSystemConstants.getCurRBM().getPd();
				String D5Name = getD5000Name(pd.getPowerDeviceID(), pd.getDeviceType());
	 	    	logTextArea.setText("  请耐心等待安全校验结果  "+"\r\n");
		    	statusLabel.setText("正在进行校验");
				try {
					String moban = tbp.common.util.file.FileOperation.readFile("check/"+"安全校验模板.txt");
					String[] tempS = moban.split("\r\n");
					StringBuffer writeString = new StringBuffer();
					StringBuffer result = new StringBuffer();
					for(int j=0;j<tempS.length;j++){
						String temp = tempS[j].toString();
						if (temp != null) {// 写文件
							if (temp.indexOf("system") > 0) {
								String temp2 = temp.substring(72, 89);
								SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HH:mm:ss");
								Date date = new Date();
								String str = format.format(date);
								temp = temp.substring(0, 71).toString() + str + "' !>"+"\r\n";
								writeString.append(temp); 
							} else if (temp.indexOf("name") > 0) {
								writeString.append(temp + "\r\n");
							} else if(temp.indexOf("宁夏.") > 0) {
								
								String volt = String.valueOf(pd.getPowerVoltGrade())+"0";
								String runType = pd.getDeviceRunType();
								String type = pd.getDeviceType();
								String deviceType = "";
								if(type.equals("ACLineSegment")){//线路
									deviceType = "0";
								}else if(type.equals("PowerTransformer")){//主变
									deviceType = "1";
								}else if(type.equals("Generator")){//机组
									deviceType = "2";
								}else if(type.equals("BusbarSection")){//母线
									deviceType = "3";
								}else if(type.equals("Breaker")){//开关
									deviceType = "4";
								}
								String status = pd.getDeviceStatus();
								if(status.equals("0")){
									status="1";//运行 送电
								}else{
									status="0";//其他状态  停电
								}
								SimpleDateFormat format = new SimpleDateFormat("yyyyMMdd_HH:mm");
								Date date = new Date();
								String str = format.format(date);
								writeString.append(D5Name +"                    "+volt+"          "+status+"               "+deviceType+"        "+str+"\r\n");
							} else {
								writeString.append(temp + "\r\n");
							}
						}
					}
					//System.out.println("**第1步**");
					uploadFile(writeString.toString());//上传文件到ftp服务器地址
					//System.out.println("**第2步**");
					String backFile = null;
					File dir = null;
					File[] files = null;
					
					for(int i=1;i<=600;i++){
						System.out.println("扫描第"+i+"次");
						if(isStop) {
		 	    			checkThread.interrupt();
		 	    			isStop = false;
		 	    			statusLabel.setText("检验中止");
		 	    			return;
			 	    	}
						try { 
							checkThread.sleep (1000) ; 
						} catch (InterruptedException e)
						{
							e.printStackTrace();
						}
						//System.out.println("**第3步**");
						downFile();//读取返回结果文件，把返回结果从ftp下载到本地目录下
						//System.out.println("**第4步**");
						dir = new File(tempFile);
						files = dir.listFiles();  //遍历此抽象路径下的所有文件
						if(files==null ||files.length ==0) {
							//logTextArea.append("服务器未取得校验结果文件");
							continue;
						}else{
							break;
						}
					}
					//等待后台生成返回结果文件
			        //File[] files = dir.listFiles();  //遍历此抽象路径下的所有文件 
					System.out.println(files.length ==0);
			        if(files.length ==0){
			        	logTextArea.setText("");
			        	logTextArea.append("D5000未返回校验结果");
			        }
			        	  
			        for (int f = 0; f < files.length; f++) {   
			        //判断此文件是否是一个文件  
			            if (!files[f].isDirectory()) {
			            	if(files[f].toString().equals(tempFile+"CZPRPT_"+upfileName+".dat")){
			            		backFile = files[f].getName();
			            		break;
			            	}
			            }   
			        }
			        //读取返回结果文件
			        String resultFile = "";
			        if(backFile !=null){
				        resultFile = tbp.common.util.file.FileOperation.readFile(tempFile+backFile);
			        }
			        String[] rest = resultFile.split("\n");
			        for(int i=7;i<rest.length;i++){
			        	System.out.println(rest[i].toString());
			        	if((rest[i].toString()).contains("</")){
			        		rest[i] = "  ";
			        	}
			        	if(rest[i].toString().indexOf("静态安全分析结论")>0){
			        		rest[i] = "静态安全分析";
			        	}
			        	if(rest[i].toString().indexOf("暂态稳定分析结论")>0){
			        		rest[i] = "暂态稳定分析";
			        	}
			        	if(rest[i].toString().indexOf("电压稳定分析结论")>0){
			        		rest[i] = "电压稳定分析";
			        	}
			        	if(rest[i].toString().indexOf("小干扰稳定分析结论")>0){
			        		rest[i] = "小干扰稳定分析";
			        	}
			        	if(rest[i].toString().indexOf("短路电流分析结论")>0){
			        		rest[i] = "短路电流分析";
			        	}
			        	if(rest[i].toString().indexOf("基态安全分析结论")>0){
			        		rest[i] = "基态安全分析";
			        	}
			        	logTextArea.setText("");
			        	if(!"".equals(rest[i].toString())){
			        		result.append(rest[i]+"\r\n");
			        		logTextArea.append(result.toString());
			        	}

			        }
			        deleteDir();//删除本地返回结果文件 
				} catch (Exception e) {
					// TODO Auto-generated catch block
		        	logTextArea.setText("");
					logTextArea.append("没有获取到结果。");
				}
	 	    	statusLabel.setText("检验完成");
			}
		});
		checkThread.start();

	}
	
	private void jButton2ActionPerformed(ActionEvent evt) {
		isStop = true;
	}
	
	private void jButton3ActionPerformed(ActionEvent evt) {
		this.dispose();
	}
	
	/**
	 *向ftp写文件(数据)
	 *<AUTHOR>
	 *@since 2014-09-16  8:47
	 *@param content
	 */
	public void uploadFile(String content) {  
		   
        // 要写入的文件内容  
        String fileContent = content;  
        // ftp登录用户名  
//        String userName = "administrator";  
        // ftp登录密码  
//        String userPassword = "tellhow";
        String userName = "d5000";  
        String userPassword = "d5000.ningxia";  
//        String userName = "administrator";  
//        String userPassword = "tellh0w";  
        // ftp地址  
//        String server = "************";//直接ip地址
//        String server = "***********";//直接ip地址  
//        String server = "************";//直接ip地址  
        String server = "**************";//直接ip地址  
        SimpleDateFormat format = new SimpleDateFormat("yyyyMMddHHmmss");
		Date date = new Date();
		String str = format.format(date);
		upfileName = str.substring(0,8)+"_"+str.substring(8,14);
//		upfileName = "20140928_141444";
        // 创建的文件  
//        String fileName = "ftp.txt"; 
		String fileName = "CZP_"+str+".txt"; 
        // 指定写入的目录  
//		String path = "/home/<USER>/ningxia/var/Syskeeper2000/1/";
		String path = "/home/<USER>/cq/var/jiaohe/";
        FTPClient ftpClient = new FTPClient();  
        try {  
            InputStream is = null;  
            // 1.输入流  
            is = new ByteArrayInputStream(fileContent.getBytes());  
            // 2.连接服务器  
            ftpClient.connect(server);  
            // 3.登录ftp  
            ftpClient.login(userName, userPassword);  
            // 4.指定写入的目录
            ftpClient.changeWorkingDirectory(path);  
            // 5.写操作  
            ftpClient.setFileType(FTPClient.BINARY_FILE_TYPE);  
            ftpClient.storeFile(new String(fileName.getBytes("utf-8"),  
                    "iso-8859-1"), is); 
            is.close();  
        } catch (Exception e) {  
            e.printStackTrace();  
        } finally {  
            if (ftpClient.isConnected()) {  
                try {  
                    ftpClient.disconnect();  
                } catch (Exception e) {  
                    e.printStackTrace();  
                }  
            }  
        }  
    }
	
	
	/** 
	 * 
     * ftp下载数据 
     * <AUTHOR>
     * @since 2014-09-16  9:02
     * 
     */  
    public void downFile() {  
        // ftp登录用户名  
//        String userName = "administrator";  
//        // ftp登录密码  
//        String userPassword = "tellhow";  
//        // ftp地址:直接IP地址  
//        String server = "************"; 
        String userName = "d5000";  
        // ftp登录密码  
        String userPassword = "d5000.ningxia";  
        // ftp地址:直接IP地址  
        String server = "***********";         // 创建的文件  
//        String server = "************";         // 创建的文件  
        // 指定写入的目录  
//        String path = "/home/<USER>/ningxia/izxclient/dsasop";
        String path = "/home/<USER>/ningxia/izxclient/dsasop/";  
        // 指定本地写入文件  
        String localPath=tempFile;
        tbp.common.util.file.FileOperation.createDir(localPath);

          
        FTPClient ftp = new FTPClient();  
        try {  
            int reply;  
            //1.连接服务器  
            ftp.connect(server);  
            //2.登录服务器 如果采用默认端口，可以使用ftp.connect(url)的方式直接连接FTP服务器  
            ftp.login(userName, userPassword);  
            //3.判断登陆是否成功  
            reply = ftp.getReplyCode();  
            if (!FTPReply.isPositiveCompletion(reply)) {  
                ftp.disconnect();  
            }  
            //4.指定要下载的目录  
            ftp.changeWorkingDirectory(path);// 转移到FTP服务器目录  
            //5.遍历下载的目录  
            FTPFile[] fs = ftp.listFiles();
            for (FTPFile ff : fs) {
                //解决中文乱码问题，两次解码  
//                byte[] bytes=ff.getName().getBytes("iso-8859-1");  
//                String fn=new String(bytes,"utf-8");
                String fn = new String(ff.getName().getBytes("iso-8859-1"), "GBK");
                if(fn.indexOf("dat")>0){
//                	if(fn.equals("CZPRESULT"+"2332"+".qs")){
                	if(fn.equals("CZPRPT_"+upfileName+".dat")){
                		//6.写操作，将其写入到本地文件中  
                		File localFile = new File(localPath + fn);  
                        OutputStream is = new FileOutputStream(localFile);  
                        ftp.retrieveFile(ff.getName(), is); 
                        is.close(); 
                     }
                	 
                }  
            }  
            ftp.logout();
        } catch (IOException e) {  
            e.printStackTrace();  
        } finally {  
            if (ftp.isConnected()) {  
                try {  
                    ftp.disconnect();  
                } catch (IOException ioe) {  
                }  
            }  
        }  
    }
    /**
     * 删除从服务器中下载的返回结果文件
     * <AUTHOR>
     * @since 2014-09-16 10:02
     * 
     */
    public  void deleteFile(){
		File dir = new File(tempFile);   
        File[] files = dir.listFiles();  //返回此抽象路径下的文件 
        String backFile = "";
        for (int f = 0; f < files.length; f++) {   
        //判断此文件是否是一个文件  
            if (!files[f].isDirectory()) {
            	if(files[f].getName().indexOf("dat")>0){
            		backFile = files[f].getName();
            		break;
            	}
            }   
        }
        if(!"".equals(backFile)){
        	tbp.common.util.file.FileOperation.deleteFile(tempFile+backFile);//删除该结果文件
        }
    }
    
    /**
     * 删除从D:\text路径
     * <AUTHOR>
     * @since 2014-09-23 11:42
     * 
     */
    public void deleteDir(){
		File dir = new File(tempFile);   
        File[] files = dir.listFiles();  //返回此抽象路径下的文件 
        String backFile = "";
        for (int f = 0; f < files.length; f++) {   
        //判断此文件是否是一个文件  
            if (!files[f].isDirectory()) {
            		backFile = files[f].getName();
                	tbp.common.util.file.FileOperation.deleteFile(tempFile+backFile);//删除该结果文件
            	}
            } 
        
        tbp.common.util.file.FileOperation.deleteFile(tempFile);
    }
    /**
     * 操作票中设备名称转化为D5000原始设备名称
     * <AUTHOR>
     * @param id
     * @param deviceType
     * @return
     */
    public String getD5000Name(String id,String deviceType){
    	Map map = new HashMap();
    	String name = "";
    	if(deviceType.equals("ACLineSegment")){
    		String sql = "select s.line_name from "+CBSystemConstants.equipUser+"T_C_ACLINEEND t,"+CBSystemConstants.equipUser+"T_C_LINE s where t.acline_id=s.line_id and t.id='"+id+"'";
    		map = (Map)DBManager.query(sql).get(0);
    		name = StringUtils.ObjToString(map.get("line_name"));
    	}else{
    		String sql = "select equip_name from "+CBSystemConstants.equipUser+"T_EQUIPINFO t where t.equip_id='"+id+"'";
    		map = (Map)DBManager.query(sql).get(0);
    		name = StringUtils.ObjToString(map.get("equip_name"));
    	}
    	return name;
    }
}

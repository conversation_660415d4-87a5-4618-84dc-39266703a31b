package czprule.securitycheck.view;

import java.awt.BorderLayout;
import java.awt.Color;
import java.awt.Dimension;
import java.awt.FlowLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.awt.event.WindowAdapter;
import java.awt.event.WindowEvent;
import java.util.Iterator;
import java.util.List;

import javax.swing.JButton;
import javax.swing.JDialog;
import javax.swing.JFrame;
import javax.swing.JPanel;
import javax.swing.JScrollPane;
import javax.swing.JTable;
import javax.swing.JTextArea;
import javax.swing.table.DefaultTableModel;


/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2010-12-15 下午04:29:00 
 */
public class CheckFlowDialog extends JDialog{

	private JTable tableStation;
	private DefaultTableModel tableModel;
	private JTextArea textArea;
	private JScrollPane jScrollPane1;
	private JScrollPane jScrollPane2;
	private JPanel panel1;
	private JPanel panelButton;
	private JButton bOK;
	private JButton bCancel;
	private List list;
	private String title;
	private Thread checkThread;
	
	public CheckFlowDialog(JFrame parent, String title, List list) {
        super(parent, true);
        this.list = list;
        this.title = title;
        initComponents();
        setLocationCenter();
    }
	
	/**
     * 屏幕中央位置
     */
    public void setLocationCenter() {
        int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
        int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
        this.setLocation((w - this.getSize().width) / 2, (h - this.getSize().height) / 2);
    }
    
    private void initComponents(){
    	this.setTitle(title+"--潮流校核");
    	this.setSize(400, 400);
    	this.setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
    	this.addWindowListener(new WindowAdapter() {
            public void windowClosing(WindowEvent e){
                closeDialog();
            }
        });
    	tableStation = new JTable();
    	initTable();
    	textArea = new JTextArea("");
    	textArea.setColumns(20);
    	textArea.setRows(2);
    	
    	jScrollPane1 = new JScrollPane();
    	jScrollPane1.setBorder(javax.swing.BorderFactory
				.createTitledBorder("设备开合操作"));
    	
    	tableStation.setPreferredSize(new Dimension(400,tableStation.getRowCount()*20));
    	jScrollPane1.setPreferredSize(new Dimension(400,(int)tableStation.getPreferredSize().getHeight()+60));
    	jScrollPane1.setViewportView(tableStation);
    	
    	jScrollPane2 = new JScrollPane();
    	jScrollPane2.setBorder(javax.swing.BorderFactory
				.createTitledBorder("潮流校核结果"));
    	jScrollPane2.setViewportView(textArea);
    	
    	panelButton = new JPanel();
    	bOK = new JButton("潮流校核");
    	bCancel= new JButton("关闭");
    	panelButton.setLayout(new FlowLayout());
    	panelButton.add(bOK);
    	panelButton.add(bCancel);
    	
    	panel1 = new JPanel();
    	panel1.setLayout(new BorderLayout());
    	panel1.add(jScrollPane1,BorderLayout.NORTH);
    	panel1.add(jScrollPane2,BorderLayout.CENTER);
    	panel1.add(panelButton,BorderLayout.SOUTH);
    	this.setContentPane(panel1);
    	
    	bOK.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(ActionEvent evt) {
                bOkActionPerformed(evt);
            }
        });
    	bCancel.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(ActionEvent evt) {
                bCancelActionPerformed(evt);
            }
        });
    }
    
    private void initTable()
    {
    	tableModel = new DefaultTableModel(
                new String [] {"设备ID","设备状态","厂站","设备","操作"},
            0){
            boolean[] canEdit = new boolean [] {
            	false,false,false,false,false,false
            };
            public boolean isCellEditable(int rowIndex, int columnIndex) {
                return canEdit [columnIndex];
            }
        };
        tableStation.setModel(tableModel);
       

        tableStation.getColumnModel().getColumn(0).setMaxWidth(0);
        tableStation.getColumnModel().getColumn(0).setMinWidth(0);
        tableStation.getColumnModel().getColumn(0).setPreferredWidth(0);
        tableStation.getColumnModel().getColumn(1).setMaxWidth(0);
        tableStation.getColumnModel().getColumn(1).setMinWidth(0);
        tableStation.getColumnModel().getColumn(1).setPreferredWidth(0);
    	tableStation.setRowHeight(20);
       
        for(Iterator iter = list.iterator();iter.hasNext();) {
        	Object[] rowData = (Object[])iter.next();
			tableModel.addRow(rowData);
        }
    }
    
    private void closeDialog(){
        this.setVisible(false);
        this.dispose();
    }
    
    private void bOkActionPerformed(ActionEvent evt)
    {
    	checkThread = new Thread(new Runnable() {
	 	    public void run() {
	 	    	textArea.setForeground(Color.BLACK);
	 	    	textArea.setText("正在连接EMS进行潮流校核，请稍后...");
	 	    	try {
					checkThread.currentThread().sleep(3000);
				} catch (InterruptedException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
				}
				textArea.setForeground(Color.RED);
				String result = "不能连接到EMS，无法进行潮流校核！";
		    	textArea.setText(result);
	 	    }
    	});
    	checkThread.start();
    }
    
    private void bCancelActionPerformed(ActionEvent evt)
    {
    	//selectedList.clear();
    	closeDialog();
    }

}

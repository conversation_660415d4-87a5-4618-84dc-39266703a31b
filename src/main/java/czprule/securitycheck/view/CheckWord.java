package czprule.securitycheck.view;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.table.TableModel;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.OperationInfo;
import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.UserRuleExecute;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.WordExecute;
import czprule.wordcard.model.CardModel;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013-3-28 下午01:52:51 
 */
public class CheckWord {
	
	public CheckWord(String word) {
	}
	
	public static CardModel execute(String task) {
		String[] word = null;
		ArrayList<OperationInfo> oiList = getEquip(task);
		if(oiList.size() == 0)
			return null;
		String equipName = oiList.get(0).getEquipName();
		if(equipName.equals(""))
			return null;
		String endStatus = getOperation(task).get(0).getEndStatus();
		QueryDeviceDao dao = new QueryDeviceDao();
		String lineID = dao.getLineByName(equipName);
		if(lineID.equals(""))
			return null;
		
		CBSystemConstants.getDtdMap().clear();
		CBSystemConstants.getCurOperateDevs().clear();
		CBSystemConstants.isOpenMessages = false;
		
		PowerDevice line = (PowerDevice) CBSystemConstants.getPowerLine(lineID);
		Map<PowerDevice,String> stationlines= QueryDeviceDao.getPowersLineBySysLine(line);
        for(Iterator iter = stationlines.keySet().iterator();iter.hasNext();) {
        	PowerDevice pd = (PowerDevice)iter.next();
        	CreatePowerStationToplogy.loadFacData(pd.getPowerStationID());
        	CBSystemConstants.putCurOperateDev(CBSystemConstants.getPowerDevice(pd.getPowerStationID(), pd.getPowerDeviceID()));
        	if(pd.getPowerDeviceName().indexOf(equipName.charAt(0)) >=0 && !CBSystemConstants.LineSource.containsKey(pd.getPowerDeviceID()))
        		CBSystemConstants.putLineSource(pd.getPowerDeviceID(), pd);
        	else {
        		if(!CBSystemConstants.LineLoad.containsKey(pd.getPowerDeviceID()))
        			CBSystemConstants.LineLoad.put(pd.getPowerDeviceID(), new ArrayList<PowerDevice>());
        		CBSystemConstants.LineLoad.get(pd.getPowerDeviceID()).add(pd);
        	}
        }
        
        DeviceOperate dre=new DeviceOperate();
		RuleBaseMode Srcrbm = dre.getRBM(CBSystemConstants.LineSource.get(line), endStatus);
		
		
		UserRuleExecute userRuleExc=new UserRuleExecute();
		if(userRuleExc.execute(Srcrbm)){
			RuleExecute ruleExc=new RuleExecute();
			if(!ruleExc.execute(Srcrbm)){
				return null;
			}
		}
		CBSystemConstants.isOpenMessages = true;
		DispatchTransDevice dtd=null;
    	int sysNum=DeviceOperate.getAlltransDevMap().size();
    	for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {    //遍历设备状态
    		dtd = CBSystemConstants.getDtdMap().get(i);
    		DeviceOperate.getAlltransDevMap().put(++sysNum, dtd);
    	}
    	
		PowerDevice dev=null;
//		List list = new ArrayList();
//		for (int j = 1; j < DeviceOperate.getAlltransDevMap().size() + 1; j++) {    //遍历设备状态
//        	dtd = (DispatchTransDevice) DeviceOperate.getAlltransDevMap().get(j);
//        	dev = (PowerDevice) dtd.getTransDevice();
//        	String endState = dtd.getEndstate();
//        	if(endState.equals("4") || endState.equals("5")) {
//        		String status = endState.equals("4")?"0":"1";
//        		String operation = endState.equals("4")?"合上":"拉开";
//        		String stationName = CBSystemConstants.getPowerStation(dev.getPowerStationID()).getPowerDeviceName();
//        		String deviceName = dev.getPowerDeviceName();
//        		Object[] data = new Object[]{dev.getPowerDeviceID(),status,stationName,deviceName,operation};
//        		list.add(data);
//        	}
//        }
//		CheckFlowDialog dialog = new CheckFlowDialog(SystemConstants.getMainFrame(), task, list);
//		dialog.setVisible(true);
		
		DeviceOperate.RollbackDeviceStatus();
		DeviceOperate.ClearDevMap();
    	CardModel cm=WordExecute.getInstance().execute(Srcrbm);
		return cm;
	}
	
	public static String execute(String[] word) {
		String result = "";
		for(int i = 0; i < word.length; i ++) {
			if(word[i].equals(""))
				result = result + "选择的第"+String.valueOf(i+1)+"条操作命令为空！\r\n";
			else {
				if(word[i].indexOf("I") >= 0)
					result = result + "请检查是否在操作命令["+word[i]+"]中将罗马数字写成英文字母！\r\n";
				String operation = getOperation(word[i]).get(0).getOperation();
				if(!operation.equals(""))
					result = result + "操作命令["+word[i]+"]解析结果是：" + operation + "\r\n";
				else
					result = result + "操作命令["+word[i]+"]不能解析出" + "操作设备与动作\r\n";
				if(result.equals(""))
					result = result + "操作命令["+word[i]+"]通过拼写检查！\r\n";
			}
		}
		return result;
	}
	
	/**
	 * 单条术语解析出设备目标状态
	 * 
	 * @param card
	 * @return
	 */
	public static ArrayList<OperationInfo> getOperation(String word) {
		ArrayList<OperationInfo> oiList = new ArrayList<OperationInfo>();
		String equipName = "";
		String equipNum = "";
		String equipType = "";
		String beginStatus = "";
		String endStatus = "";
		String operation = "";
		oiList = getEquip(word);
		for(OperationInfo oi : oiList) {
			if(word.indexOf("转") >= 0) {
				String w1 = word.substring(0, word.indexOf("转"));
				String w2 = word.substring(word.indexOf("转")+1);
				if (w1.indexOf("运行") >= 0) {
					beginStatus = "0";
				}
				else if (w1.indexOf("热备用") >= 0) {
					beginStatus = "1";
				}
				else if (w1.indexOf("冷备用") >= 0) {
					beginStatus = "2";
				}
				else if (w1.indexOf("检修") >= 0) {
					beginStatus = "3";
				}
				if (w2.indexOf("运行") >= 0) {
					endStatus = "0";
				}
				else if (w2.indexOf("热备用") >= 0) {
					endStatus = "1";
				}
				else if (w2.indexOf("冷备用") >= 0) {
					endStatus = "2";
				}
				else if (w2.indexOf("检修") >= 0) {
					endStatus = "3";
				}
				equipName=oi.getEquipName();
				operation = equipName+CBSystemConstants.getDeviceStatusName("", beginStatus)+"转"+CBSystemConstants.getDeviceStatusName("", endStatus);
			}
			else if(word.indexOf("拉开") >= 0) {
				beginStatus = "0";
				endStatus = "1";
				operation = "拉开"+equipName;
			}
			else if(word.indexOf("拉出") >= 0) {
				beginStatus = "0";
				endStatus = "1";
				operation = "拉出"+equipName;
			}
			else if(word.indexOf("推上") >= 0) {
				beginStatus = "1";
				endStatus = "0";
				operation = "推上"+equipName;
			}
			else if(word.indexOf("推入") >= 0) {
				beginStatus = "1";
				endStatus = "0";
				operation = "推入"+equipName;
			}
			else if(word.indexOf("断开") >= 0) {
				beginStatus = "0";
				endStatus = "1";
				operation = "断开"+equipName;
			}
			else if(word.indexOf("合上") >= 0) {
				beginStatus = "1";
				endStatus = "0";
				operation = "合上"+equipName;
			}
			oi.setBeginStatus(beginStatus);
			oi.setEndStatus(endStatus);
			oi.setOperation(operation);
		}
		return oiList;
	}
	
	public static ArrayList<OperationInfo> getEquip(String word) {
		ArrayList<OperationInfo> oiList = new ArrayList<OperationInfo>();
		String equipNum = "";
		
		String[] type = new String[]{SystemConstants.SwitchFlowGroundLine,SystemConstants.SwitchFlowGroundLine, SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate,SystemConstants.SwitchSeparate, SystemConstants.SwitchSeparate, SystemConstants.Switch, SystemConstants.Switch,SystemConstants.MotherLine, SystemConstants.MotherLine, SystemConstants.InOutLine, SystemConstants.PowerTransformer, SystemConstants.ElecShock, SystemConstants.ElecCapacity};
		String[] key = new String[]{"接地刀闸","地刀","隔离刀闸","小车1","小车", "刀闸","断路器" ,"开关", "母线", "母", "线", "主变", "电抗器", "电容器"};
		for(int i = 0; i < key.length; i++) {
			if(word.lastIndexOf(key[i]) >= 0) {
				/*String str = word.substring(0, word.lastIndexOf(key[i]));
				if(key[i].equals("线"))
					equipNum = str;
				else {
					for (int j = str.length()-1; j >= 0; j--) {
						char c = str.charAt(j);
						if (c == '、') {
							OperationInfo oi = new OperationInfo();
							oi.setEquipName(equipNum + key[i]);
							oi.setEquipNum(equipNum);
							oi.setEquipType(type[i]);
							oiList.add(oi);
							equipNum = "";
						}
						else if (Character.isDigit(c) || c == '#' || c == '-' || StringUtils.isALB(c))
							equipNum = str.charAt(j) + equipNum;
						else
							break;
					}
				}*/
				if(word.contains("、")){
					String[] oprs = word.split("、");
					for (int j = 0; j < oprs.length; j++) {
						equipNum = getEquipNum(oprs[j]);
						OperationInfo oi = new OperationInfo();
						oi.setEquipName(equipNum + key[i]);
						oi.setEquipNum(equipNum);
						oi.setEquipType(type[i]);
						oi.setTypeName(key[i]);
						oiList.add(oi);
					}
				}else{
					//equipNum=getEquipNum(word);
					equipNum = CZPService.getService().getDevNum(word);
					OperationInfo oi = new OperationInfo();
					
					List results=DBManager.query(OPEService.getService().getEquipCheck(equipNum + key[i]));
					if(results.size()==0){
						oi.setEquipName(equipNum + key[i]);
						oi.setEquipNum("不存在");
						oi.setEquipType(type[i]);
						oi.setTypeName(key[i]);
						oiList.add(oi);
					}else{
						oi.setEquipName(equipNum + key[i]);
						oi.setEquipNum(equipNum);
						oi.setEquipType(type[i]);
						oi.setTypeName(key[i]);
						oiList.add(oi);
					}

				}
					
				break;	
				
				/*OperationInfo oi = new OperationInfo();
				oi.setEquipName(equipNum + key[i]);
				oi.setEquipNum(equipNum);
				oi.setEquipType(type[i]);
				oiList.add(oi);
				break;*/
			}
		}
		if(oiList.size()==0&&!(equipNum=getEquipNum(word)).equals("")){
			String kind = "";
			String deviceType = "";
			int index=word.indexOf("-");
			if(index==-1){
				kind="开关";
				deviceType=SystemConstants.Switch;
			}else{
				String endnum=word.substring(index, word.length()-1);
				if(endnum.length()==1){
					kind="刀闸";
					deviceType=SystemConstants.SwitchSeparate;
				}else if(endnum.length()==2){
					kind="接地刀闸";
					deviceType=SystemConstants.SwitchFlowGroundLine;
				}
			}
			OperationInfo oi = new OperationInfo();
			oi.setEquipName(equipNum + kind);
			oi.setEquipNum(equipNum);
			oi.setEquipType(deviceType);
			oi.setTypeName(kind);
			oiList.add(oi);
		}
		return oiList;
	}
	
	/**
	  * 创建时间 2013年12月3日 下午10:47:28
	  * 根据操作获取设备数字信息
	  * <AUTHOR>
	  * @Title matchEquipNum
	  * @param opr
	  * @return
	  */
	public static String getEquipNum(String opr){
		String reg;
		String out = "";
		if(opr.lastIndexOf(".") >= 0){
			opr = opr.substring(opr.lastIndexOf(".")+1);
		}
		if(opr.contains("-")){
			reg="\\d{1,4}\\-\\d{1,2}";
		}else if(opr.contains("母")||opr.contains("变")){
			reg="[^\u4e00-\u9fa5]";
			if(opr.contains("母联")){
				reg = "\\d{2,}[a-zA-Z]{0,1}";
			}
		}else{
			reg = "\\d{2,}[a-zA-Z]{0,1}";
		}
		
		Pattern pattern = Pattern.compile(reg);
		Matcher matcher = pattern.matcher(opr);
		
		while(matcher.find()) {
			out= matcher.group();
		} 
		return out;
	}
	
	public static boolean isCanDelete(JTable table, int scol, int ccol) {
		TableModel dTableModel = table.getModel();
		int selectRow = table.getSelectedRow();
		String pRowCont = "";
		String pOprUnit = "";
		if(selectRow == -1)
			return false;
		if(dTableModel.getValueAt(selectRow, scol) == null || 
				dTableModel.getValueAt(selectRow, ccol) == null)
			return true;
		String oprUnit = (String) dTableModel.getValueAt(selectRow, scol);
		String cRowCont = (String)dTableModel.getValueAt(selectRow, ccol);
		if (cRowCont.indexOf("开关") != -1) // 当前行是针对开关的操作
		{
			if (cRowCont.indexOf("断开") == 0) // 当前是针对开关的断开操作
			{
				// -------------自当前行出发扫描后续行中是否有针对刀闸的拉开操作------------
				for (int i = selectRow; i < dTableModel
						.getRowCount(); i++) {
					pRowCont = (String) dTableModel
							.getValueAt(i, ccol);
					pOprUnit = (String)dTableModel
							.getValueAt(i, scol);
					if(pRowCont == null || pOprUnit==null)
						continue;

					if (pOprUnit.trim().equals(oprUnit.trim())) // 同属一个厂站内的操作
					{
						//-------------------------判断是否有对刀闸的操作------
						// ---------------------------
						if ((pRowCont.indexOf("线路侧刀闸") != -1)
								|| (pRowCont.indexOf("母线侧刀闸") != -1)
								|| (pRowCont.indexOf("母联") != -1)) {
							if (pRowCont.indexOf("拉开") == 0) // 是针对刀闸的拉开操作
							{
								JOptionPane
										.showMessageDialog(
												null,
												"后续术语中存在["
														+ pRowCont
														+ "],不允许删除当前开关操作术语！",
												CBSystemConstants.SYSTEM_TITLE,
												JOptionPane.INFORMATION_MESSAGE);
								return false;
							}
						}
					}
				}
			}
			// -----------针对开关的合上，同期合上操作是可以删除的--------------------
		}

		if ((cRowCont.indexOf("刀闸") != -1)
				&& (cRowCont.indexOf("接地刀闸") == -1)) // 当前是针对刀闸的删除操作
		{
			//----------------当刀闸为推上状态位时，判断其前或后是否存在另一侧刀闸的推上操作，若有，则可删除
			// ，若没有，则扫描后续操作中是否有针对开关的合操作，若有，则不允许删除
			if (cRowCont.indexOf("推上") == 0) // 当前是针对开关的推上操作
			{
				boolean bExistsOtherKnife = false;
				for (int i = selectRow - 1; i < dTableModel
						.getRowCount(); i++) {
					pRowCont = (String) dTableModel
							.getValueAt(i, ccol);
					pOprUnit = (String)dTableModel
							.getValueAt(i, scol);
					if(pRowCont == null || pOprUnit==null)
						continue;

					if (pOprUnit.trim().equals(oprUnit.trim())) // 同属一个厂站内的操作
					{
						//-------------------------判断是否有对刀闸的操作------
						// ---------------------------
						if ((pRowCont.indexOf("线路侧刀闸") != -1)
								|| (pRowCont.indexOf("母线侧刀闸") != -1)
								|| (pRowCont.indexOf("母联") != -1)) {
							if (!pRowCont.equals(cRowCont)) // 当前不是本次要操作的术语
							// ,
							// 将本术语排除
							{
								if (pRowCont.indexOf("推上") == 0) // 是针对刀闸的推上操作
								{
									bExistsOtherKnife = true;
									return bExistsOtherKnife;
								}

								// --------2009-12-25加上，
								// 用于在手工输入母联开关情况时先推母联刀闸
								// ，然后删除合开关操作情况，这种情况下是不可删除的
								if (pRowCont.indexOf("合上") != -1
										&& pRowCont.indexOf("开关") != -1) {
									JOptionPane
											.showMessageDialog(
													null,
													"后续术语中存在["
															+ pRowCont
															+ "],不允许删除当前刀闸操作术语！",
													CBSystemConstants.SYSTEM_TITLE,
													JOptionPane.INFORMATION_MESSAGE);
									return false;
								}
							}
						}
					}
				}

				if (!bExistsOtherKnife) // 当前不存在对另把刀闸的操作
				{
					for (int i = selectRow; i < dTableModel
							.getRowCount(); i++) {
						pRowCont = (String) dTableModel
								.getValueAt(i, ccol);
						pOprUnit = (String)dTableModel
								.getValueAt(i, scol);
						if(pRowCont == null || pOprUnit==null)
							continue;

						if (pOprUnit.trim().equals(oprUnit.trim())) // 同属一个厂站内的操作
						{
							//-----------------------后继术语中有对本站开关的合操作
							// ,则也不允许删除唯一剩余的刀闸操作------------------
							if ((pRowCont.indexOf("开关") != -1)
									&& ((pRowCont.indexOf("合上") == 0) || (pRowCont
											.indexOf("同期合上") == 0))) {
								JOptionPane
										.showMessageDialog(
												null,
												"后续术语中存在["
														+ pRowCont
														+ "],不允许删除当前刀闸操作术语！",
												CBSystemConstants.SYSTEM_TITLE,
												JOptionPane.INFORMATION_MESSAGE);
								return false;
							}
						}
					}
				}
			} else if (cRowCont.indexOf("拉开") == 0) {
				JOptionPane.showMessageDialog(null,
						"停电操作不允许删除当前刀闸操作术语！",
						CBSystemConstants.SYSTEM_TITLE,
						JOptionPane.INFORMATION_MESSAGE);
				return false;
			}
		}
		if ((cRowCont.indexOf("确认") != -1)
				&& (cRowCont.indexOf("冷备用") != -1)) {
			JOptionPane.showMessageDialog(null, "[" + cRowCont
					+ "]不能删除！", CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.INFORMATION_MESSAGE);
			return false;
		}
		if (cRowCont.indexOf("由冷备用转检修") != -1
				|| cRowCont.indexOf("由检修转冷备用") != -1) {
			JOptionPane.showMessageDialog(null, "[" + cRowCont
					+ "]不能删除！", CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.INFORMATION_MESSAGE);
			return false;
		}
		if ((cRowCont.indexOf("合上") != -1)
				&& (cRowCont.indexOf("开关") != -1)) {
			JOptionPane.showMessageDialog(null, "[" + cRowCont
					+ "]不能删除！", CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.INFORMATION_MESSAGE);
			return false;
		}
		if ((cRowCont.indexOf("断开") != -1)
				&& (cRowCont.indexOf("开关") != -1)) {
			JOptionPane.showMessageDialog(null, "[" + cRowCont
					+ "]不能删除！", CBSystemConstants.SYSTEM_TITLE,
					JOptionPane.INFORMATION_MESSAGE);
			return false;
		}
		return true;
	}
	
	public static boolean isCanMoveDown(JTable table, int ccol) {
		if(table.getSelectedRow() >= table.getRowCount()-1)
			return true;
		TableModel dTableModel = table.getModel();
		if(dTableModel.getValueAt(table.getSelectedRow(), ccol) == null ||
				dTableModel.getValueAt(table.getSelectedRow() + 1, ccol) == null)
			return true;
		String cRowCont = ((String) dTableModel.getValueAt(table.getSelectedRow(), ccol)).replace("。", "");;
		String nRowCont = ((String) dTableModel.getValueAt(table.getSelectedRow() + 1, ccol)).replace("。", "");;
		if(nRowCont == null)
			return false;
		boolean bCanMove = true; //20130523
		//20091223改为线路转运行、转检修时只允许两个开关位置之间相互移动
		if ((cRowCont.indexOf("断开") != -1)
				&& (cRowCont.indexOf("开关") != -1)
				&& (nRowCont.indexOf("断开") != -1)
				&& (nRowCont.indexOf("开关") != -1))
			bCanMove = true;
		else if ((cRowCont.indexOf("合上") != -1)
				&& (cRowCont.indexOf("开关") != -1)
				&& (nRowCont.indexOf("合上") != -1)
				&& (nRowCont.indexOf("开关") != -1))
			bCanMove = true;
		if(!bCanMove)
			JOptionPane.showMessageDialog(null, "[" + cRowCont + "]不能移动至["
					+ nRowCont + "]之后位置！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
		return bCanMove;
	}
	
	public static boolean isCanMoveUp(JTable table, int ccol) {
		if(table.getSelectedRow() < 1)
			return true;
		TableModel dTableModel = table.getModel();
		if(dTableModel.getValueAt(table.getSelectedRow(), ccol) == null ||
				dTableModel.getValueAt(table.getSelectedRow() - 1, ccol) == null)
			return true;
		String cRowCont = ((String) dTableModel.getValueAt(table.getSelectedRow(), ccol)).replace("。", "");
		String pRowCont = ((String) dTableModel.getValueAt(table.getSelectedRow() - 1, ccol)).replace("。", "");;
		boolean bCanMove = true; //20130523
		//20091223改为线路转运行、转检修时只允许两个开关位置之间相互移动
		if ((cRowCont.indexOf("断开") != -1)
				&& (cRowCont.indexOf("开关") != -1)
				&& (pRowCont.indexOf("断开") != -1)
				&& (pRowCont.indexOf("开关") != -1))
			bCanMove = true;
		else if ((cRowCont.indexOf("合上") != -1)
				&& (cRowCont.indexOf("开关") != -1)
				&& (pRowCont.indexOf("合上") != -1)
				&& (pRowCont.indexOf("开关") != -1))
			bCanMove = true;
		if(!bCanMove)
			JOptionPane.showMessageDialog(null, "[" + cRowCont + "]不能移动至["
					+ pRowCont + "]之前位置！", "操作票提示框",
					JOptionPane.WARNING_MESSAGE);
		return bCanMove;
	}
}

package czprule.wordcard;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.CZPImpl;

import czprule.model.PowerDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprule.wordcard.replaceclass.WordCardBuild;


public abstract class WordExecute {
	
	protected static WordExecute wordExecute;
	
	public static WordExecute getInstance() {
		
		if (wordExecute == null) {
			wordExecute=(WordExecute)CZPImpl.getInstance("WordExecute");
			if(wordExecute == null)
				wordExecute = new WordExecuteDefault();
		}
		return wordExecute;
	}
	
	/**
	 * 智能票术语
	 * @param Srcrbm
	 * @return
	 */
	public CardModel execute(RuleBaseMode rbm){
		
	
		return null;
	}
	public CardModel executeFZ(RuleBaseMode rbm){
		
		
		return null;
	}
	public CardModel execute(List<RuleBaseMode> rbmlist) {
		
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		PowerDevice curDev=new PowerDevice();
		//单个设备
		curDev=rbmlist.get(0).getPd();
		//设备集合
		List<PowerDevice> curDevlist = new ArrayList<PowerDevice>();
		
		for(int i=0;i<rbmlist.size();i++){
			PowerDevice curDevlinshi = new PowerDevice();
			curDevlinshi=rbmlist.get(i).getPd();
			curDevlist.add(curDevlinshi);
		}
		String srcStatus=rbmlist.get(0).getBeginStatus();
		String stateCode=rbmlist.get(0).getStateCode();
		String czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		List<String> descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		
		
		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		CardItemModel tempcim=null;
		//操作任务替换
		String result = "";
		String[] rws = czrw.replace("\r\n", "\n").split("\n");
		for (int i = 0; i < rws.length; i++) {
			if(curDevlist.size()>0){
				tempItem= wcb.execute(curDevlist.get(0),rws[i]);	
			}
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				if(!tempcim.getCardDesc().equals("")){
					result = result + tempcim.getCardDesc() + "，";
				}
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);

		if(CBSystemConstants.cardbuildtype.equals("0"))
			results.setCzrw(result);
		else if(CBSystemConstants.roleCode.equals("0"))
			results.setCzrw("");
		else
			results.setCzrw(result);

		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDevlist,desc);
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		for (int i = 0; i < itemModels.size(); i++) {
			itemModels.get(i).setShowName(itemModels.get(i).getStationName());
		}
		
		results.setCardItems(itemModels);
		return results;
	}
	
	/**
	 * 双线路术语合并
	 * @param rbmlist
	 * @return
	 */
	public CardModel executeTwo(RuleBaseMode rbm,RuleBaseMode rbm2,CardModel cm,CardModel cm2) {
		return null;
	}
	
	
	


}

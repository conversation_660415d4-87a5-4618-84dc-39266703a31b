package czprule.wordcard;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.dao.CustomCodexDao;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.rule.view.EquipChooseIssueSuper;
import czprule.rule.view.UnitSelectDialog;
import czprule.system.CBSystemConstants;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.model.CardModel;
import czprule.wordcard.replaceclass.WordCardBuild;

public class WordExecuteDefault extends WordExecute{
	
	/**
	 * 智能票术语
	 * @param Srcrbm
	 * @return
	 */
	public CardModel execute(RuleBaseMode Srcrbm){
		
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CustomCodexDao ccdd = new CustomCodexDao();
		PowerDevice curDev=Srcrbm.getPd();
		String srcStatus=Srcrbm.getBeginStatus();
		String stateCode=Srcrbm.getStateCode();
		String czrw="";//操作任务
		String bzsx="";//备注事项
		List<String> descLists=null;  //操作指令集合
		
		//获取操作任务及术语模板
		String[]userCzrw=ccdd.getCzrw(curDev.getPowerDeviceID(), srcStatus, stateCode, CBSystemConstants.cardbuildtype); //自定义术语
		if("".equals(userCzrw[0].trim())){
		    czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype);
		    bzsx=dsmm.getBZSX(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype);
		    descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);

		}else{
			czrw=userCzrw[0].trim();
			descLists=ccdd.getShuYu(userCzrw[1].trim());
		}
		
		WordCardBuild wcb=new WordCardBuild();
		CardItemModel tempcim=null;
		List<CardItemModel> tempItem=null;
		String result = "";
		
		//获取备注
		String[] bzs = bzsx.replace("\r\n", "\n").split("\n");
		
		for (int i = 0; i < bzs.length; i++) {
			tempItem= wcb.execute(curDev,bzs[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				if(!tempcim.getCardDesc().equals(""))
					result = result + tempcim.getCardDesc() + "，";
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);
		results.setBzsx(result);
		
		//获取操作任务
		result = "";
		String[] rws = czrw.replace("\r\n", "\n").split("\n");
		for (int i = 0; i < rws.length; i++) {
			tempItem= wcb.execute(curDev,rws[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				result = result + tempcim.getCardDesc() + "，";
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);
		results.setCzrw(result);
		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDev,desc);
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		List<DispatchTransDevice> dtdZXList = new ArrayList<DispatchTransDevice>();
		for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
			DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
			dtdZXList.add(dtd);
		}

	
		for (int i = 0; i < itemModels.size(); i++) {
			itemModels.get(i).setShowName(itemModels.get(i).getStationName());
			itemModels.get(i).setCardNum(String.valueOf(i+1));
		}
		//重置步骤
		String preItem = "";
		int curStep = 0;
		for (int i = 0; i < itemModels.size(); i++) {
			String temp = itemModels.get(i).getCardItem();
			if(i == 0) {
				itemModels.get(i).setCardItem("1");
				curStep = 1;
			}
			else if(itemModels.get(i).getCardItem().equals(preItem)) {
				itemModels.get(i).setCardItem(String.valueOf(curStep));
			}
			else {
				curStep++;
				itemModels.get(i).setCardItem(String.valueOf(curStep));
			}
			preItem = temp;
		}
				
		results.setCardItems(itemModels);
		return results;
	}
	
	/**
	 * 术语合并
	 * @param rbmlist
	 * @return
	 */
	public CardModel execute(List<RuleBaseMode> rbmlist) {
		CardModel results=new CardModel(); //返回结果
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		PowerDevice curDev=new PowerDevice();
		//单个设备
		curDev=rbmlist.get(0).getPd();
		//设备集合
		List<PowerDevice> curDevlist = new ArrayList<PowerDevice>();
		
		for(int i=0;i<rbmlist.size();i++){
			PowerDevice curDevlinshi = new PowerDevice();
			curDevlinshi=rbmlist.get(i).getPd();
			curDevlist.add(curDevlinshi);
		}
		String srcStatus=rbmlist.get(0).getBeginStatus();
		String stateCode=rbmlist.get(0).getStateCode();
		String czrw=dsmm.getCZRW(curDev.getDeviceType(), srcStatus, stateCode, CBSystemConstants.cardtype);

		List<String> descLists=dsmm.getStateMents(curDev.getDeviceType(), srcStatus, stateCode,  CBSystemConstants.cardtype, CBSystemConstants.cardbuildtype);
		
		
		WordCardBuild wcb=new WordCardBuild();
		List<CardItemModel> tempItem=null;
		CardItemModel tempcim=null;
		//操作任务替换
		String result = "";
		String[] rws = czrw.replace("\r\n", "\n").split("\n");
		for (int i = 0; i < rws.length; i++) {
			tempItem= wcb.execute(curDevlist,rws[i]);
			if(tempItem.size()>0){
				tempcim=tempItem.get(0);
				if(!tempcim.getCardDesc().equals("")){
					result = result + tempcim.getCardDesc() + "，";
				}
			}
		}
		if(result.endsWith("，"))
			result = result.substring(0, result.length()-1);

		if(CBSystemConstants.cardbuildtype.equals("0"))
			results.setCzrw(result);
		else if(CBSystemConstants.roleCode.equals("0"))
			results.setCzrw("");
		else
			results.setCzrw(result);

		List<CardItemModel>  itemModels=new ArrayList<CardItemModel>();
		for (int i = 0; i < descLists.size(); i++) {
			String desc=descLists.get(i);
			tempItem= wcb.execute(curDevlist,desc);
			for (int j = 0; j < tempItem.size(); j++) {
				itemModels.add(tempItem.get(j));
			}
		}
		
		for (int i = 0; i < itemModels.size(); i++) {
			itemModels.get(i).setShowName(itemModels.get(i).getStationName());
		}
		
		results.setCardItems(itemModels);
		return results;
	}
	
}

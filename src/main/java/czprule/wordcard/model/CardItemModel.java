package czprule.wordcard.model;

import java.util.UUID;

public class CardItemModel {
	
	private String CardNum="";  //序号
	private String CardItem="";  //顺序
	private String StationName="";   //变电站操作单位
	private String ShowName="";   //用于显示的名称
	private String CardDesc="";   //术语
	private String uuIds="";
	private String bzbj="";    //步骤标记
	private String remark="";  //操作提醒
	private boolean twoDevice = false;//对二次设备操作
	private String orderNumber="";  //下令顺序
	private String bdzName="";//变电站名称
	private String zlxh="";//指令序号
	private String czdwID="";//操作单位ID
	private String preMxid="";//顺控成票要记录以前的id

	public boolean getTwoDevice() {
		return twoDevice;
	}
	public String getBdzName() {
		return bdzName;
	}
	public void setBdzName(String bdzName) {
		this.bdzName = bdzName;
	}
	public void setTwoDevice(boolean twoDevice) {
		this.twoDevice = twoDevice;
	}
	public String getBzbj() {
		return bzbj;
	}
	public void setBzbj(String bzbj) {
		this.bzbj = bzbj;
	}
	public String getCardNum() {
		return CardNum;
	}
	public void setCardNum(String cardNum) {
		CardNum = cardNum;
	}
	public String getCardItem() {
		return CardItem;
	}
	public void setCardItem(String cardItem) {
		CardItem = cardItem;
	}
	public String getStationName() {
		return StationName;
	}
	public void setStationName(String stationName) {
		StationName = stationName;
	}
	public String getShowName() {
		return ShowName;
	}
	public void setShowName(String showName) {
		ShowName = showName;
	}
	public String getCardDesc() {
		return CardDesc;
	}
	public void setCardDesc(String cardDesc) {
		CardDesc = cardDesc;
	}
	public String getUuIds() {
		if("".equals(uuIds.trim())){
			UUID uuid = UUID.randomUUID();
			return uuid.toString();
		}else
		    return uuIds;
	}
	public void setUuIds(String uuIds) {
		this.uuIds = uuIds;
	}
	@Override
	public String toString() {
		return "CardItemModel [CardDesc=" + CardDesc + "]";
	}
	public String getRemark() {
		return remark;
	}
	public void setRemark(String remark) {
		this.remark = remark;
	}
	public String getOrderNumber() {
		return orderNumber;
	}
	public void setOrderNumber(String orderNumber) {
		this.orderNumber = orderNumber;
	}
	public String getZlxh() {
		return zlxh;
	}
	public void setZlxh(String zlxh) {
		this.zlxh = zlxh;
	}
	public String getCzdwID() {
		return czdwID;
	}
	public void setCzdwID(String czdwID) {
		this.czdwID = czdwID;
	}
	public String getPreMxid() {
		return preMxid;
	}
	public void setPreMxid(String preMxid) {
		this.preMxid = preMxid;
	}
	
}

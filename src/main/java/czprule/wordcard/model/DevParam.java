package czprule.wordcard.model;

import java.util.ArrayList;
import java.util.List;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2013年8月17日 上午12:45:22 
 */
public class DevParam {

	private String devDesc = "";
	private String devType = "";
	private String devSetType = "";
	private String devRunModel = "";
	private String devSrcStatus = "";
	private String devTagStatus = "";
	private String devBeginStatus = "";
	private String devEndStatus = "";
	private String devVol = "";
	private String devMinVol = "";
	private String devProp = "";
	private String devControl = "";
	private List<String> excDevRunTypeList = new ArrayList<String>();//新增排除设备安装类型
	
	public List<String> getExcDevRunTypeList() {
		return excDevRunTypeList;
	}
	public void setExcDevRunTypeList(List<String> excDevRunTypeList) {
		this.excDevRunTypeList = excDevRunTypeList;
	}
	public String getDevDesc() {
		return devDesc;
	}
	public void setDevDesc(String devDesc) {
		this.devDesc = devDesc;
	}
	public String getDevRunModel() {
		return devRunModel;
	}
	public void setDevRunModel(String devRunModel) {
		this.devRunModel = devRunModel;
	}
	public String getDevType() {
		return devType;
	}
	public void setDevType(String devType) {
		this.devType = devType;
	}
	public String getDevSetType() {
		return devSetType;
	}
	public void setDevSetType(String devRunType) {
		this.devSetType = devRunType;
	}
	public String getDevSrcStatus() {
		return devSrcStatus;
	}
	public void setDevSrcStatus(String devSrcStatus) {
		this.devSrcStatus = devSrcStatus;
	}
	public String getDevTagStatus() {
		return devTagStatus;
	}
	public void setDevTagStatus(String devTagStatus) {
		this.devTagStatus = devTagStatus;
	}
	public String getDevBeginStatus() {
		return devBeginStatus;
	}
	public void setDevBeginStatus(String devBeginStatus) {
		this.devBeginStatus = devBeginStatus;
	}
	public String getDevEndStatus() {
		return devEndStatus;
	}
	public void setDevEndStatus(String devEndStatus) {
		this.devEndStatus = devEndStatus;
	}
	public String getDevVol() {
		return devVol;
	}
	public void setDevVol(String devVol) {
		this.devVol = devVol;
	}
	public String getDevProp() {
		return devProp;
	}
	public void setDevProp(String devProp) {
		this.devProp = devProp;
	}
	public String getDevMinVol() {
		return devMinVol;
	}
	public void setDevMinVol(String devMinVol) {
		this.devMinVol = devMinVol;
	}
	public String getDevControl() {
		return devControl;
	}
	public void setDevControl(String devControl) {
		this.devControl = devControl;
	}
	
	
}

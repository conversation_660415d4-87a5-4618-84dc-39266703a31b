package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrSDDYDCKG implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if("双端电源对侧开关".equals(tempStr)){
			//查找线路
			PowerDevice circuit=null;
			List<PowerDevice> circuits=RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, null, null, CBSystemConstants.RunTypeSideMother, false, true,true, true,"0");
			if(circuits.size()==0){
				return null;
			}else{
			circuit=circuits.get(0);
			}
			String moc=circuit.getDeviceRunModel();
			//单双电源线路判断
			if(moc.equals(CBSystemConstants.RunModelOneLine)){
				return null;
			}
			//对侧厂站线路
			PowerDevice otheresidecircuit=null;
			List<PowerDevice> othersidecircuits=RuleExeUtil.getLineOtherSideList(circuit);
			if(othersidecircuits.size()==0){
				return null;
			}else{
				otheresidecircuit=othersidecircuits.get(0);
			}
			//对侧厂站开关
			PowerDevice othersideswith=null;
			List<PowerDevice> othersideswiths=RuleExeUtil.getDeviceList(otheresidecircuit, SystemConstants.Switch, null, CBSystemConstants.RunTypeSwitchXL, null, false, true, true, true, "0");
			if(othersideswiths.size()==0){
				return null;
			}else{
				othersideswith=othersideswiths.get(0);
			}
			replaceStr=othersideswith.getPowerDeviceName();
//			Map<Integer, DispatchTransDevice> dtdmap=CBSystemConstants.getDtdMap();
//			DispatchTransDevice dtd=dtdmap.get(1);
//			PowerDevice pd=dtd.getTransDevice();
//			replaceStr=pd.getPowerDeviceName();
		}
		return replaceStr;
	}

}

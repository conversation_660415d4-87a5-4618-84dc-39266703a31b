package czprule.wordcard.replaceclass.impl;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrSBNUMBH implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if ("设备数字编号".equals(tempStr)) {
			String[] roma = {"Ⅰ","Ⅱ"};
			String[] num = {"1","2"};

			replaceStr = CZPService.getService().getDevNum(curDev);
			
			for(int i=0;i<roma.length;i++){
				if(replaceStr.equals(roma[i])){
					replaceStr = num[i];
				}
			}
			
			replaceStr = replaceStr.replace("#", "");
		}
		
		return replaceStr;
	}

}

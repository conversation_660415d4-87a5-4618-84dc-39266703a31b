package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBGYCMX implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		// TODO Auto-generated method stub
		//主变高压侧母线
		String replaceStr = null;
		List<PowerDevice> highSideMLList = new ArrayList<PowerDevice>();
		if("主变高压侧母线".equals(tempStr)){
			 if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)){
				 //List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev,SystemConstants.MotherLine , null, false, true, true);
				 List<PowerDevice> mlList =RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer, null, null, false, false, true, true);
				 for (PowerDevice ml : mlList) {
					 if(!highSideMLList.contains(ml))
						 highSideMLList.add(ml);
				 }
				 replaceStr = CZPService.getService().getDevName(highSideMLList);
			 }
		}
		
		return replaceStr;
	}

}

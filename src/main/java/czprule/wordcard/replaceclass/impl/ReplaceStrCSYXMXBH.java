package czprule.wordcard.replaceclass.impl;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCSYXMXBH implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
//		if("初始运行母线编号".equals(tempStr)){
//			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
//			for(int i=1;i<dtds.size()+1;i++){
//				DispatchTransDevice dtd=dtds.get(i);
//				PowerDevice pd=dtd.getTransDevice();
//				if(pd.getDeviceType().equals(SystemConstants.Switch)&&pd.getPowerStationID().equals(curDev.getPowerStationID())){
//					List<PowerDevice> lines=RuleExeUtil.getDeviceList(pd, null, SystemConstants.InOutLine, null, null, null, false, true, true, true);
//					if(lines.size()==0){
//						return null;
//					}
//					PowerDevice line=lines.get(0);
//					if(line.equals(curDev)){
//						List<PowerDevice> daozha=RuleExeUtil.getDeviceList(pd, null, SystemConstants.SwitchSeparate, null, null, null, false, true, true, true);
//						if(daozha.size()==0){
//							
//						}else{
//							for(int j=0;j<daozha.size();j++){
//								PowerDevice dev=daozha.get(j);
//								String statu=dev.getDeviceStatus();
//								if(statu.equals("0")){
//									List<PowerDevice> ms=RuleExeUtil.getDeviceList(dev, null, SystemConstants.MotherLine, null, null, CBSystemConstants.RunTypeSideMother, true, true, true, true);
//									if(ms.size()==0){
//										
//									}else{
//										for(int k=0;k<ms.size();k++){
//											PowerDevice dev1=ms.get(k);
//											if("".equals(replaceStr))
//												replaceStr=CZPService.getService().getDevNum(dev1.getPowerDeviceName());
//										}
//									}
//								}else{
//									for(int k=1;k<CBSystemConstants.getDtdMap().size()+1;k++){
//										PowerDevice dtdpd=CBSystemConstants.getDtdMap().get(k).getTransDevice();
//										if(dtdpd.equals(dev)){
//											List<PowerDevice> ms=RuleExeUtil.getDeviceList(dev, null, SystemConstants.MotherLine, null, null, CBSystemConstants.RunTypeSideMother, true, true, true, true);
//											if(ms.size()==0){
//												
//											}else{
//												for(int kk=0;kk<ms.size();kk++){
//													PowerDevice dev1=ms.get(kk);
//													if("".equals(replaceStr))
//														replaceStr=CZPService.getService().getDevNum(dev1.getPowerDeviceName());
//												}
//											}
//										}
//									}
//								}
//							}
//						}
//					}
//				}
//			}
//		}
		
		if("初始运行母线编号".equals(tempStr)){
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			for(int i=1;i<dtds.size()+1;i++){
				DispatchTransDevice dtd=dtds.get(i);
				PowerDevice pd=dtd.getTransDevice();
				if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX)
					&&pd.getPowerStationID().equals(stationDev.getPowerStationID())
					&&dtd.getBeginstatus().equals("0")){
					List<PowerDevice> ms=RuleExeUtil.getDeviceDirectList(pd,SystemConstants.MotherLine);
					if(ms.size()>0){
						PowerDevice mlDev = ms.get(0);
						if(mlDev != null&&mlDev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
						     replaceStr=CZPService.getService().getDevNum(mlDev.getPowerDeviceName());
						}
					}else{
						for(int k=0;k<ms.size();k++){
							PowerDevice dev1=ms.get(k);
							if("".equals(replaceStr))
								replaceStr=CZPService.getService().getDevNum(dev1.getPowerDeviceName());
						}
					}
				}
			}
		}
		
		if(!replaceStr.equals(""))
			replaceStr=replaceStr+"母";
		return replaceStr;
	}
}

package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrCZDYDJ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("厂站电压等级".equals(tempStr)) {
			PowerDevice st = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			if(st.getDeviceType().equals(SystemConstants.InOutLine)) {
				st = CBSystemConstants.getPowerStation(st.getPowerStationID());
			}
			if(st!=null) {
				String volt = String.valueOf(st.getPowerVoltGrade());
				replaceStr = volt.substring(0, volt.indexOf(".")) + "kV";
			}
		}
		return replaceStr;
	}

}

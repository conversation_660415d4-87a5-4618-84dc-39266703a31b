package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYXMXBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("运行母线编号".equals(tempStr)){
			
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			DispatchTransDevice dtd=null;
			PowerDevice dev=null;									
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        List<PowerDevice> motherList=new ArrayList<PowerDevice>();
	        List searchDevs=null;
			for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
				 dtd=(DispatchTransDevice)iterator.next();
				 dev=dtd.getTransDevice();
				 if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
					   inPara.put("oprSrcDevice", dev);
				       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
				       inPara.put("isSearchDirectDevice", "true");
				       cs.execute(inPara, outPara);
				       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
					   if(searchDevs.size()==0)
						   continue;
					   dev=(PowerDevice)searchDevs.get(0);
					   if(dtd.getEndstate().equals("0") && 
							   dev.getPowerStationID().equals(stationDev.getPowerStationID()) && 
							   !motherList.contains(dev) &&
							   dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
						   motherList.add(dev);
				 }
			}
			if(motherList.size() != 0) {
				for (int k = 0; k < motherList.size(); k++) {
					dev = motherList.get(k);
					if("".equals(replaceStr))
						replaceStr=dev.getPowerDeviceName().replace(dev.getPowerStationName(), "");
					else
						replaceStr=replaceStr+"、"+dev.getPowerDeviceName().replace(dev.getPowerStationName(), "");
				}
				replaceStr = StringUtils.getEquipName(replaceStr);
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMLKG implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("母联开关".equals(tempStr)){
			
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			DispatchTransDevice dtd=null;
			PowerDevice devML=null;									
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        List searchDevs=null;
			for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
				 dtd=(DispatchTransDevice)iterator.next();
				 PowerDevice dev=dtd.getTransDevice();
				 if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
					   inPara.put("oprSrcDevice", dev);
				       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
				       inPara.put("isSearchDirectDevice", "true");
				       cs.execute(inPara, outPara);
				       inPara.clear();
				       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
					   if(searchDevs.size()==0)
						   continue;
					   if(!((PowerDevice)searchDevs.get(0)).getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)) {
						   devML=(PowerDevice)searchDevs.get(0);
						   break;
					   }
				 }
			}
			
			if(devML == null && stationDev.getDeviceType().equals(SystemConstants.MotherLine)) {
				devML = stationDev;
			}
			if(devML != null) {
				PowerDevice mlSwitch=null;
				inPara.put("oprSrcDevice", devML);
                inPara.put("tagDevType", SystemConstants.Switch);
                cs.execute(inPara, outPara);
        		inPara.clear();
        		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
        		for (int n = 0; n < tempSwitchs.size(); n++) {
        			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(n);
        			if(!tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
        				continue;
                    if(RuleExeUtil.isSwitchDoubleML(tempSwitch)){
                    	mlSwitch = tempSwitch;
                    	break;
                    }
                    else if(tempSwitch.getDeviceSetType().equals(CBSystemConstants.RunTypeSwitchMLPL))
                    	mlSwitch = tempSwitch;
        		}
				if(mlSwitch != null)
					replaceStr=CZPService.getService().getDevName(mlSwitch);
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.ReplaceUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrKGMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("开关名称".equals(tempStr)) {
			// 此术语在当前设备为开关才有用
			PowerDevice sw = RuleExeUtil.getDeviceSwitch(stationDev);
			if(sw != null)
				replaceStr = CZPService.getService().getDevName(sw);
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrFHCCZMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if ("负荷侧厂站名称".equals(tempStr)) {
					
			//replaceStr = StringUtils.getStationName(station);
			
			if(CBSystemConstants.LineLoad
					.get(curDev.getPowerDeviceID()).size() > 0) {
				PowerDevice station = CBSystemConstants.LineLoad
						.get(curDev.getPowerDeviceID()).get(0);
				if(station.getDeviceType().equals(SystemConstants.PowerStation)){
					replaceStr = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(station.getPowerDeviceID()));
				}else{
					replaceStr = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(station.getPowerStationID()));
				}
				
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.WordCardBuild;
public class ReplaceStrDZZBC implements TempStringReplace{

	private int countKnife=0;//刀闸母线侧出现次数

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("刀闸主变侧".equals(tempStr)){
			replaceStr=WordCardBuild.getSideName(curDev, SystemConstants.PowerTransformer, desc,countKnife);
			if(replaceStr==null||replaceStr.equals("")){
				return null;
			}
			replaceStr+="主变侧";
		}
		return replaceStr;
	}

}

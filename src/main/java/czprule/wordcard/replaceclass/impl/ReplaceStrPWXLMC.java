package czprule.wordcard.replaceclass.impl;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprulepw.PWSystemConstants;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrPWXLMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网线路名称".equals(tempStr)){
			PowerDevice dev = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			
			if(dev != null){
				if(dev.getDeviceType().equals(SystemConstants.InOutLine))
					replaceStr=CZPService.getService().getDevName(dev);
				if(replaceStr.equals("")) {
					
					PowerDevice pwDev = null;
					//根据主线开关找线路ID
					if(stationDev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)) {
						String lineID = "";
						SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
						
						if(SystemConstants.getGuiBuilder()!=null){
							if(CBSystemConstants.isCurrentSys)
								lineID = resolver.getLineID(SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgDocument());
							if(!lineID.equals(""))
								replaceStr = CZPService.getService().getDevName(CBSystemConstants.getMapPowerFeeder().get(lineID)).replace("大馈线", "");
							
							List<PowerDevice> linkdevList = RuleExeUtil.getDeviceDirectList(stationDev, "");
							for(PowerDevice linkdev  : linkdevList) {
								if(linkdev.isPW()) {
									pwDev = linkdev;
									break;
								}
							}
							if(pwDev == null) {
								for(PowerDevice linkdev  : linkdevList) {
									List<PowerDevice> linklinkdevList = RuleExeUtil.getDeviceDirectList(linkdev, "");
									for(PowerDevice linklinkdev  : linklinkdevList) {
										if(linklinkdev.isPW()) {
											pwDev = linklinkdev;
											break;
										}
									}
								}
							}
						}
					}
					else {
						pwDev = stationDev;
					}
					if(pwDev!=null) {
						
						
						
						List<Map> list = DBManager.queryForList("select distinct b.line_id from "+CBSystemConstants.equipUser+"T_C_TERMINAL a,"+CBSystemConstants.equipUser+"T_PD_EQUIPINFO b where CONNECTIVITYNODE_ID in(select CONNECTIVITYNODE_ID from "+CBSystemConstants.equipUser+"T_C_TERMINAL where equip_id='"+pwDev.getPowerDeviceID()+"') and a.equip_id= b.equip_id and line_id is not null");
			            if(list.size() > 0 && list.get(0).get("line_id")!= null && !list.get(0).get("line_id").equals("")) {
			            	String line_id = list.get(0).get("line_id").toString();
			            	replaceStr = CZPService.getService().getDevName(CBSystemConstants.getMapPowerFeeder().get(line_id));
			            }
			            if(replaceStr.equals("")) {
			            	CommonSearch cs = new CommonSearch();
							Map<String, Object> inPara = new HashMap<String, Object>();
							Map<String, Object> outPara = new HashMap<String, Object>();
							inPara.put("oprSrcDevice", curDev);
							inPara.put("tagDevType", SystemConstants.InOutLine); // 目标设备线路
							inPara.put("excDevType", SystemConstants.PowerTransformer);//排除搜索主变，
							cs.execute(inPara, outPara);
							List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
							if (searchDevs.size() > 0) {//如果有线路，则用线路的设备名称来代替线路名称。
								PowerDevice tempdev = (PowerDevice) searchDevs.get(0);
								replaceStr = CZPService.getService().getDevName(tempdev);
							}
			            }
						
					}
					else if(CBSystemConstants.getMapPowerFeeder().containsKey(stationDev.getPowerStationID())) {
						replaceStr = CZPService.getService().getDevName(CBSystemConstants.getMapPowerFeeder().get(stationDev.getPowerStationID()));
					}
					else if(stationDev.getDevice()!=null && !stationDev.getDevice().equals("")) {
						
						 replaceStr = CZPService.getService().getDevName(CBSystemConstants.getMapPowerFeeder().get(stationDev.getDevice()));
					
						 
					}
				}
			}
	    }
		//replaceStr = replaceStr.replace("电站", "");
		return replaceStr;
	}

}

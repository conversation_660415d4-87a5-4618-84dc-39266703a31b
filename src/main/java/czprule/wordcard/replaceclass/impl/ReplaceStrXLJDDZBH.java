package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXLJDDZBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("线路接地刀闸编号".equals(tempStr))
		{
			PowerDevice xlGroundKnife=null;
			
			List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(stationDev, SystemConstants.SwitchFlowGroundLine);
			
			
        	if(list.size() > 0)
        	{
        		replaceStr=CZPService.getService().getDevNum(list.get(0).getPowerDeviceName());
        	}
		}
		return replaceStr;
	}

}

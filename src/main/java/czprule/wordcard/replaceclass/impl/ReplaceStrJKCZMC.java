package czprule.wordcard.replaceclass.impl;


import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * 用于获取操作指令的受令单位
 * <AUTHOR>
public class ReplaceStrJKCZMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		String stationName = "";
		if(!"监控厂站".equals(tempStr)){
			return desc;
		}
		
		String dwmc = "";
		String sql = "select b.dwmc from opcard.T_NW_ZDDDGXFW a,opcard.T_NW_ZDDDGXFW b where a.fjdw=b.organid and a.dispatchid='"+stationDev.getPowerStationID()+"'";
		List org=DBManager.queryForList(sql);
		if(org !=null && org.size()>0){
			Map map=new HashMap();
			map=(Map) org.get(0);
			dwmc = StringUtils.ObjToString(map.get("dwmc"));
			stationName = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stationDev.getPowerStationID()));
			if(dwmc.equals(stationName))
				return "";
			else
				return stationName;
		}
		else
			stationName = "";
//	    if(stationDev.getDeviceType().equals(SystemConstants.PowerStation) || stationDev.getDeviceType().equals(SystemConstants.PowerFactory))
//	    	stationName=CZPService.getService().getDevName(stationDev);
//		else
//			stationName=CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stationDev.getPowerStationID()));
	    replaceStr = stationName;
	    return replaceStr;
	}
}

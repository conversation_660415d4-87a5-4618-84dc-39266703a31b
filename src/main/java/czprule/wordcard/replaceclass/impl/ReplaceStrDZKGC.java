package czprule.wordcard.replaceclass.impl;

import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDZKGC implements TempStringReplace{

	private int countKnife=0;

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("刀闸开关侧".equals(tempStr)){
			if(curDev.getDeviceType().equals(SystemConstants.Switch)){
				List<PowerDevice> glines = RuleUtil.getDirectDevice(curDev,SystemConstants.SwitchFlowGroundLine);
				if(glines==null||glines.size()==0){
					List<PowerDevice> rms = CBSystemConstants.getlinkRMDevice(curDev);
					Iterator<PowerDevice> iter=rms.iterator();
					while(iter.hasNext()) {
						PowerDevice rm =iter.next();
						if(desc.contains("挂")&&rm.getDeviceStatus().equals("0")){
							if(RuleUtil.isDeviceInDtdMap(rm)){
								continue;
							}
						}
						if(desc.contains("拆")&&rm.getDeviceStatus().equals("1")){
							if(RuleUtil.isDeviceInDtdMap(rm)){
								continue;
							}
						}
						iter.remove();
					}
					if(rms.size()>countKnife){
						PowerDevice rm = rms.get(countKnife);
						PowerDevice knife = CBSystemConstants.getPowerDevice(curDev.getPowerStationID(), rm.getKnife());
						String knifename = knife.getPowerDeviceName();
						if(!knifename.contains("刀闸")){
							knifename+="刀闸"; 
						}
						replaceStr=knifename+"开关侧";
						countKnife++;
					}
				}else {
					for (Iterator<PowerDevice> iterator = glines.iterator(); iterator.hasNext();) {
						PowerDevice line = (PowerDevice)iterator.next();
						if(desc.contains("挂")&&line.getDeviceStatus().equals("0"))
							continue;
						if(desc.contains("拆")&&line.getDeviceStatus().equals("1"))
							continue;
						iterator.remove();
					}
					if(glines.size()>countKnife){
						PowerDevice line = glines.get(countKnife);
						List<PowerDevice> knife = RuleUtil.getDirectDevice(curDev, line, SystemConstants.SwitchSeparate);
						if(knife!=null&&knife.size()!=0){
							replaceStr=knife.get(0).getPowerDeviceName()+"开关侧";
							countKnife++;
						}
					}
				}
			}
			if(replaceStr==null||replaceStr.equals("")){
				return null;
			}
		}
		return replaceStr;
	}

}

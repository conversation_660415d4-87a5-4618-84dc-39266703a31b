package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMBMXMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("目标母线名称".equals(tempStr)) {

			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants
					.getDtdMap();
			DispatchTransDevice dtd = null;
			PowerDevice dev = null;
			CommonSearch cs = new CommonSearch();
			Map<String, Object> inPara = new HashMap<String, Object>();
			Map<String, Object> outPara = new HashMap<String, Object>();
			List<PowerDevice> motherList = new ArrayList<PowerDevice>();
			List searchDevs = null;
			for (Iterator iterator = dtds.values().iterator(); iterator
					.hasNext();) {
				dtd = (DispatchTransDevice) iterator.next();
				dev = dtd.getTransDevice();
				if (dev.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
					inPara.put("oprSrcDevice", dev);
					inPara.put("tagDevType", SystemConstants.MotherLine); // 目标设备母线
					inPara.put("isSearchDirectDevice", "true");
					cs.execute(inPara, outPara);
					searchDevs = (ArrayList) outPara.get("linkedDeviceList");
					if (searchDevs.size() == 0)
						continue;
					 List<PowerDevice> swList=RuleExeUtil.getKnifeRelateSwitch(dev);
					   if(swList.size() > 0 && swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
						   continue;
					PowerDevice ml = (PowerDevice) searchDevs.get(0);
					if (dtd.getEndstate().equals("0")
							&& ml.getPowerStationID().equals(
									stationDev.getPowerStationID())
							&& !motherList.contains(ml)
							&& !ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)
							&& !ml.equals(stationDev)
							&& ml.getDeviceRunModel().equals(
									CBSystemConstants.RunModelDoubleMotherLine))
						motherList.add(ml);
				}
			}
			if (motherList.size() == 0) {
				return null;
			}
			for (int k = 0; k < motherList.size(); k++) {
				//去除本操作母线
				if(!motherList.get(k).equals(curDev)){
					dev = motherList.get(k);
				}
				if ("".equals(replaceStr)){
					replaceStr = CZPService.getService().getDevName(dev);
				}
				else{
					replaceStr = replaceStr
							+ "、"
							+ CZPService.getService().getDevName(dev);
				}
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBDYCKGBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("主变低压侧开关编号".equals(tempStr)){
			double vol = RuleExeUtil.getTransformerVolByType(curDev, "low");
			List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchKnife(curDev, vol);
			for(PowerDevice sw : swList) {
				if(sw.getDeviceType().equals(SystemConstants.Switch)) {
					replaceStr = StringUtils.getSwitchCode(sw.getPowerDeviceName());
					break;
				}
			}
		}
		return replaceStr;
	}

}

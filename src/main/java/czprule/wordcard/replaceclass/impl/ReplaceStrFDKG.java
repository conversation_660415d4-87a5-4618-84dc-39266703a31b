package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrFDKG implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
			String replaceStr="";
		if("分段开关".equals(tempStr)){
			List<PowerDevice>  devList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch ,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
					"", false, true, true, true);
			//Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			replaceStr = CZPService.getService().getDevName(devList);
			if (replaceStr.equals(""))
				replaceStr = null;
		}
		return replaceStr;
	}

}

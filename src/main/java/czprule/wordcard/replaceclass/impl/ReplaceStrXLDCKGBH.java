package czprule.wordcard.replaceclass.impl;



import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.securitycheck.view.CheckWord;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXLDCKGBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("线路对侧开关编号".equals(tempStr)){
			PowerDevice another = RuleUtil.getAnotherSideLineByLine(curDev);
			PowerDevice sw = RuleUtil.getlineSwitchOrKnife(another);
			if(sw != null)
				replaceStr=CheckWord.getEquipNum(sw.getPowerDeviceName());
			//cardModel.setStationName(CZPService.getService().getDevName(CBSystemConstants.getPowerStation(sw.getPowerStationID()))); 
			
		}
		return replaceStr;
	}

}

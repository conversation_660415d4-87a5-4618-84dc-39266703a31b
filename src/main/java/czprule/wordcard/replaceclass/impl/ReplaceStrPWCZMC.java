package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrPWCZMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网厂站名称".equals(tempStr)){
			if(stationDev.getDeviceType().equals(SystemConstants.PowerStation) || stationDev.getDeviceType().equals(SystemConstants.PowerFactory))
				replaceStr=CZPService.getService().getDevName(stationDev);
			else {
				PowerDevice dev = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
				if(dev.getDeviceType().equals(SystemConstants.InOutLine)) {
					if(CBSystemConstants.getPowerStation(dev.getPowerStationID())!=null)
						replaceStr=CZPService.getService().getDevName(CBSystemConstants.getPowerStation(dev.getPowerStationID()));
				}
				else
					replaceStr=CZPService.getService().getDevName(dev);
			}
	    }
		//replaceStr = replaceStr.replace("电站", "");
		return replaceStr;
	}

}

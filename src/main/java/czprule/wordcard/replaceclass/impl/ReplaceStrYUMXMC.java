package czprule.wordcard.replaceclass.impl;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYUMXMC implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if(tempStr.equals("于母线名称")){
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			if(swList.size()>0&&swList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
				List<PowerDevice> mxList = RuleExeUtil.getDeviceList(swList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						"",CBSystemConstants.RunTypeSideMother,false, false, true, true);
				if(mxList.size()>0&&mxList.get(0).getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)){
					replaceStr="于"+CZPService.getService().getDevName(mxList.get(0));
				}
			}
			
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.HashMap;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDYC implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("对应侧".equals(tempStr)){
			if(curDev.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)){
				List<PowerDevice> pds = RuleUtil.getDirectDevice(curDev);
				HashMap<String, PowerDevice> map=new HashMap<String, PowerDevice>();
				for (PowerDevice dev : pds) {
					map.put(dev.getDeviceType(),dev);
				}
				if(map.keySet().contains(SystemConstants.MotherLine)&&map.keySet().contains(SystemConstants.SwitchSeparate)){
					replaceStr=map.get(SystemConstants.SwitchSeparate).getPowerDeviceName()+"母线侧";
				}else if(map.keySet().contains(SystemConstants.InOutLine)&&map.keySet().contains(SystemConstants.SwitchSeparate)){
					List<PowerDevice> swt = RuleUtil.getDirectDevice(map.get(SystemConstants.SwitchSeparate), SystemConstants.Switch);
					replaceStr=swt.get(0).getPowerDeviceName()+"线路侧";
				}else if(map.keySet().contains(SystemConstants.Switch)&&map.keySet().contains(SystemConstants.SwitchSeparate)){
					replaceStr=map.get(SystemConstants.SwitchSeparate).getPowerDeviceName()+"开关侧";
				}else if(map.keySet().contains(SystemConstants.PowerTransformer)&&map.keySet().contains(SystemConstants.SwitchSeparate)){
					replaceStr=map.get(SystemConstants.SwitchSeparate).getPowerDeviceName()+"主变侧";
				}
			}
		}
		return replaceStr;
	}

}

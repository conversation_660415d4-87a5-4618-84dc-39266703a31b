package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 */
public class ReplaceStrPWXZKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网选择开关".equals(tempStr)){
			
			if(CBSystemConstants.getSamepdlist().size() > 0)
				replaceStr = CZPService.getService().getDevName(CBSystemConstants.getSamepdlist());
			
			
			
		}
			
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBZYCMX implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		// TODO Auto-generated method stub
		String replaceStr = "";
		if("主变中压侧母线".equals(tempStr)){
			List<PowerDevice> templist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, null, null, null, false, true, true, true);
		 
			List<PowerDevice> midSideMLList = new ArrayList<PowerDevice>();
			 for (PowerDevice device : templist) {
				 if(RuleExeUtil.isSwitchMiddleVolSide(device)) {
					 List<PowerDevice> mlList = RuleExeUtil.getDeviceList(device,SystemConstants.MotherLine , null, false, true, true);
					 for (PowerDevice ml : mlList) {
						 if(!midSideMLList.contains(ml))
							 midSideMLList.add(ml);
					 }
					
				 }
			 }
			 if(midSideMLList.size() > 0)
				 replaceStr = CZPService.getService().getDevName(midSideMLList);
				
		}
		return 	replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.List;
/**
 * Gny
 * 判断是否监控展示
 */
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooJK implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		// TODO Auto-generated method stub
		PowerDevice jkDev = stationDev;
		if (!jkDev.getDeviceType().equals(SystemConstants.Switch)) {
			List<PowerDevice> swList = RuleExeUtil.getDeviceList(jkDev,
					SystemConstants.Switch, SystemConstants.PowerTransformer,
					true, true, true);
			if (swList.size() > 0) {
				for(PowerDevice sw : swList) {
					if(!sw.getSupervisionright_id().equals("")) {
						jkDev = sw;
						break;
					}
				}
			}
		}
		if (SystemConstants.isSupervisory.equals("1")) {
			/**
			 * 有监控展示
			 */
			if (tempStr.equals("监控-展")) {
				if (jkDev.getSupervisionright_id() != null
						&& !jkDev.getSupervisionright_id().trim().equals("")
						&& judgeMethod(jkDev.getPowerStationID())) {
					return true;
				} else {
					return false;
				}
			} else {// 无监控展示
				if (jkDev.getSupervisionright_id() != null
						&& !jkDev.getSupervisionright_id().trim().equals("")
						&& judgeMethod(jkDev.getPowerStationID())) {
					return false;
				} else {
					return true;
				}
			}
		} else {
			if (tempStr.equals("监控-展")) {
				return false;
			} else {// 无监控展示
				return true;			}
		}

	}

	public boolean judgeMethod(String id) {
		for (String sid : SystemConstants.isSControl) {
			if (sid.equals(id)) {
				return true;
			}
		}
		return false;

	}
}

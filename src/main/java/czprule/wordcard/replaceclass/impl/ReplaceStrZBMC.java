package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrZBMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变名称".equals(tempStr)){
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			//操作设备存在主变
			if(curDev.getDeviceType().equals(SystemConstants.PowerTransformer)){
				 String DevName= curDev.getPowerDeviceName();
				    if(DevName.lastIndexOf(".") >= 0){
				    	replaceStr = DevName.substring(DevName.lastIndexOf(".")+1);
				    }			    	
					else{
						replaceStr=curDev.getPowerDeviceName();
					}
			}
		
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
		    inPara.put("oprSrcDevice", curDev);
	        inPara.put("tagDevType", SystemConstants.PowerTransformer); //目标设备主变
	        cs.execute(inPara, outPara);
	        List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
		    if(searchDevs.size()==0&&curDev.getDeviceType().equals(SystemConstants.MotherLine)){
		    	List<PowerDevice> zbList = RuleExeUtil.getDeviceList(curDev, SystemConstants.PowerTransformer, "", true, false, true);
		    	for(PowerDevice zb:zbList){
		    		if(RuleExeUtil.isDeviceInDtd(zb)){
		    			searchDevs.add(zb);
			    	}
		    	}
		    }
		    if(searchDevs.size()>0){
		    	
			    PowerDevice dev=(PowerDevice)searchDevs.get(0);
			    String DevName=CZPService.getService().getDevName(dev);
			    if(DevName.lastIndexOf(".") >= 0)
			    	replaceStr = DevName.substring(DevName.lastIndexOf(".")+1);
			    else{
			    	replaceStr = DevName;
			    }
			    //replaceStr=dev.getPowerDeviceName();
		    }
		    
		    if(replaceStr.contains("#"))
		    	replaceStr= replaceStr.substring(replaceStr.indexOf("#"));
		}
		return replaceStr;
	}

}

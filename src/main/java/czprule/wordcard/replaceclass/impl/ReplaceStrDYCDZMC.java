package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDYCDZMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("电源侧刀闸名称".equals(tempStr)) {
			List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			String a = curDev.getDeviceRunType();
			if(kfList.size() >= 2) {
				for(PowerDevice kf : kfList) {
					if(kf.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}
					if(!RuleExeUtil.isDeviceChanged(kf)){
						continue;
					}
					if(RuleExeUtil.JudgeknifeIsPowerSide(kf).equals("1")) {
						replaceStr = CZPService.getService().getDevName(kf);
						break;
					}
					else if(curDev.getDeviceRunType().equals("lowtransswitch")){
						RuleExeUtil.swapDeviceList(kfList);
						replaceStr = CZPService.getService().getDevName(kfList.get(1));
						break;
					}
					else if(curDev.getDeviceRunType().equals("monthlinelinkswitch")){
						RuleExeUtil.swapDeviceList(kfList);
						replaceStr = CZPService.getService().getDevName(kfList.get(1));
						break;
					}
				}
			}
			
			if (replaceStr.equals(""))
				replaceStr = null;
		}
		return replaceStr;
	}

}

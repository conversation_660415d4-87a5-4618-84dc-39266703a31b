package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * 
 * 查找当前操作线路所关联主变与母线之间的刀闸
 * 
 * <AUTHOR>
 * 
 */
public class ReplaceStrZBCDZ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		PowerDevice pdz = null;
		if ("主变侧刀闸".equals(tempStr)) {
			PowerDevice ptf = null;
			List<PowerDevice> pList = new ArrayList<PowerDevice>(); // 主变
			List<PowerDevice> dzList = new ArrayList<PowerDevice>();// 刀闸
			pList = RuleExeUtil.getDeviceList(curDev,
					SystemConstants.PowerTransformer, null, true, false, true); // 查找对应主变
			if (pList.size() > 0) {
				ptf = pList.get(0);
			}
			dzList = RuleExeUtil.getDeviceList(ptf,
					SystemConstants.SwitchSeparate, null, false, false, true); // 查找对应主变
			if (dzList.size() > 0) {
				for (PowerDevice pdi : dzList) {
					if (pdi.getDeviceRunType().equals(
							CBSystemConstants.RunTypeKnifeZBS)) { // 主变刀闸（代替主变开关）
						pdz = pdi;
						break;
					}
				}
			}
		}
		if(pdz==null){
			return "";
		}
		replaceStr = CZPService.getService().getDevName(pdz);
		replaceStr = replaceStr.substring(replaceStr.indexOf("变")+1); 
		return replaceStr;
	}
}

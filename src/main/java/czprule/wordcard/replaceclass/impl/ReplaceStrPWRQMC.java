package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.service.GetDDTicketZTImpl;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrPWRQMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网容器名称".equals(tempStr)){
			
			PowerDevice room = CBSystemConstants.getPowerDevice(stationDev.getPowerStationID(), stationDev.getRoomID());
		
			if(room!=null){
				replaceStr = CZPService.getService().getDevName(room);
			}else{
				if(!GetDDTicketZTImpl.pwOperationEquipment.equals("")){
					replaceStr = GetDDTicketZTImpl.pwOperationEquipment;
				}
			}
			
	    }
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprulepw.PWSystemConstants;

/**
 */
public class ReplaceStrPWZNKG implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网站内开关".equals(tempStr)){
			
			
			List<PowerDevice> devList = RuleExeUtil.getDeviceList(stationDev, null, SystemConstants.Switch, null,PWSystemConstants.PWRunTypeSwitchZX,PWSystemConstants.PWRunTypeSwitchLL, false, true, true, true);
			if(devList.size() > 0) {
				PowerDevice zxDev = devList.get(0);
				replaceStr = CZPService.getService().getDevName(zxDev);
			}
			
			
			
		}
			
		return replaceStr;
	}

}

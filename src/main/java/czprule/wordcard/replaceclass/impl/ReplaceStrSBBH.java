package czprule.wordcard.replaceclass.impl;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrSBBH implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if ("设备编号".equals(tempStr)) {
			replaceStr = CZPService.getService().getDevNum(curDev);
		}
		return replaceStr;
	}

}

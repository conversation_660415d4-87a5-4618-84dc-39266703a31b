package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDCKG implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = null;
		if ("对侧开关".equals(tempStr)) {
			PowerDevice pd=getDCKG(curDev);
			
			if(pd==null){
			
			}
			else{
				replaceStr =CZPService.getService().getDevName(pd);
				//replaceStr = pd.getPowerDeviceName();
			}
		}

		return replaceStr;
	}
	public static PowerDevice getDCKG(PowerDevice curDev){
		PowerDevice dev = null;
		dev = curDev;
		if (curDev.getDeviceType().equals(SystemConstants.Switch)) {
		
			List<PowerDevice> templist = RuleUtil.getSwitchOrderLines(curDev);
			if(templist==null || templist.size() == 0){
				return null;
			}else{
				dev = templist.get(0);
			}
			
		}
		if (dev != null&& dev.getDeviceType().equals(SystemConstants.InOutLine)) {
			List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(dev);
			if(list.size()>0){
			PowerDevice pd = RuleUtil.getLineSwitch(list.get(0));
			return pd;
			}
		}
		return null;
	}
}

package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBHSSSBMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr=null;
		if(tempStr.equals("保护所属设备名称")){
			String stationid = curDev.getPowerStationID();
			PowerDevice dev = CBSystemConstants.getPowerDevice(stationid, curDev.getDevice());
			replaceStr=CZPService.getService().getDevName(dev);

		}
		return replaceStr;
	}

}

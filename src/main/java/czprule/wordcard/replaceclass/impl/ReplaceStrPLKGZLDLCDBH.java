package czprule.wordcard.replaceclass.impl;

import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPLKGZLDLCDBH implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if(tempStr.equals("旁路开关纵联电流差动保护")){
			PowerDevice pd=ReplaceStrPLKG.getSideSwitch(curDev);
			List<Map> sby=DBManager.queryForList("select t.protectname,  t.protectstatus from "+CBSystemConstants.opcardUser+"t_a_protectequip t,"+CBSystemConstants.opcardUser+"T_A_PROTECTINFO s where equipid='"+pd.getPowerDeviceID()+"' and s.PROTECTTYPENAME='纵联电流差动保护' and t.protecttypeid=s.protecttypeid");
			for (Map  map : sby) {
				String protectname=StringUtils.ObjToString(map.get("protectname"));
				String protectstatus=StringUtils.ObjToString(map.get("protectstatus"));
				replaceStr+=(CZPService.getService().getDevName(pd)+protectname+"纵联电流差动保护;");
			}
		}
		if(replaceStr.indexOf(";")>0){
			replaceStr=replaceStr.substring(0,replaceStr.length()-1);
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}

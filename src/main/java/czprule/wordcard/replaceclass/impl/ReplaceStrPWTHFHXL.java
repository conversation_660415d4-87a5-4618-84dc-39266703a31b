package czprule.wordcard.replaceclass.impl;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprulepw.PWSystemConstants;

/**
 */
public class ReplaceStrPWTHFHXL implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网调回负荷线路".equals(tempStr)){
			
			PowerDevice sdDev = null;
			PowerDevice dhDev = null;
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice pd = dtd.getTransDevice();
				if(pd.getDeviceType().equals(SystemConstants.Switch) && dtd.getBeginstatus().equals("0") && dtd.getEndstate().equals("1"))
					dhDev = pd;
				else if(pd.getDeviceType().equals(SystemConstants.Switch) && dtd.getBeginstatus().equals("1") && dtd.getEndstate().equals("0"))
					sdDev = pd;
			}
			
			if(sdDev!=null && dhDev!=null) {
				
				List<PowerDevice> devList = RuleExeUtil.getDeviceList(dhDev, sdDev, SystemConstants.Switch, null,PWSystemConstants.PWRunTypeSwitchZX,null, false, false, true, true);
				
				PowerDevice zxDev = null;
				if(devList.size() >= 2) {
					for(PowerDevice d : devList) {
						if(RuleExeUtil.getPWLine(d).equals(RuleExeUtil.getPWLine(dhDev)))
							zxDev = d;
					}
				}
				if(zxDev == null && devList.size()>0)
					zxDev = devList.get(0);
				
				if(zxDev != null) {	
					String line_id = "";
					List<Map> list = DBManager.queryForList("select distinct b.line_id from "+CBSystemConstants.equipUser+"T_C_TERMINAL a,"+CBSystemConstants.equipUser+"T_PD_EQUIPINFO b where CONNECTIVITYNODE_ID in(select CONNECTIVITYNODE_ID from "+CBSystemConstants.equipUser+"T_C_TERMINAL where equip_id='"+zxDev.getPowerDeviceID()+"') and a.equip_id= b.equip_id and line_id is not null");
		            if(list.size() > 0 && list.get(0).get("line_id")!= null && !list.get(0).get("line_id").equals("")) {
		            	line_id = list.get(0).get("line_id").toString();
		            	
		         
	            	if(!line_id.equals("")){
						
						String[] arr = line_id.split(",");
						for(String ln : arr) {
							if(!ln.equals(zxDev.getPowerStationID())) {
								PowerDevice line = CBSystemConstants.getMapPowerFeeder().get(ln);
								replaceStr = CZPService.getService().getDevName(CBSystemConstants.getMapPowerStation().get(line.getPowerStationID()))+CZPService.getService().getDevName(line);
								break;
							}
						}
	            	}
		            }
				}
			}
			
			
		}
			
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.List;

/**
 * Gny
 * 判断是否监控展示
 */
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooXBZ implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		boolean ret = false;
		if (tempStr.equals("线变组")) {
			if(RuleExeUtil.getRunMode(stationDev).equals("2")){
				ret=true;
			}
		}
		return ret;
	}
		
}

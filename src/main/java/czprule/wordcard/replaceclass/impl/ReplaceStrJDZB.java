package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDZB implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		// TODO Auto-generated method stub
		String replaceStr = "false";
		if("接地主变".equals(tempStr)){
			
			for (int i = 1; i < CBSystemConstants.getDtdMap().size() + 1; i++) {
				DispatchTransDevice dtd = CBSystemConstants.getDtdMap().get(i);
				PowerDevice pd = dtd.getTransDevice();
				if(dtd.getEndstate().equals("0") && pd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD)) {
					List<PowerDevice> zbList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.PowerTransformer);
					if(zbList.size() > 0 && zbList.get(0).getDeviceStatus().equals("0")) {
						replaceStr = CZPService.getService().getDevName(zbList.get(0));
					}
				}
				else if(dtd.getEndstate().equals("0") && pd.getDeviceType().equals(SystemConstants.PowerTransformer)) {
					List<PowerDevice> curgds = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchFlowGroundLine);
					for (PowerDevice gd : curgds) {
						if(gd.getDeviceRunType().equals(CBSystemConstants.RunTypeGroundZXDDD) && gd.getPowerVoltGrade()==pd.getPowerVoltGrade() && gd.getDeviceStatus().equals("0")) {
							replaceStr = CZPService.getService().getDevName(pd);
							break;
						}
					}
				}
				if(!replaceStr.equals("false"))
					break;
			}
		}
		return 	replaceStr;
	}

}

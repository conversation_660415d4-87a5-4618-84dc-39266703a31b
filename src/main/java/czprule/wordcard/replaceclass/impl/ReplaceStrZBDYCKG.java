package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBDYCKG implements TempStringReplace{

	//主变低压侧开关:可做各地通用

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = null;
		if("主变低压侧开关".equals(tempStr)){
			double vol = RuleExeUtil.getTransformerVolByType(curDev, "low");
			List<PowerDevice> swList = RuleExeUtil.getTransformerSwitchKnife(curDev, vol);
			for(PowerDevice sw : swList) {
				if(sw.getDeviceType().equals(SystemConstants.Switch)) {
					replaceStr = sw.getPowerDeviceName();
					break;
				}
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXLMCCN implements TempStringReplace {
@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		PowerDevice tempdev = new PowerDevice();
		if ("线路名称简称".equals(tempStr)) {
			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants
					.getDtdMap();//获取到了操作过的设备缓存
			if (curDev.getDeviceType().equals(SystemConstants.InOutLine)) {// 如果操作设备设备类型是线路
				tempdev = stationDev;
				} else if (curDev.getDeviceType().equals(SystemConstants.PowerTransformer)&& RuleUtil.isTransformerNQ(curDev)) {//如果操作设备设备类型是主变并且主变是内桥接线
				// 在主变、开关同时停电时，寻找停电的线路
				PowerDevice zbSwitch = RuleExeUtil.getTransformerSwitch(curDev,curDev.getPowerVoltGrade());//查找该主变的主变开关、线路开关（带主变开关）
				if (zbSwitch != null) {//如果查找到的主变开关不为空
					List<PowerDevice> searchDevs = RuleExeUtil.getDeviceList(
							zbSwitch, SystemConstants.InOutLine,
							SystemConstants.PowerTransformer, true, true, true);//查找设备,搜索起始设备为主变开关，目标设备类型为线路，排除设备类型为主变，是否搜索断开路径为是，是否遇母线停止搜索是，是否遇目标设备类型停止搜索是
					if (searchDevs.size() > 0) {//如果查找到的线路不为空
						PowerDevice dev = (PowerDevice) searchDevs.get(0);//获取线路
						tempdev = dev;
						}
				}
			}
			else {//否则围绕操作设备来搜索线路，
				CommonSearch cs = new CommonSearch();
				Map<String, Object> inPara = new HashMap<String, Object>();
				Map<String, Object> outPara = new HashMap<String, Object>();
				inPara.put("oprSrcDevice", curDev);
				inPara.put("tagDevType", SystemConstants.InOutLine); // 目标设备线路
				inPara.put("excDevType", SystemConstants.PowerTransformer);//排除搜索主变，
				cs.execute(inPara, outPara);
				List searchDevs = (ArrayList) outPara.get("linkedDeviceList");
				if (searchDevs.size() > 0) {//如果有线路，则用线路的设备名称来代替线路名称。
					PowerDevice dev = (PowerDevice) searchDevs.get(0);
					tempdev = dev;
				}
			}
		}
		replaceStr = CZPService.getService().getDevName(tempdev);
		replaceStr = replaceStr.replaceAll("\\d*$", "");
		//replaceStr = replaceStr.toLowerCase();//2017-10-19 黄彩凤  鹰潭问题
		if(replaceStr.contains("kv")){
			replaceStr = replaceStr.substring(replaceStr.indexOf("kv")+2, replaceStr.length());
		}
		return replaceStr;
	}
}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPLKGMBMXBH  implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		 if("旁路开关目标母线编号".equals(tempStr)){
			 
			 PowerDevice plkg = null;
			 if(stationDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL))
				 plkg = stationDev;
			 else if(stationDev.getDeviceType().equals(SystemConstants.Switch)) {
				 Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
				 for (Iterator<DispatchTransDevice> iterator = dtds.values().iterator(); iterator.hasNext();) {
					 DispatchTransDevice dtd=(DispatchTransDevice)iterator.next();
					 PowerDevice dev=dtd.getTransDevice();
					 if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)) {
						 plkg = dev;
						 break;
					 }
				 }
			 }
			 if(plkg != null) {
				 List<PowerDevice> motherList = RuleExeUtil.getDeviceList(plkg, SystemConstants.MotherLine, SystemConstants.PowerTransformer, false, true, true);
					for(Iterator it = motherList.iterator();it.hasNext();) {
						PowerDevice ml = (PowerDevice)it.next();
						if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
							it.remove();
					}
					if(motherList.size() == 0) {
						return null;	
					}
					for (int k = 0; k < motherList.size(); k++) {
						PowerDevice dev = motherList.get(k);
						if("".equals(replaceStr))
							replaceStr=CZPService.getService().getDevNum(dev.getPowerDeviceName());
						else
							replaceStr=replaceStr+"、"+CZPService.getService().getDevNum(dev.getPowerDeviceName());
					}
			 }
				
				
				
			}
		return replaceStr;
	}

}

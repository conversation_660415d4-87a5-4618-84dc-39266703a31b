package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrDMSBMCYX implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("倒母运行设备名称".equals(tempStr)){
			
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			DispatchTransDevice dtd=null;
			PowerDevice dev=null;									
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        List<PowerDevice> devList=new ArrayList<PowerDevice>();
	        List searchDevs=null;
			for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
				 dtd=(DispatchTransDevice)iterator.next();
				 dev=dtd.getTransDevice();
				 if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
					   inPara.put("oprSrcDevice", dev);
				       inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.PowerTransformer); //目标设备母线
				       inPara.put("isSearchDirectDevice", false);
				       cs.execute(inPara, outPara);
				       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
					   if(searchDevs.size()==0)
						   continue;
					   List<PowerDevice> swList = RuleExeUtil.getKnifeRelateSwitch(dev);
					   if(swList.size()==0)
						   continue;
					   PowerDevice kg=(PowerDevice)swList.get(0);
					   PowerDevice ml=(PowerDevice)searchDevs.get(0);
					   if(dtd.getBeginstatus().equals("1") &&
							   dtd.getEndstate().equals("0") &&
							   ml.getPowerStationID().equals(stationDev.getPowerStationID()) && 
							   kg.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)&&
							   kg.getDeviceStatus().equals("0")&&
							   !devList.contains(ml))
						   devList.add(ml);
				 }
			}
			if(devList.size() == 0) {
				return replaceStr;
			}
			for (int k = 0; k < devList.size(); k++) {
				dev = devList.get(k);
				if("".equals(replaceStr))
					replaceStr=CZPService.getService().getDevName(dev);
				else
					replaceStr=replaceStr+"、"+CZPService.getService().getDevName(dev);
			}
		}
	
	/*	if(!replaceStr.equals(""))
			replaceStr=replaceStr+"母";*/
		return replaceStr;
	}

}

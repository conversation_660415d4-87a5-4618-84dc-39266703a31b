package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;
/**
 * 回去线路开关编号
 * <AUTHOR>
 *
 */
public class ReplaceStrXLKG implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("线路开关".equals(tempStr))
		{
			PowerDevice xlSwitch=null;
			
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
		    inPara.put("oprSrcDevice", stationDev);
	        inPara.put("tagDevType", SystemConstants.Switch); 
	        cs.execute(inPara, outPara);
	      
	        List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
	        
	        if(tempSwitchs != null){
	        	for (int n = 0; n < tempSwitchs.size(); n++) {
	    			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(n);
	                if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
	                	xlSwitch = tempSwitch;
	                	break;
	                }
	    		}
	        }
	        
    		
        	if(xlSwitch!=null)
        	{
//        		replaceStr=xlSwitch.getPowerDeviceName().replace(curDev.getPowerDeviceName(), "").replace(String.valueOf(curDev.getPowerVoltGrade()), "");
        		replaceStr = CZPService.getService().getDevName(xlSwitch);
        		//replaceStr = CZPService.getService().getDevNum(replaceStr);
//        		replaceStr = replaceStr.replace("开关", "");
//        		replaceStr = replaceStr.substring(replaceStr.indexOf("线")+1);
        	}
		}
		
		return replaceStr;
	}

}

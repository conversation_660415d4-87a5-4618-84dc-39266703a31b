package czprule.wordcard.replaceclass.impl;

import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrCHZ implements TempStringReplace{

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if(tempStr.equals("重合闸")){
			List<Map> sby=DBManager.queryForList("select t.protectname,  t.protectstatus from "+CBSystemConstants.opcardUser+"t_a_protectequip t,"+CBSystemConstants.opcardUser+"T_A_PROTECTINFO s where equipid='"+curDev.getPowerDeviceID()+"' and s.PROTECTTYPENAME='重合闸' and t.protecttypeid=s.protecttypeid");
			for (Map  map : sby) {
				String protectname=StringUtils.ObjToString(map.get("protectname"));
				String protectstatus=StringUtils.ObjToString(map.get("protectstatus"));
				replaceStr+=(protectname+"重合闸;");
			}
		}
		if(replaceStr.indexOf(";")>0){
			replaceStr=replaceStr.substring(0,replaceStr.length()-1);
		}
		if(replaceStr.equals("")){
			return null;
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXTXLMC implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("系统线路名称".equals(tempStr)){
			List<String> list = CBSystemConstants.getEquiplinemap().get(curDev.getPowerDeviceID());
			if(list != null && list.size() > 0) {
				String lineID = list.get(0);
				PowerDevice line = CBSystemConstants.getPowerLine(lineID);
				replaceStr = CZPService.getService().getDevName(line);
			}
			else
				replaceStr = CZPService.getService().getDevName(curDev);
		}
		return replaceStr;
	}
}

package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrFDDYCDZ implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("分段开关电源侧刀闸".equals(tempStr)){
			PowerDevice pd = null;
			if(curDev.getDeviceType().equals(SystemConstants.MotherLine)) {
				List<PowerDevice>  devList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch ,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
						"", false, true, true, true);
				pd = devList.get(0);
			}
			else if(curDev.getDeviceType().equals(SystemConstants.Switch)) {
				pd = stationDev;
			}
			List<PowerDevice>  fhcdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.Switch, false, true, true);
			if(fhcdzList.size() == 2 && fhcdzList.get(0).getPowerDeviceName().equals(fhcdzList.get(1).getPowerDeviceName())) {
				for(PowerDevice kf : fhcdzList) {
					List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(kf, SystemConstants.SwitchSeparate);
					if(kfList.size() > 0) {
						fhcdzList.remove(1);
						fhcdzList.add(kfList.get(0));
						break;
					}
				}
			}
			RuleExeUtil.swapDeviceList(fhcdzList);
			PowerDevice pd2 = fhcdzList.get(1);
			replaceStr = CZPService.getService().getDevName(pd2)==null?"":CZPService.getService().getDevName(pd2);
		}
		return replaceStr;
	}

}

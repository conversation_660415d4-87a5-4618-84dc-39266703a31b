package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrXC implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("小车".equals(tempStr)){
			List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			if(kfList.size() >= 1) {
				for(PowerDevice kf : kfList) {
					if(!kf.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}
					if(!RuleExeUtil.isDeviceChanged(kf))
						continue;
					replaceStr = StringUtils.getSwitchCode(kf.getPowerDeviceName())+"小车";
					break;
					
				}
			}
			if(replaceStr.equals(""))
				return null;
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDXLKGBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("接带线路开关编号".equals(tempStr)){
			if(CBSystemConstants.LineTransform.size() == 0) {
				return null;
			}
			for (int n = 0; n < CBSystemConstants.LineTransform.size(); n++) {
				PowerDevice xlSwitch=null;
				CommonSearch cs=new CommonSearch();
		        Map<String,Object> inPara = new HashMap<String,Object>();
		        Map<String,Object> outPara = new HashMap<String,Object>();
			    inPara.put("oprSrcDevice", CBSystemConstants.LineTransform.get(n));
		        inPara.put("tagDevType", SystemConstants.Switch); 
		        cs.execute(inPara, outPara);
		      
		        List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
		        if(tempSwitchs.size() > 0) {
		        	xlSwitch = (PowerDevice)tempSwitchs.get(0);
		        	replaceStr=replaceStr+StringUtils.getSwitchCode(xlSwitch.getPowerDeviceName())+",";
		        }
			}
			if(replaceStr.endsWith(","))
				replaceStr = replaceStr.substring(0, replaceStr.length()-1);
	    }
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.List;

/**
 * Gny
 * 判断是否监控展示
 */
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooDMJX implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		boolean ret = false;
		if (tempStr.equals("单母接线")) {
			if(stationDev.getDeviceType().equals(SystemConstants.InOutLine)){
				PowerDevice equip = RuleExeUtil.getDeviceSwitch(stationDev);
				if(equip!=null&&equip.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
					return true;
				}
			}else if(stationDev.getDeviceRunModel().equals(CBSystemConstants.RunModelOneMotherLine)){
				return true;
			}
		}
		return ret;
	}
		
}

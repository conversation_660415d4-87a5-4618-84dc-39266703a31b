package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDBKGMC implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("接地变开关名称".equals(tempStr)){
			List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer+","+SystemConstants.Switch, true, true, true);
			if(mlList.size() > 0) {
				List<PowerDevice> swList = RuleExeUtil.getDeviceList(mlList.get(0), SystemConstants.Switch, SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchJDB, "", false, true, true, true);
				if(swList.size() > 0) {
					replaceStr = CZPService.getService().getDevName(swList.get(0));
				}
			}
		}
		return replaceStr;
	}

}

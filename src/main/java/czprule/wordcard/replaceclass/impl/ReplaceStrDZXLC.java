package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.WordCardBuild;

public class ReplaceStrDZXLC implements TempStringReplace {

	private int countKnife = 0;

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("刀闸线路侧".equals(tempStr)) {
			replaceStr = WordCardBuild.getSideName(curDev,
					SystemConstants.InOutLine, desc, countKnife);

			replaceStr += "线路侧";
			if (replaceStr == null || replaceStr.equals("")) {
				return null;
			}
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrSBMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		
		if("设备名称".equals(tempStr)){
			PowerDevice tempdev = curDev;
			if (curDev.getDeviceType().equals(SystemConstants.InOutLine)) {// 如果操作设备设备类型是线路
				tempdev = stationDev;
			} 
			replaceStr = CZPService.getService().getDevName(tempdev);
//			if(curDev.getDeviceType().equals(SystemConstants.InOutLine)){
//				replaceStr = replaceStr.replaceAll("\\d*$", "");
//				if(!replaceStr.contains("线"))
//					replaceStr+="线";
//			}
			//replaceStr = curDev.getPowerDeviceName();
		}
		return replaceStr;
	}

}

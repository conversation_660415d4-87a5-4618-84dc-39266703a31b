package czprule.wordcard.replaceclass.impl;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.securitycheck.view.CheckWord;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrXLKGBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("线路开关编号".equals(tempStr)){
			PowerDevice sw = RuleUtil.getlineSwitchOrKnife(stationDev);
			if(sw!=null){
			   replaceStr=CheckWord.getEquipNum(sw.getPowerDeviceName());
			}
			
		}
		return replaceStr;
	}

}

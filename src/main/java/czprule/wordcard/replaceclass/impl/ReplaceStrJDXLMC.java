package czprule.wordcard.replaceclass.impl;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrJDXLMC implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("接带线路名称".equals(tempStr)){
			if(CBSystemConstants.LineTransform.size() == 0) {
				return null;
			}
			for (int n = 0; n < CBSystemConstants.LineTransform.size(); n++) {
				replaceStr=replaceStr+CBSystemConstants.LineTransform.get(n).getPowerDeviceName()+",";
			}
			if(replaceStr.endsWith(","))
				replaceStr = replaceStr.substring(0, replaceStr.length()-1);
	    }
		return replaceStr;
	}

}

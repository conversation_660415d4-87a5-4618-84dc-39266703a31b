package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrQTZB implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("其他主变".equals(tempStr)){
			List<PowerDevice>  zbList = RuleExeUtil.getDeviceList(stationDev, SystemConstants.PowerTransformer, null, true, false, true);
			for(int i=0;i<zbList.size();i++){
				if(zbList.get(i).getPowerDeviceName().contains("站用")||zbList.get(i).getPowerDeviceName().contains("所内")
						||zbList.get(i).getPowerDeviceName().contains("所用")||zbList.get(i).getPowerDeviceName().contains("接地")
						||RuleExeUtil.isDeviceInDtd(zbList.get(i))){
					zbList.remove(i);
					i--;
				}
			}
			RuleExeUtil.swapDeviceList(zbList);
			replaceStr = CZPService.getService().getDevName(zbList);
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBDYCMX implements TempStringReplace {


	@Override
 //@ SBGLMX 设备关联母线 中间有开关    由开关类型 找对应母线
	
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = null;
		List<PowerDevice> lowSideMLList = new ArrayList<PowerDevice>();
		// TODO Auto-generated method stub
		if("主变低压侧母线".equals(tempStr)){
			 if(curDev.getDeviceType().equals(SystemConstants.PowerTransformer)){
			List<PowerDevice> templist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, null, null, null, false, true, true, true);
		 
			
			 for (PowerDevice device : templist) {
				 if(RuleExeUtil.isSwitchLowVolSide(device)) {
					 List<PowerDevice> mlList = RuleExeUtil.getDeviceList(device,SystemConstants.MotherLine , SystemConstants.PowerTransformer, true, true, true);
					 for (PowerDevice ml : mlList) {
						 if(!lowSideMLList.contains(ml))
							 lowSideMLList.add(ml);
					 }
					
				 }
			 }
			 }
			 if(RuleExeUtil.isSwitchMiddleVolSide(curDev)){
				 List<PowerDevice> mlList = RuleExeUtil.getDeviceList(curDev,SystemConstants.MotherLine , null, false, true, true);
				 for (PowerDevice ml : mlList) {
					 if(!lowSideMLList.contains(ml))
						 lowSideMLList.add(ml);
				 }
			 }
//			
//			if(templist== null||templist.equals("")) return null;
//
//		  List<PowerDevice> templist2 , templist3 = new ArrayList<PowerDevice>() ;
//		  List<PowerDevice> minvoltlist =new ArrayList<PowerDevice>() ;
//		  List<PowerDevice> maxvoltlist = new ArrayList<PowerDevice>() ; 
//		  List<PowerDevice> midvoltlist = new ArrayList<PowerDevice>() ;
//         for (PowerDevice device : templist) {
//			if( (templist2 =RuleExeUtil.getDeviceList(device,SystemConstants.MotherLine , null, false, true, true))!=null)
//				templist3.addAll(templist2)// bug
//				;
//			  
//		  }
//         //判别 高各端母线
//          if(templist3!=null){
//        	  RuleExeUtil.swapDeviceList(templist3);
//        	  
//        	  PowerDevice maxvoltdev =templist3.get(templist.size()-1);
//    		  PowerDevice minvoltdev =templist3.get(0);
//    		   double maxvolt =maxvoltdev.getPowerVoltGrade();
//    		   double minvolt =minvoltdev.getPowerVoltGrade();
//    		   
//    		   for (PowerDevice device : templist3){
//    			   if (device.getPowerVoltGrade()==minvolt){
//    				   minvoltlist.add(device);
//    			   }
//    			   else if(device.getPowerVoltGrade()==maxvolt){
//    				   maxvoltlist.add(device);
//    			   }
//    			   else midvoltlist.add(device);
//    		   }
//    		   
//          }
			 if(lowSideMLList.size() > 0)
				 replaceStr = CZPService.getService().getDevName(lowSideMLList);
			
		}
		return 	replaceStr;
	}

}

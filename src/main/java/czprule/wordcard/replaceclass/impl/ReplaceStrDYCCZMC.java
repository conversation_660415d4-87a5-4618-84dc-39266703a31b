package czprule.wordcard.replaceclass.impl;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrDYCCZMC implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("电源侧厂站名称".equals(tempStr)) {
			if(CBSystemConstants.LineSource.get(
					curDev.getPowerDeviceID())!= null)
				replaceStr = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(CBSystemConstants.LineSource.get(
						curDev.getPowerDeviceID()).getPowerStationID()));
		}
		return replaceStr;
	}

}

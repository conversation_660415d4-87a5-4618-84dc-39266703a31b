package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;
import czprule.wordcard.replaceclass.WordCardBuild;

public class ReplaceStrDZMXC implements TempStringReplace {

	private int countKnife;

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("刀闸母线侧".equals(tempStr)) {
			replaceStr = WordCardBuild.getSideName(curDev,
					SystemConstants.MotherLine, desc, countKnife);
			if (replaceStr == null || replaceStr.equals("")) {
				return null;
			}
			replaceStr += "母线侧";
		}
		return replaceStr;
	}

}

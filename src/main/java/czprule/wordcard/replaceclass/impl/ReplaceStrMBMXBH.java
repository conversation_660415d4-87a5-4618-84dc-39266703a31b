package czprule.wordcard.replaceclass.impl;

import java.text.Collator;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Locale;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrMBMXBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		 if("目标母线编号".equals(tempStr)){
				
				Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
				DispatchTransDevice dtd=null;
				PowerDevice dev=null;									
				CommonSearch cs=new CommonSearch();
		        Map<String,Object> inPara = new HashMap<String,Object>();
		        Map<String,Object> outPara = new HashMap<String,Object>();
		        List<PowerDevice> motherList=new ArrayList<PowerDevice>();
		        List<?> searchDevs=null;
				for (Iterator<DispatchTransDevice> iterator = dtds.values().iterator(); iterator.hasNext();) {
					 dtd=(DispatchTransDevice)iterator.next();
					 dev=dtd.getTransDevice();
					 if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
						   inPara.put("oprSrcDevice", dev);
					       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
					       inPara.put("isSearchDirectDevice", "true");
					       cs.execute(inPara, outPara);
					       searchDevs = (ArrayList<?>) outPara.get("linkedDeviceList");
						   if(searchDevs.size()==0)
							   continue;
						   List<PowerDevice> swList=RuleExeUtil.getKnifeRelateSwitch(dev);
						   if(swList.size() > 0 && swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
							   continue;
						   dev=(PowerDevice)searchDevs.get(0);
						   if(dtd.getEndstate().equals("0") && 
								   !dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother) &&
								   dev.getPowerStationID().equals(stationDev.getPowerStationID()) && 
								   !motherList.contains(dev) &&
								   dev.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine))
							   motherList.add(dev);
					 }
				}
				if(motherList.size() == 0) {
					return null;	
				}
				//按设备名称有小到大排序：5M，1M变成1M，5M
				motherList = sortByName(motherList);
				for (int k = 0; k < motherList.size(); k++) {
					dev = motherList.get(k);
					if("".equals(replaceStr))
						replaceStr=CZPService.getService().getDevNum(dev);
					else
						replaceStr=replaceStr+"、"+CZPService.getService().getDevNum(dev);
				}
			}
		return replaceStr;
	}
	
	/**
	 * 对设备集合按照名称大小排序
	 * @param arr
	 * @return
	 */
	public List<PowerDevice> sortByName(List<PowerDevice> arr){
		PowerDevice deviceTemp = null;
		boolean status = false;
		Collator collator = Collator.getInstance(Locale.CHINA);
		for(int i = 0; i < arr.size(); i++){
			status = false;
			for(int j = arr.size()-2; j>=i; j--){
				 int result = collator.compare(CZPService.getService().getDevNum(arr.get(j).getPowerDeviceName()), CZPService.getService().getDevNum(arr.get(j+1).getPowerDeviceName()));
	    		 if(result>0){
	    				deviceTemp = arr.get(j+1);
						arr.set(j+1, arr.get(j));
						arr.set(j, deviceTemp);
						status = true;
	    		 }
			}
			if(!status)
				break;
		}
		return arr;
	}

}

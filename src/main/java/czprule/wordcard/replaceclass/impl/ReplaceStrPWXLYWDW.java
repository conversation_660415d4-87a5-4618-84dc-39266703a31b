package czprule.wordcard.replaceclass.impl;


import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.svg.document.SVGDocumentResolver;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 */
public class ReplaceStrPWXLYWDW implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("配网线路运维单位".equals(tempStr)){
			String lineID = "";
			PowerDevice dev = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			if(dev.getDeviceType().equals(SystemConstants.InOutLine))
				lineID=dev.getPowerDeviceID();
			if(lineID.equals("")) {
				if(SystemConstants.getGuiBuilder()!=null) {
					SVGDocumentResolver resolver = SVGDocumentResolver.getResolver();
					lineID = resolver.getLineID(SystemConstants.getGuiBuilder().getActivateSVGPanel().getSvgDocument());
					if(!lineID.equals(""))
						lineID = CBSystemConstants.getMapPowerFeeder().get(lineID).getPowerDeviceID();
					else {
						List<Map> list = DBManager.queryForList("select distinct b.line_id from "+CBSystemConstants.equipUser+"T_C_TERMINAL a,"+CBSystemConstants.equipUser+"T_PD_EQUIPINFO b where CONNECTIVITYNODE_ID in(select CONNECTIVITYNODE_ID from "+CBSystemConstants.equipUser+"T_C_TERMINAL where equip_id='"+stationDev.getPowerDeviceID()+"') and a.equip_id= b.equip_id and line_id is not null");
			            if(list.size() > 0 && list.get(0).get("line_id")!= null && !list.get(0).get("line_id").equals("")) {
			            	lineID = list.get(0).get("line_id").toString();
			       
			            }
					}
				}
				else if(CBSystemConstants.getMapPowerFeeder().containsKey(stationDev.getPowerStationID())) {
					lineID = CBSystemConstants.getMapPowerFeeder().get(stationDev.getPowerStationID()).getPowerDeviceID();
				}
				else if(stationDev.getDevice()!=null){
					if(!stationDev.getDevice().equals("")) {
						
						 replaceStr = CBSystemConstants.getMapPowerFeeder().get(stationDev.getDevice()).getPowerDeviceID();
					}
				}
			}
			String sql = "SELECT LINE_ORGANNAME DWMC FROM "+CBSystemConstants.opcardUser+"TH_DC_DCMS_ORGANLINEREL WHERE LINE_ID = '"+lineID+"'";
			List<Map<String,Object>> list=DBManager.queryForList(sql);
			if(list.size()>0){
				replaceStr = StringUtils.ObjToString(list.get(0).get("DWMC"));
			}
		}
		if(replaceStr.equals(""))
			replaceStr = CZPService.getService().getDevName(CBSystemConstants.getPowerStation(stationDev.getPowerStationID()));
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrYMXBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("源母线编号".equals(tempStr)){
			
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			DispatchTransDevice dtd=null;
			PowerDevice dev=null;									
			CommonSearch cs=new CommonSearch();
	        Map<String,Object> inPara = new HashMap<String,Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        List<PowerDevice> motherList=new ArrayList<PowerDevice>();
	        List searchDevs=null;
			for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
				 dtd=(DispatchTransDevice)iterator.next();
				 dev=dtd.getTransDevice();
//				 if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
//					 List<PowerDevice> kgDevices = RuleExeUtil.getDeviceList(dev, SystemConstants.Switch, SystemConstants.PowerTransformer,
//							 true, true, true);
//					 for(PowerDevice kg:kgDevices){
//						 System.out.println("厂站名称："+kg.getPowerStationName()+" 设备ID："+kg.getPowerDeviceID()+" 设备名称："+kg.getPowerDeviceName()+ "设备状态："
//								 +kg.getDeviceStatus()+" 设备类型："+kg.getDeviceRunType()+" 设备接线方式："+kg.getDeviceRunModel()+"---------------------------");
//					 }
//					 List<PowerDevice> dzDevices = RuleExeUtil.getDeviceList(dev, SystemConstants.SwitchSeparate, SystemConstants.PowerTransformer,
//							 true, true, true);
//					 for(PowerDevice dz:dzDevices){
//						 System.out.println("厂站名称："+dz.getPowerStationName()+" 设备ID："+dz.getPowerDeviceID()+" 设备名称："+dz.getPowerDeviceName()+ " 设备状态："
//								 +dz.getDeviceStatus()+"---------------------------");
//					 }
//				 }
//				 CZPService.getService().getDevNum(RuleExeUtil.getDeviceList(dev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
//						 true, true, true).get(0));
				 if(dev.getDeviceType().equals(SystemConstants.SwitchSeparate)&&dev.getPowerStationID().equals(stationDev.getPowerStationID())){
					
					   inPara.put("oprSrcDevice", dev);
				       inPara.put("tagDevType", SystemConstants.MotherLine); //目标设备母线
				       inPara.put("isSearchDirectDevice", "true");
				       cs.execute(inPara, outPara);
				       searchDevs = (ArrayList) outPara.get("linkedDeviceList");
					   if(searchDevs.size()==0)
						   continue;
					   List<PowerDevice> swList=RuleExeUtil.getKnifeRelateSwitch(dev);
					   if(swList.size() > 0 && swList.get(0).getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML))
						   continue;
					   PowerDevice ml=(PowerDevice)searchDevs.get(0);
					   if(dtd.getBeginstatus().equals("0") &&
							   ml.getPowerStationID().equals(stationDev.getPowerStationID()) && 
							   ml.getDeviceRunModel().equals(CBSystemConstants.RunModelDoubleMotherLine)&&
							   !motherList.contains(ml))
						   motherList.add(ml);
				 }
			}
			if(motherList.size() == 0) {
				List<PowerDevice> mx_List = RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,true,true,true);

				for(int k = 0; k < mx_List.size(); k++){
					PowerDevice pd = mx_List.get(k);
					if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
						mx_List.remove(k);
						k--;
					}
				}
				if(mx_List.size()>0){
					replaceStr=CZPService.getService().getDevNum(mx_List.get(0).getPowerDeviceName());
				}
				return replaceStr;
			}
			for (int k = 0; k < motherList.size(); k++) {
				dev = motherList.get(k);
				if("".equals(replaceStr))
					replaceStr=CZPService.getService().getDevNum(dev);
				else
					replaceStr=replaceStr+"、"+CZPService.getService().getDevNum(dev);
			}
		}
	
	/*	if(!replaceStr.equals(""))
			replaceStr=replaceStr+"母";*/
		return replaceStr;
	}

}

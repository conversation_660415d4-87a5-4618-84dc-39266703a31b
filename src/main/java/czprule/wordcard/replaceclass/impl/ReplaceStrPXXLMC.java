package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2021年1月26日 上午9:05:56
 */
public class ReplaceStrPXXLMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		PowerDevice tempdev = new PowerDevice();
		if ("平行线路名称".equals(tempStr)) {
			List<PowerDevice> searchDevs = RuleExeUtil.getDeviceList(curDev, SystemConstants.InOutLine, "", true, false, true);
			if (searchDevs.size() > 0) {//如果有线路，则用线路的设备名称来代替线路名称。
				for(PowerDevice dev :searchDevs){
					if(!CZPService.getService().getDevName(dev).equals(getXLMC(curDev, stationDev, desc))
							&& dev.getPowerVoltGrade()==curDev.getPowerVoltGrade()){
						tempdev = dev;
					}
				}
			}
		}
		replaceStr = CZPService.getService().getDevName(tempdev);
		return replaceStr;
	}
	
	//获取当前线路名称
	private String getXLMC(PowerDevice curDev,
			PowerDevice stationDev,String desc){
		ReplaceStrXLMC replaceStrXLMC=new ReplaceStrXLMC();
		return replaceStrXLMC.strReplace("线路名称", curDev, stationDev, desc);
	}
}

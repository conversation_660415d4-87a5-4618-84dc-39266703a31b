package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrFDFHCDZ implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("分段开关负荷侧刀闸".equals(tempStr)){
			PowerDevice pd = null;
			if(curDev.getDeviceType().equals(SystemConstants.MotherLine)) {
				List<PowerDevice>  devList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch ,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchML,
						"", false, true, true, true);
				pd = devList.get(0);
			}
			else if(curDev.getDeviceType().equals(SystemConstants.Switch)) {
				pd = stationDev;
			}
			List<PowerDevice>  fhcdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.Switch, false, true, true);
			if(fhcdzList.size() == 2 && fhcdzList.get(0).getPowerDeviceName().equals(fhcdzList.get(1).getPowerDeviceName())) {
				for(PowerDevice kf : fhcdzList) {
					List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(kf, SystemConstants.SwitchSeparate);
					if(kfList.size() > 0) {
						fhcdzList.remove(1);
						fhcdzList.add(kfList.get(0));
						break;
					}
				}
			}
			RuleExeUtil.swapDeviceList(fhcdzList);
			PowerDevice pd2 = fhcdzList.get(0);
			replaceStr = CZPService.getService().getDevName(pd2)==null?"":CZPService.getService().getDevName(pd2);
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBKGFHCDZ implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("主变开关负荷侧刀闸".equals(tempStr)){
			List<PowerDevice>  devList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch ,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC,
					"", false, true, true, true);
			PowerDevice pd = devList.get(0);
			List<PowerDevice>  fhcdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.Switch, false, true, true);
			RuleExeUtil.swapDeviceList(fhcdzList);
			PowerDevice pd2 = fhcdzList.get(0);
			replaceStr = CZPService.getService().getDevName(pd2)==null?"":CZPService.getService().getDevName(pd2);
		}
		return replaceStr;
	}

}

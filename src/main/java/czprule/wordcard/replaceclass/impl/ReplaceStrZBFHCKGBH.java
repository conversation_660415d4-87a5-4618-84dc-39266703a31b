package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBFHCKGBH implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("主变负荷侧开关编号".equals(tempStr)){
			PowerDevice pd=RuleExeUtil.getTransformSwitch(curDev);
			if(pd!=null){
				replaceStr=StringUtils.getSwitchCode(pd.getPowerDeviceName());
			}else{
				replaceStr=null;
			}
		}
		return replaceStr;
	}

}

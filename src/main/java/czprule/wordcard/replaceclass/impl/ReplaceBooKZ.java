package czprule.wordcard.replaceclass.impl;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooKZ implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		if (tempStr.equals("是空载")) {
			if (CBSystemConstants.sdkz == 0) {
				return false;
			} else {
				return true;
			}
		} else if (tempStr.equals("否空载")) {
			if (CBSystemConstants.sdkz == 0) {
				return true;
			} else {
				return false;
			}
		}
		return false;
	}

}

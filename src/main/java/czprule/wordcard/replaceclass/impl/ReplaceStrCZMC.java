package czprule.wordcard.replaceclass.impl;


import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrCZMC implements TempStringReplace {
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("厂站名称".equals(tempStr)){
			if(stationDev.getDeviceType().equals(SystemConstants.PowerStation) || stationDev.getDeviceType().equals(SystemConstants.PowerFactory))
				replaceStr=CZPService.getService().getDevName(stationDev);
			else {
				if(stationDev.isPW()) {
					PowerDevice line=CBSystemConstants.getMapPowerFeeder().get(stationDev.getPowerStationID());
					replaceStr=CZPService.getService().getDevName(CBSystemConstants.getPowerStation(line.getPowerStationID()));
				}
				else {
					PowerDevice dev = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
					replaceStr=CZPService.getService().getDevName(dev);
				}
			}
	    }
		return replaceStr;
	}

}

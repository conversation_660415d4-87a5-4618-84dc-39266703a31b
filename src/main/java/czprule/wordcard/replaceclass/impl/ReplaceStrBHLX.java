package czprule.wordcard.replaceclass.impl;

import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrBHLX implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		List list=DBManager.queryForList("select t.PROTECTTYPENAME from "+CBSystemConstants.opcardUser+"T_A_PROTECTINFO t where t.DEVICETYPEID='"+curDev.getDeviceType()+"'");
		Map map=(Map) list.get(0);
		String replaceStr=null;
		replaceStr=StringUtils.ObjToString(map.get("PROTECTTYPENAME"));
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.impl;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrDYDJ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		if ("电压等级".equals(tempStr)) {
			String volt = String.valueOf(curDev.getPowerVoltGrade());			
			replaceStr = volt.substring(0, volt.indexOf(".")) + "kV";
//			if(desc.indexOf("名称")>0&&(StringUtils.contains(curDev.getPowerDeviceName(),"kV")||StringUtils.contains(curDev.getPowerDeviceName(),"kV"))){
//				replaceStr = "";	
//			}
		}
		return replaceStr;
	}

}

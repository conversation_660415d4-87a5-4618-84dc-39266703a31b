package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrDYCDZBH implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("电源侧刀闸编号".equals(tempStr)){
			List<PowerDevice> kfList = RuleExeUtil.getDeviceDirectList(curDev, SystemConstants.SwitchSeparate);
			if(kfList.size() >= 2) {
				for(PowerDevice kf : kfList) {
					if(kf.getDeviceKind().equals(CBSystemConstants.KindKnifeXC)){
						continue;
					}
					if(!RuleExeUtil.isDeviceChanged(kf))
						continue;
					if(RuleExeUtil.JudgeknifeIsPowerSide(kf).equals("1")) {
						replaceStr = StringUtils.getSwitchCode(kf.getPowerDeviceName());
						break;
					}
				}
					//replaceStr = StringUtils.getSwitchCode(kfList.get(kfList.size()-1).getPowerDeviceName());
			}
			if (replaceStr.equals(""))
				replaceStr = null;
		}
		return replaceStr;
	}

}

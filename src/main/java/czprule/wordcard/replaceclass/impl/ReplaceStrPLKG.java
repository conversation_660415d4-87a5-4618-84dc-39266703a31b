package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPLKG implements TempStringReplace{

	

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("旁路开关".equals(tempStr)){
			
			PowerDevice sideSwitch = getSideSwitch(curDev);
			if(sideSwitch != null)
				replaceStr= CZPService.getService().getDevName(sideSwitch);
		}
		return replaceStr;
	}
	
	public static PowerDevice getSideSwitch(PowerDevice curDev){
		PowerDevice sideSwitch=null;
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		if(curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)) {
        	inPara.put("oprSrcDevice", curDev);
            inPara.put("tagDevType", SystemConstants.Switch);
            cs.execute(inPara, outPara);
    		inPara.clear();
    		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
    		for (int n = 0; n < tempSwitchs.size(); n++) {
    			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(n);
                if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
                		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
                	sideSwitch = tempSwitch;
                	break;
                }
    		}
		}
		else {
			
			inPara.put("oprSrcDevice", curDev);
	        inPara.put("tagDevType", SystemConstants.MotherLine);
	        cs.execute(inPara, outPara);
			inPara.clear();
			PowerDevice sideML=null;
			
			List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
			for (int m = 0; m < tempMLs.size(); m++) {
				PowerDevice tempML = (PowerDevice) tempMLs.get(m);
	            if(tempML.getPowerVoltGrade()==curDev.getPowerVoltGrade() && tempML.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
	            	sideML = tempML;
	            	inPara.put("oprSrcDevice", sideML);
	                inPara.put("tagDevType", SystemConstants.Switch);
	                cs.execute(inPara, outPara);
	        		inPara.clear();
	        		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
	        		for (int n = 0; n < tempSwitchs.size(); n++) {
	        			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(n);
	                    if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
	                    		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
	                    	sideSwitch = tempSwitch;
	                    	break;
	                    }
	        		}
	            	break;
	            }
			}
		}
		return sideSwitch;
	}

}

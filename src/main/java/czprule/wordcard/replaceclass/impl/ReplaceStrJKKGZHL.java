package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;
/**
 * 回去线路开关编号
 * <AUTHOR>
 *
 */
public class ReplaceStrJKKGZHL implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("监控开关综合令".equals(tempStr))
		{
//			String caozou = "";
			String check = "";
			if(desc.contains("拉开")){
//				caozou = "拉开";
				check="断开";
			}else if(desc.contains("断开")){
//				caozou = "断开";
				check="断开";
			}else if(desc.contains("合上")){
//				caozou = "合上";
				check = "合上";
			}
			PowerDevice devST = CBSystemConstants.getPowerStation(stationDev.getPowerStationID());
			String stationName = CZPService.getService().getDevName(devST);
			Map<Integer, DispatchTransDevice> dtds=CBSystemConstants.getDtdMap();
			for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
				DispatchTransDevice dtd=(DispatchTransDevice)iterator.next();
				PowerDevice dev=dtd.getTransDevice();
				if(dev.getDeviceType().equals(SystemConstants.Switch)){
					if(!replaceStr.equals("")){
						if(check.equals("断开")){
							replaceStr+="拉开";
						}else{
							replaceStr+="合上";
						}
					}
					replaceStr += stationName+CZPService.getService().getDevName(dev)+"\r\n";
					replaceStr +="检查"+stationName+CZPService.getService().getDevName(dev)+"确在"+check+"位置"+"\r\n";
				}
			}
			
		}

		return replaceStr;
	}

}

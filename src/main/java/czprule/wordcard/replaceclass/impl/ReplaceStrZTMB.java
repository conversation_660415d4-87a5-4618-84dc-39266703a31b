package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.wordcard.replaceclass.TempStringReplace;
/**
 * 回去线路开关编号
 * <AUTHOR>
 *
 */
public class ReplaceStrZTMB implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr="";
		if("目标状态".equals(tempStr))
		{
//			replaceStr = CBSystemConstants.getDeviceStatusName(stationDev.getDeviceType(), stationDev.getDeviceStatus());
			//判断是否充电运行
			boolean isCD = false;
			if(CBSystemConstants.roleCode.equals("0") && stationDev.getDeviceType().equals(SystemConstants.InOutLine) && stationDev.getDeviceStatus().equals("0")) {
				List<PowerDevice> lnList = RuleExeUtil.getLineAllSideList(stationDev);
				if(lnList.size() == 2) {
					boolean isOn1 = false;
					boolean isOn2 = false;
					List<PowerDevice> swList1 = RuleExeUtil.getLinkedSwitch(lnList.get(0));
					for(PowerDevice sw : swList1) {
						String swsta = sw.getDeviceStatus();
						if(swsta.equals("0")) {
							isOn1 = true;
						}
					}
					List<PowerDevice> swList2 = RuleExeUtil.getLinkedSwitch(lnList.get(1));
					for(PowerDevice sw : swList2) {
						String swsta = sw.getDeviceStatus();
						if(swsta.equals("0")) {
							isOn2 = true;
						}
					}
					if((isOn1&&!isOn2) || (!isOn1&&isOn2))
						isCD = true;
				}
			}
			if(isCD)
				replaceStr = "充电运行";
			else
				replaceStr = CBSystemConstants.getDeviceStatusName(stationDev.getDeviceType(), stationDev.getDeviceStatus());		
			
		
		}
		
		return replaceStr;
	}

}

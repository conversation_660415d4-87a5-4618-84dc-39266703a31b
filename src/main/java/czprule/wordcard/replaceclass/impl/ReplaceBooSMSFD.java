package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempBooleanReplace;

public class ReplaceBooSMSFD implements TempBooleanReplace {

	@Override
	public boolean strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		if (tempStr.equals("双母双分段")) {
			if(curDev.getDeviceType().equals(SystemConstants.MotherLine)){
				List<PowerDevice> mxList =RuleExeUtil.getDeviceList(curDev, SystemConstants.MotherLine, SystemConstants.PowerTransformer,
						"", CBSystemConstants.RunTypeSideMother, false, true, false, false);
				if(mxList.size()>2){
					return true;
				}	
			}
			
		} 
		return false;
	}

}

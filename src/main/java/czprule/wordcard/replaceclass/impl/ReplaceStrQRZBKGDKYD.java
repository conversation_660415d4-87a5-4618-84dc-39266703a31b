package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * 县调35kV厂站（一侧是开关一侧是刀闸的情况）
 * 断开开关前，确认35kV军横线已转冷备用
 * @since 2014-12-17 14:03
 * <AUTHOR>
 *
 */
public class ReplaceStrQRZBKGDKYD implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		PowerDevice sourceLineTrans = null;
		List<PowerDevice> loadLineTrans = null ;
		PowerDevice lineDev = curDev;
		List<PowerDevice> ms = RuleExeUtil.getDeviceList(lineDev, SystemConstants.MotherLine, "", true, false, false);
		if(ms.size()>0){//存在母线
			for(int j=0;j<ms.size();j++){
				PowerDevice mlDev = ms.get(j);
				if(mlDev.getPowerStationID().equals(lineDev.getPowerStationID()) 
						&& mlDev.getPowerVoltGrade()==lineDev.getPowerVoltGrade()){
					List<PowerDevice> line = RuleExeUtil.getDeviceList(mlDev, SystemConstants.InOutLine, "", true, true, true);
				     if(line.size()==1){
						 List<PowerDevice> mbList = RuleExeUtil.getDeviceList(mlDev, SystemConstants.PowerTransformer, "", true, true, true);
						 for(int i = 0;i<mbList.size();i++){
							 replaceStr=replaceStr+","+CZPService.getService().getDevNum(mbList.get(i).getPowerDeviceName());

						 }
				     }
				}
			}

		}
		if(replaceStr !=null && !"".equals(replaceStr)){
			replaceStr = replaceStr.substring(1,replaceStr.length());

		}
		return replaceStr;
	}
}

package czprule.wordcard.replaceclass.impl;

import java.util.Iterator;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrPLGLXLDZ implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		// TODO Auto-generated method stub
		String replaceStr = "";
		if ("旁路关联线路刀闸".equals(tempStr)) {
			
			//查找旁路刀闸
			Map<Integer, DispatchTransDevice> dtds = CBSystemConstants
			.getDtdMap();
	DispatchTransDevice dtd = null;
	PowerDevice dev = null;
	for (Iterator iterator = dtds.values().iterator(); iterator.hasNext();) {
		
      dtd = (DispatchTransDevice) iterator.next();
     dev = dtd.getTransDevice();
     //旁路刀闸关联开关为线路开关，则返回值；
     if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL )||dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
    	 PowerDevice lineswitch = RuleExeUtil. getDeviceSwitch(dev);
    	 if(lineswitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)){
    	 replaceStr =CZPService.getService().getDevName(dev);
    	 return replaceStr;
    	 }
     }
	
	}
		}
		return null;
	}

}

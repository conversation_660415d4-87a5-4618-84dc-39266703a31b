package czprule.wordcard.replaceclass.impl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

/**
 * <AUTHOR> 创建时间:2014年2月12日 上午9:05:56
 */
public class ReplaceStrZBKGDYCDZ implements TempStringReplace {


	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String replaceStr = "";
		if("主变开关电源侧刀闸".equals(tempStr)){
			List<PowerDevice>  devList = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch ,SystemConstants.PowerTransformer, CBSystemConstants.RunTypeSwitchDYC+","+CBSystemConstants.RunTypeSwitchFHC,
					"", false, true, true, true);
			PowerDevice pd = devList.get(0);
			List<PowerDevice>  fhcdzList = RuleExeUtil.getDeviceList(pd, SystemConstants.SwitchSeparate, SystemConstants.Switch, false, true, true);
			RuleExeUtil.swapDeviceList(fhcdzList);
			PowerDevice pd2 = fhcdzList.get(1);
			replaceStr = CZPService.getService().getDevName(pd2)==null?"":CZPService.getService().getDevName(pd2);
		}
		return replaceStr;
	}

}

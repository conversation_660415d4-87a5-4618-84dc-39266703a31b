package czprule.wordcard.replaceclass.impl;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplace;

public class ReplaceStrZBYXMX implements TempStringReplace {

	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev, String desc) {
		
		// TODO Auto-generated method stub
		String replaceStr = "";
		if("主变运行母线".equals(tempStr)){
			List<PowerDevice> templist = RuleExeUtil.getDeviceList(curDev, SystemConstants.Switch, null, null, null, false, true, true, true);
			RuleExeUtil.swapLowDeviceList(templist);
			
			HashMap<String, List<PowerDevice>> midSideMLMap = new LinkedHashMap<String, List<PowerDevice>>();
			 for (PowerDevice device : templist) {
				 
				 List<PowerDevice> mlList = RuleExeUtil.getDeviceList(device,SystemConstants.MotherLine , null, false, true, true);
				 for (PowerDevice ml : mlList) {
					 if(ml.getDeviceRunModel().equals(CBSystemConstants.RunModelThreeTwo))
						 continue;
					 if(!midSideMLMap.containsKey(String.valueOf(ml.getPowerVoltGrade())))
						 midSideMLMap.put(String.valueOf(ml.getPowerVoltGrade()), new ArrayList<PowerDevice>());
					 List<PowerDevice> midSideMLList = midSideMLMap.get(String.valueOf(ml.getPowerVoltGrade()));
					 if(!midSideMLList.contains(ml))
						 midSideMLList.add(ml);
				 }
				 
			 }
			 for(List<PowerDevice> list : midSideMLMap.values()) {
				 RuleExeUtil.swapLowDeviceList(list);
				 replaceStr = replaceStr + CZPService.getService().getDevName(list) + "、";
			 }
			
			 
			 if(replaceStr.endsWith("、"))
				 replaceStr = replaceStr.substring(0, replaceStr.length()-1);
				
		}
		return 	replaceStr;
	}

}

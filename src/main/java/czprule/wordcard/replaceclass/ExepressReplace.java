package czprule.wordcard.replaceclass;

import czprule.system.ShowMessage;
import czprule.wordcard.WordExpressionInf;
import czprule.wordcard.dao.DeviceStateMentManager;

public class ExepressReplace {

	public boolean execute(String exp) {
		// TODO Auto-generated method stub
		if(exp.indexOf("&&")>0){
			String[] expKeys=exp.split("&&");
			return (this.expAction(expKeys[0])&&this.expAction(expKeys[1]));
		}
		if(exp.indexOf("||")>0){
			String[] expKeys=exp.split("||");
			return (this.expAction(expKeys[0])||this.expAction(expKeys[1]));
		}
		return this.expAction(exp);
	}
	
	private boolean expAction(String expKey){
		
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
	    String beanClass=dsmm.getWCBean(expKey, "1");
	    boolean results=false;
	    try {
	    	WordExpressionInf we=  (WordExpressionInf)Class.forName(beanClass).newInstance();
	    	results=we.execute();
		} catch (ClassNotFoundException e) {
			ShowMessage.view("自定义表达式["+expKey+"]找不到处理类："+beanClass);
			results=false;
		} catch (Exception e) {
			e.printStackTrace();
			ShowMessage.view("自定义表达式["+expKey+"]处理类："+beanClass+"内部异常！");
			results=false;
		}
		return results;
	}

}

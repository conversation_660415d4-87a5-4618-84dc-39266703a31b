package czprule.wordcard.replaceclass;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.model.CardItemModel;

public class ReplaceUtil {
	private static Map<String,String> pro1=new HashMap<String, String>();
	private static Map<String,String> pro2=new HashMap<String, String>();
	static {
		List<Map> list1 = DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"T_A_CARDWORDTEMPREPLACE order by tempstr");
		for (Map map : list1) {
			String temp=(String)map.get("tempstr");
			String classbean=(String)map.get("classbean");
			String classbeanforlist=(String)map.get("classbeanforlist");
			if(classbean!=null)
				pro1.put(temp, classbean);
			if(classbeanforlist!=null)
				pro2.put(temp, classbeanforlist);
		}  
		
	}
	
	public static String strReplace(String tempStr,List<PowerDevice> curDevlist,PowerDevice stationDev,String desc){
		String replaceStr="";
		TempStringReplaceDevList tsr=getInstance1(tempStr);
		if(tsr!=null){
			replaceStr=tsr.strReplace(tempStr, curDevlist,stationDev,desc);
		}else{
			PowerDevice curDev=curDevlist.get(0);
			TempStringReplace t= getInstance(tempStr);
			if(t!=null){
				replaceStr=t.strReplace(tempStr, curDev, stationDev, desc);
			}else{
				TempBooleanReplace boo= getbooleanInstance(tempStr);
				if(boo!=null){
					boolean del=boo.strReplace(tempStr, curDev, stationDev, desc);
					if(del==true){
						replaceStr="";
					}else{
						return null;
					}
				}
				else
					return null;
			}
			
		}
		return replaceStr;
	}
	
	public static String strReplace(String tempStr,PowerDevice curDev,String desc){
		String replaceStr="";
		TempStringReplace tsr=getInstance(tempStr);
		if(tsr!=null){
			replaceStr=tsr.strReplace(tempStr, curDev,curDev,desc);
			if(replaceStr == null || replaceStr.toLowerCase().equals("false")) {
				return "";
			}
		}

		return replaceStr;
	}
	

	public static String strReplace(String tempStr,PowerDevice curDev,PowerDevice stationDev,String desc, List<CardItemModel> cardModelList, StringBuffer buff, String firstStr, String lastStr){
		String replaceStr="";
		TempStringReplace tsr=getInstance(tempStr);
		if(tsr!=null){
			replaceStr=tsr.strReplace(tempStr, curDev,stationDev,desc);
			if(replaceStr == null || replaceStr.toLowerCase().equals("false")) {
				return null;
			}
		}else{
			TempBooleanReplace boo=getbooleanInstance(tempStr);
			if(boo!=null){
				boolean del=boo.strReplace(tempStr, curDev, stationDev, desc);
				if(del==true){
					replaceStr="";
				}else{
					return null;
				}
			}
		}
//		if(replaceStr!=null&&replaceStr.contains(";")){
//			String []replaces=replaceStr.split(";");	
//			StringBuilder decBuff;
//			for (int i = 0; i < replaces.length-1; i++) {
//				CardItemModel cardModel=new CardItemModel(); 
//				String rp = replaces[i];
//				decBuff=new StringBuilder();
//				decBuff.append(buff);
//				decBuff.append(firstStr);
//				decBuff.append(rp);
//				decBuff.append(lastStr);
//				cardModel.setCardDesc(decBuff.toString());
//				cardModel.setStationName(CZPOperatorBackup.getOperator().getDevName(CBSystemConstants.getPowerStation(stationDev.getPowerStationID())));
//				cardModel.setUuIds(UUID.randomUUID().toString());
//				cardModelList.add(cardModel);
//			}
//			replaceStr=replaces[replaces.length-1]; 
//		}
		return replaceStr;
	}
	
	/**
	 * 根据不同的字符串获取不同的子类
	 * */
	private static TempStringReplace getInstance(String tempStr) {
		TempStringReplace tsr;
		try {
			tsr=(TempStringReplace) Class.forName(pro1.get(tempStr)).newInstance();
		} catch (Exception e) {
			return null;
		}
		return tsr;
	}
	/**
	 * 根据不同的字符串获取不同的Boolean子类
	 * */
	private static TempBooleanReplace getbooleanInstance(String tempStr) {
		TempBooleanReplace boo;
		try {
			boo=(TempBooleanReplace) Class.forName(pro1.get(tempStr)).newInstance();
		} catch (Exception e) {
			return null;
		}
		return boo;
	}

	private static TempStringReplaceDevList getInstance1(String tempStr) {
		TempStringReplaceDevList tsr;
		try {
			tsr=(TempStringReplaceDevList) Class.forName(pro2.get(tempStr)).newInstance();
		} catch (Exception e) {
			return null;
		}
		return tsr;
	}

	/**
	 * 对[]内的字符串替换
	 * */
	public  static String strReplaceSP(String allStr,PowerDevice curDev){
        Pattern pattern = Pattern.compile("(?<=\\[)(.+?)(?=\\])");
        Matcher matcher = pattern.matcher(allStr);
        while(matcher.find()){
        	String str=matcher.group();
        	String rep = strReplace(str, curDev,null,"",null,null,null,null);
            allStr=allStr.replace("[", "").replace("]", "").replace(str, rep==null?"":rep);
        }
        return allStr;
	}
	/**
	 * 从目标字符串中找出符合正则表达式的所有字符串集合
	 * @param str  输入的字符串
	 *        reg  正则表达式
	 * */
	public static  List<String> getRegList(String str,String reg) {
		List<String> list = new ArrayList<String>();
		Pattern pattern = Pattern.compile(reg);
		Matcher matcher = pattern.matcher(str);
		while (matcher.find()) {
			if (!list.contains(matcher.group()))
				list.add(matcher.group());
		}
		return list;
	}
	//配置文件更新到数据库
	public static void  updateConfigToDB(){
		Set<String> keys = pro1.keySet();
		for (String key : keys) {
			String value=pro1.get( key);
			int i=DBManager.update("update "+CBSystemConstants.opcardUser+"T_A_CARDWORDTEMPREPLACE set classbean=? where tempstr=?",value ,key);
			if(i==0){
		    	String uuid=UUID.randomUUID().toString();
		    	DBManager.update("insert into  "+CBSystemConstants.opcardUser+"T_A_CARDWORDTEMPREPLACE(id,tempstr,classbean) values(?,?,?)",uuid ,key,value);
		    }
		}
	}
	public static void main(String[] args) {
		//String str="[111][2322][444]";
		//strReplaceSP(str);
		updateConfigToDB();
	}

}

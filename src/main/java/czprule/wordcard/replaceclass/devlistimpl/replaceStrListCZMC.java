package czprule.wordcard.replaceclass.devlistimpl;

import java.util.List;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.wordcard.replaceclass.TempStringReplaceDevList;

public class replaceStrListCZMC implements TempStringReplaceDevList {

	@Override
	public String strReplace(String tempStr, List<PowerDevice> curDevlist,
			PowerDevice stationDev, String desc) {
		String replaceStr = "";
		PowerDevice curDev = curDevlist.get(0);
		if ("厂站名称".equals(tempStr)) {
			if (curDev.getDeviceType().equals(SystemConstants.PowerStation)
					|| curDev.getDeviceType().equals(
							SystemConstants.PowerFactory))
				replaceStr = CZPService.getService().getDevName(curDev);
			else
				replaceStr = CZPService.getService().getDevName(
						CBSystemConstants.getPowerStation(curDev
								.getPowerStationID()));
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.devlistimpl;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplaceDevList;
public class replaceStrListSBGLDZ implements TempStringReplaceDevList{

	@Override
	public String strReplace(String tempStr, List<PowerDevice> curDevlist,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if(tempStr.equals("设备关联刀闸")) {
			
			String devNameSet = "";
			for (PowerDevice dev : curDevlist) {
				String devName = CZPService.getService().getDevName(dev);
				devNameSet = devNameSet + devName + "、";
			}
			if (devNameSet.endsWith("、"))
				devNameSet = devNameSet.substring(0, devNameSet.length() - 1);
			replaceStr = devNameSet;
			
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.devlistimpl;
import java.util.List;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplaceDevList;
public class replaceStrListSBBH implements TempStringReplaceDevList{

	@Override
	public String strReplace(String tempStr, List<PowerDevice> curDevlist,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if("设备编号".equals(tempStr)){
			String devNameSet = "";
			for (PowerDevice dev : curDevlist) {
				String devName = StringUtils.getSwitchCode(dev.getPowerDeviceName());
				devNameSet = devNameSet + devName + "、";
			}
			if (devNameSet.endsWith("、"))
				devNameSet = devNameSet.substring(0, devNameSet.length() - 1);
			replaceStr = devNameSet;
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass.devlistimpl;
import java.util.List;

import com.tellhow.czp.app.service.CZPService;

import czprule.model.PowerDevice;
import czprule.wordcard.replaceclass.TempStringReplaceDevList;
public class replaceStrListSBMC implements TempStringReplaceDevList{

	@Override
	public String strReplace(String tempStr, List<PowerDevice> curDevlist,
			PowerDevice stationDev, String desc) {
		String replaceStr="";
		if(tempStr.equals("设备名称")) {
			
//			String devNameSet = "";
//			for (int i = 0; i < curDevlist.size(); i++) {
//				String devName = StringUtils.getSwitchCode(curDevlist.get(i).getPowerDeviceName());
//				devNameSet = devNameSet + devName + "、";
//			}
//			if(!devNameSet.equals(""))
//				devNameSet = devNameSet.substring(0, devNameSet.length()-1);
//			String devName0 = curDevlist.get(0).getPowerDeviceName();
//			String devNum0 = StringUtils.getSwitchCode(devName0);
//			replaceStr = devName0.replace(devNum0, devNameSet);
			if(curDevlist.size()==1){
				replaceStr = CZPService.getService().getDevName(curDevlist.get(0));
			}else
				replaceStr = CZPService.getService().getDevName(curDevlist);
			
		}
		return replaceStr;
	}

}

package czprule.wordcard.replaceclass;

import java.util.ArrayList;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;
import java.util.UUID;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.constants.SystemConstants;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.dao.CustomCodexDao;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.WordStringInf;
import czprule.wordcard.dao.DeviceStateMentManager;
import czprule.wordcard.model.CardItemModel;
import czprule.wordcard.replaceclass.impl.ReplaceStrPWXLDGDW;
import czprule.wordcard.replaceclass.impl.ReplaceStrQRZBKGDK;
import czprule.wordcard.replaceclass.impl.ReplaceStrQRZBKGDKYD;

public class WordCardBuild {
	static int order = 0;
	public static List<CardItemModel> splitCardItemModels = new ArrayList<CardItemModel>();
	public List<PowerDevice> loadLineList = new ArrayList<PowerDevice>();

	public List<CardItemModel> execute(List<PowerDevice> curDevlist, String desc) {
		List<CardItemModel> cardModelList = new ArrayList<CardItemModel>(); // 指令模型集合
		CardItemModel cardModel = new CardItemModel(); // 指令数据模型
		String cardDesc = desc.replace("{EQS}", "");
		PowerDevice curDev = curDevlist.get(0);

		// 对设备名进行修改 并存回 curDevlist
		// String newname = curDev.getPowerDeviceName();
		// System.out.println(newname);

		Pattern pattern = Pattern.compile("\\$\\{M:([^\\}]*)\\}");
		Matcher matcher = pattern.matcher(desc);
		while (matcher.find()) {
			boolean isValid = true;
			String exp = matcher.group(1);
			desc = desc.replace("${M:" + exp + "}", "");
			if (curDev.getDeviceRunModel().equals(
					CBSystemConstants.RunModelOneMotherLine)
					&& exp.indexOf("单母接线") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunModel().equals(
					CBSystemConstants.RunModelDoubleMotherLine)
					&& exp.indexOf("双母接线") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunModel().equals(
					CBSystemConstants.RunModelThreeTwo)
					&& exp.indexOf("3/2接线") >= 0)
				isValid = true;
			else if (RuleUtil.isTransformerNQ(curDev)
					&& exp.indexOf("内桥接线") >= 0)
				isValid = true;
			else if (!RuleUtil.isTransformerNQ(curDev)
					&& CBSystemConstants.isParallelRun
					&& exp.indexOf("并列运行") >= 0)
				isValid = true;
			else if (!RuleUtil.isTransformerNQ(curDev)
					&& !CBSystemConstants.isParallelRun
					&& exp.indexOf("分列运行") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(SystemConstants.InOutLine)
					&& curDev.getDeviceRunModel().equals(
							CBSystemConstants.RunModelDoubleLine)
					&& exp.indexOf("双侧电源") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(SystemConstants.InOutLine)
					&& curDev.getDeviceRunModel().equals(
							CBSystemConstants.RunModelOneLine)
					&& exp.indexOf("单侧电源") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.SwitchSeparate)
					&& !curDev.getDeviceKind().equals(
							CBSystemConstants.KindKnifeXC)
					&& exp.indexOf("刀闸") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.SwitchSeparate)
					&& curDev.getDeviceKind().equals(
							CBSystemConstants.KindKnifeXC)
					&& exp.indexOf("小车") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchXL)
					&& exp.indexOf("线路开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchML)
					&& RuleExeUtil.isSwitchDoubleML(curDev)
					&& exp.indexOf("母联开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchML)
					&& !RuleExeUtil.isSwitchDoubleML(curDev)
					&& exp.indexOf("分段开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDYC)
					&& exp.indexOf("电源侧主变开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchFHC)
					&& exp.indexOf("负荷侧主变开关") >= 0)
				isValid = true;
			else if ((curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDYC) || curDev
					.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSwitchFHC))
					&& curDev.getDeviceType().equals(SystemConstants.Switch)
					&& exp.indexOf("主变属开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDR)
					&& exp.indexOf("电容器开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchPL)
					&& exp.indexOf("旁路开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDJ)
					&& exp.indexOf("电机开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchZYB)
					&& exp.indexOf("站用变开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchQT)
					&& exp.indexOf("其它开关") >= 0)
				isValid = true;
			else if (RuleExeUtil.isSourceSide(curDev) && exp.equals("电源侧"))
				isValid = true;
			else if (!RuleExeUtil.isSourceSide(curDev) && exp.equals("负荷侧"))
				isValid = true;
			else
				isValid = false;
			if (!isValid)
				return cardModelList;
		}
		if (desc.indexOf("(综)") >= 0)
			desc = desc.replace("(综)", "");
		
		Object[] splitParams = init(desc, '{', '}'); // 自定义变量获取：格式${aaa} 获取aaa
		boolean isDel = false; // 是否删除指令
		StringBuffer descBuff = new StringBuffer();
		List<String> paramsKey = (ArrayList<String>) splitParams[1]; // 标签集合
		if (paramsKey.size() > 0) {
			List firstStr = (ArrayList) splitParams[0];
			String lastStr = splitParams[2].toString();
			String tempStr = ""; // 标签
			for (int j = 0; j < paramsKey.size(); j++) {
				String replaceStr = ""; // 标签替换字符串
				tempStr = paramsKey.get(j).toString(); // 标签
				if (tempStr.indexOf(":") > 0) {
					String[] tags = tempStr.split(":");
					if ("EQ".equals(tags[0].trim().toUpperCase())) {
						DeviceFind df = new DeviceFind();
						replaceStr = df.execute(tags[1].trim(),
								curDevlist.get(0));
						if (df.dtdLists.size() == 0) {
							isDel = true;
						}

					}
					if ("ES".equals(tags[0].trim().toUpperCase())) {
						DeviceFind df = new DeviceFind();
						replaceStr = df.execute(tags[1].trim(),
								curDevlist.get(0));
						if (df.dtdLists.size() == 0) { // 存在
							isDel = true;
						}
					}
					if ("EC".equals(tags[0].trim().toUpperCase())) {
						DeviceFind df = new DeviceFind();
						replaceStr = df.execute(tags[1].trim(),
								curDevlist.get(0));
						if (df.dtdLists.size() > 0) { // 不存在
							isDel = true;
						}
					}
				} else {
					for(PowerDevice curdev:curDevlist){
						replaceStr = ReplaceUtil.strReplace(tempStr, curDevlist,
								curdev, cardDesc);
						if(replaceStr!=null){
							break;
						}
					}
					
				}
				if (replaceStr == null)
					isDel = true;
				descBuff.append(firstStr.get(j).toString());
				descBuff.append(replaceStr);
			}
			descBuff.append(lastStr);
		} else
			descBuff.append(desc);
		if (!isDel) {
			// 判断CardDesc是否含有关键字'/r/n'
			String cardDescs = descBuff.toString();
			if (cardDescs != null && !cardDescs.equals("")) {
				String dw = "";
				if (desc.indexOf("(") >= 0) {
					pattern = Pattern.compile("(?<=\\()[^\\)]+");
					matcher = pattern.matcher(cardDescs);
					while (matcher.find()) {
						dw = matcher.group();
						
						String organ = ReplaceUtil.strReplace(dw, curDev, desc);
						
						cardModel.setStationName(organ);
						
						cardDescs = cardDescs.replace("(" + dw + ")", "");
						
						if (!organ.equals(""))
							dw = organ;
						
						break;
					}
				}
				
				if(cardModel.getStationName().equals("")){
					cardModel.setStationName(curDev.getPowerStationName());
				}
		
				String[] cardDescArr = null;
				if (cardDescs.contains("/r/n"))
					cardDescArr = cardDescs.split("/r/n");
				else if (cardDescs.contains("\r\n"))
					cardDescArr = cardDescs.split("\r\n");
				else
					cardDescArr = new String[] { cardDescs };
				if (cardDescArr.length >= 1) {
					for (int k = 0; k < cardDescArr.length; k++) {
						String cardDescNew = cardDescArr[k];
						CardItemModel cardModel1 = new CardItemModel(); // 指令数据模型
						if (cardDescNew.contains("@")) {
							cardModel1.setStationName(cardDescNew
									.split("@")[0]);
							cardDescNew = cardDescNew.split("@")[1];
						} else
							cardModel1.setStationName(cardModel
									.getStationName());
	
						if (CBSystemConstants.addXH
								&& isInteger(cardModel
										.getCardItem())) {
							cardModel1.setCardItem((Integer
									.parseInt(cardModel
											.getCardItem()) + k)
									+ "");
						} else {
							cardModel1.setCardItem(cardModel
									.getCardItem());
						}
						cardModel1.setCardDesc(cardDescNew);
						cardModel1.setCzdwID(cardModel.getCzdwID());
						if(!dw.equals("")){
							cardModel1.setStationName(dw);
							cardModel1.setShowName(dw);
						}
						
						cardModelList.add(cardModel1);
					}
				} else {
					cardModel.setCardDesc(descBuff.toString());
					cardModel.setStationName(CZPService.getService().getDevName(
							CBSystemConstants.getPowerStation(curDev
									.getPowerStationID())));
					if(!dw.equals("")){
						cardModel.setStationName(dw);
						cardModel.setShowName(dw);
					}
					cardModelList.add(cardModel);
				}
			} else {
				cardModel.setCardDesc(descBuff.toString());
				cardModel.setStationName(CZPService.getService().getDevName(
						CBSystemConstants.getPowerStation(curDev
								.getPowerStationID())));
				cardModelList.add(cardModel);
			}
			
	//			cardModel.setCardDesc(descBuff.toString());
	//			cardModel.setStationName(CZPService.getService().getDevName(
	//					CBSystemConstants.getPowerStation(curDev
	//							.getPowerStationID())));
	//			cardModelList.add(cardModel);
			}
	
			if (desc.indexOf("(配网线路调管单位)") >= 0) {
				ReplaceStrPWXLDGDW pw = new ReplaceStrPWXLDGDW();
				String pwdw = pw.strReplace("配网线路调管单位", curDev, curDev, cardDesc);
				cardModel.setShowName(pwdw);
				cardModel.setStationName(pwdw);
				cardModel
						.setCardDesc(descBuff.toString().replace("(配网线路调管单位)", ""));
			}
	
			return cardModelList;
	}

	public List<CardItemModel> execute(PowerDevice curDev, String desc) {
		System.out.println("--------------WordCardBuild.execute:"+desc);
		desc=desc.replace("{EQS}", "");
		List<String> attentionList = new ArrayList<String>();
		List<Object[]> stationDescList = new ArrayList<Object[]>();// 操作单位
		List<CardItemModel> cardModelList = new ArrayList<CardItemModel>(); // 指令模型集合
		List<DispatchTransDevice> dtdLists = new ArrayList<DispatchTransDevice>(); // 每行命令对应的设备对象集合
		CustomCodexDao ccdd = new CustomCodexDao();
		if ("".equals(desc) || desc == null)
			return cardModelList;

		if (desc.contains("(T接)")) {// (T接)作为T接线需要拆分的指令
			splitCardItemModels.addAll(this.execute(curDev,
					desc.replace("(T接)", "")));
			return cardModelList;
		} else if (desc.contains("T接终)")) {// (T接终)作为T接线需要拆分的指令的结束标识
			splitCardItemModels.addAll(this.execute(curDev,
					desc.replace("(T接终)", "")));
			this.sortListByBdz(splitCardItemModels, cardModelList);
			splitCardItemModels.clear();
			return cardModelList;
		}

		Pattern pattern = Pattern.compile("\\$\\{V:([^\\}]*)\\}");
		Matcher matcher = pattern.matcher(desc);
		if (matcher.find()) {
			boolean isValid = true;
			String exp = matcher.group(1);
			desc = desc.replace("${V:" + exp + "}", "");
			String[] cal = new String[] { "!=", "<=", ">=", "=", "<", ">" };
			for (int i = 0; i < cal.length; i++) {
				if (exp.indexOf(cal[i]) >= 0) {
					double vol = Double.valueOf(exp.replace(cal[i], ""));
					switch (i) {
					case 0:
						isValid = (curDev.getPowerVoltGrade() != vol);
						break;
					case 1:
						isValid = (curDev.getPowerVoltGrade() <= vol);
						break;
					case 2:
						isValid = (curDev.getPowerVoltGrade() >= vol);
						break;
					case 3:
						isValid = (curDev.getPowerVoltGrade() == vol);
						break;
					case 4:
						isValid = (curDev.getPowerVoltGrade() < vol);
						break;
					case 5:
						isValid = (curDev.getPowerVoltGrade() > vol);
						break;
					}
					break;
				}
			}
			if (!isValid)
				return cardModelList;
		}

		/**
		 * 表达式{C:"单侧开关刀闸主变编号"} ${单侧开关刀闸主变编号}有值则，则显示，不存在则不显示
		 */
		pattern = Pattern.compile("\\$\\{C:([^\\}]*)\\}");
		matcher = pattern.matcher(desc);
		if (matcher.find()) {
			boolean isValid = true;
			String exp = matcher.group(1);
			desc = desc.replace("${C:" + exp + "}", "");
			String[] cal = new String[] { "!=", "<=", ">=", "=", "<", ">" };
			for (int i = 0; i < cal.length; i++) {
				if (exp.indexOf(cal[i]) >= 0) {
					if (exp.contains("负")) {
						String vol = exp.replace(cal[i], "");
						ReplaceStrQRZBKGDK str = new ReplaceStrQRZBKGDK();
						// 单侧开关刀闸主变编号存在，整条术语显示
						String bianhao = str.strReplace(vol, curDev, curDev,
								desc);
						if (bianhao.equals("") || bianhao == null) {
							isValid = false;
							break;
						}
					}
					if (exp.contains("源")) {
						String vol = exp.replace(cal[i], "");
						ReplaceStrQRZBKGDKYD str = new ReplaceStrQRZBKGDKYD();
						// 单侧开关刀闸主变编号存在，整条术语显示
						String bianhao = str.strReplace(vol, curDev, curDev,
								desc);
						if (bianhao.equals("") || bianhao == null) {
							isValid = false;
							break;
						}
					}

				}
			}
			if (!isValid)
				return cardModelList;
		}

//		System.out.println("--------------WordCardBuild.execute:"+11111);
		
		
		// 根据开关接线方式确定线路接线方式。若开关接线方式数据为空，将线路接线方式置为单母接线
		PowerDevice equip = curDev;
		if (curDev.getDeviceType().equals(SystemConstants.InOutLine)) {
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource
					.get(curDev.getPowerDeviceID());
			List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad
					.get(curDev.getPowerDeviceID());
			PowerDevice lineDev = curDev;
			if (desc.indexOf("(源)") >= 0) {
				if (sourceLineTrans != null)
					lineDev = sourceLineTrans;
			} else if (desc.indexOf("(负)") >= 0) {
				if (loadLineTrans != null && loadLineTrans.size() > 0)
					lineDev = loadLineTrans.get(0);
			}
			equip = RuleExeUtil.getDeviceSwitch(lineDev);

			if (equip != null && equip.getDeviceRunModel().equals(""))
				equip.setDeviceRunModel(CBSystemConstants.RunModelOneMotherLine);
		}

		/**
		 * 表达式{T:"厂站电压等级"}
		 * 
		 */
		pattern = Pattern.compile("\\$\\{T:([^\\}]*)\\}");
		matcher = pattern.matcher(desc);
		if (matcher.find()) {
			boolean isValid = true;
			String exp = matcher.group(1);
			desc = desc.replace("${T:" + exp + "}", "");
			String[] cal = new String[] { "!=", "<=", ">=", "=", "<", ">" };
			for (int i = 0; i < cal.length; i++) {
				if (exp.indexOf(cal[i]) >= 0) {
					if (equip == null) {
						break;
					}
					double vol = Double.valueOf(exp.replace(cal[i], ""));
					switch (i) {
					case 0:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() != vol);
						break;
					case 1:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() <= vol);
						break;
					case 2:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() >= vol);
						break;
					case 3:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() == vol);
						break;
					case 4:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() < vol);
						break;
					case 5:
						isValid = (CBSystemConstants.getPowerStation(
								equip.getPowerStationID()).getPowerVoltGrade() > vol);
						break;
					}
					break;
				}
			}
			if (!isValid)
				return cardModelList;
		}

		pattern = Pattern.compile("\\$\\{M:([^\\}]*)\\}");
		matcher = pattern.matcher(desc);
		while (matcher.find()) {
			boolean isValid = true;
			String exp = matcher.group(1);
			desc = desc.replace("${M:" + exp + "}", "");
			if (equip != null
					&& equip.getDeviceRunModel().equals(
							CBSystemConstants.RunModelOneMotherLine)
					&& exp.indexOf("单母接线") >= 0) {
				// if(exp.indexOf("线变组接线")>=0){
				// if(exp.indexOf("非线变组接线")>=0){
				// if(!RuleExeUtil.getRunMode(curDev).equals("2")){
				// isValid = true;
				// }else{
				// isValid =false;
				// }
				//
				// }else if(RuleExeUtil.getRunMode(curDev).equals("2")){
				// isValid = true;
				// }else{
				// isValid =false;
				// }
				//
				// }else{
				isValid = true;
				// }
			} else if (equip != null
					&& equip.getDeviceRunModel().equals(
							CBSystemConstants.RunModelDoubleMotherLine)
					&& exp.indexOf("双母接线") >= 0)
				isValid = true;
			else if (equip != null
					&& equip.getDeviceRunModel().equals(
							CBSystemConstants.RunModelThreeTwo)
					&& exp.indexOf("3/2接线") >= 0)
				isValid = true;
			else if (RuleUtil.isTransformerNQ(curDev)
					&& exp.indexOf("内桥接线") >= 0)
				isValid = true;
			else if (!RuleUtil.isTransformerNQ(curDev)
					&& exp.indexOf("非桥型接线") >= 0)
				isValid = true;
			else if (RuleExeUtil.isTransformerXBZ(curDev)
					&& exp.indexOf("线变组接线") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.PowerTransformer)
					&& RuleExeUtil.isTransformerZBDM(curDev)
					&& !RuleExeUtil.isTransformerXBZ(curDev)
					&& !RuleUtil.isTransformerNQ(curDev)
					&& exp.indexOf("主变单母接线") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.PowerTransformer)
					&& !RuleExeUtil.isTransformerZBDM(curDev)
					&& !RuleExeUtil.isTransformerXBZ(curDev)
					&& !RuleUtil.isTransformerNQ(curDev)
					&& exp.indexOf("主变双母接线") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.PowerTransformer)
					&& !RuleUtil.isTransformerNQ(curDev)
					&& !RuleExeUtil.isTransformerXBZ(curDev)
					&& exp.indexOf("主变其他接线") >= 0)
				isValid = true;
			// else if(exp.indexOf("线变组接线")>=0){
			// if(exp.indexOf("单母接线")>=0){
			// isValid=false;
			// }else{
			// if(exp.indexOf("非线变组接线")>=0){
			// if(!RuleExeUtil.getRunMode(curDev).equals("2")){
			// isValid = true;
			// }else{
			// isValid =false;
			// }
			// }else if(RuleExeUtil.getRunMode(curDev).equals("2")){
			// isValid = true;
			// }else{
			// isValid =false;
			// }
			// }
			//
			// }
			else if (!RuleUtil.isTransformerNQ(curDev)
					&& CBSystemConstants.isParallelRun
					&& exp.indexOf("并列运行") >= 0)
				isValid = true;
			else if (!RuleUtil.isTransformerNQ(curDev)
					&& !CBSystemConstants.isParallelRun
					&& exp.indexOf("分列运行") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(SystemConstants.InOutLine)
					&& curDev.getDeviceRunModel().equals(
							CBSystemConstants.RunModelDoubleLine)
					&& exp.indexOf("双侧电源") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(SystemConstants.InOutLine)
					&& curDev.getDeviceRunModel().equals(
							CBSystemConstants.RunModelOneLine)
					&& exp.indexOf("单侧电源") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.SwitchSeparate)
					&& !curDev.getDeviceKind().equals(
							CBSystemConstants.KindKnifeXC)
					&& exp.indexOf("刀闸") >= 0)
				isValid = true;
			else if (curDev.getDeviceType().equals(
					SystemConstants.SwitchSeparate)
					&& curDev.getDeviceKind().equals(
							CBSystemConstants.KindKnifeXC)
					&& exp.indexOf("小车") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchXL)
					&& exp.indexOf("线路开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchML)
					&& RuleExeUtil.isSwitchDoubleML(curDev)
					&& exp.indexOf("母联开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchML)
					&& !RuleExeUtil.isSwitchDoubleML(curDev)
					&& exp.indexOf("分段开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDYC)
					&& exp.indexOf("电源侧主变开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchFHC)
					&& exp.indexOf("负荷侧主变开关") >= 0)
				isValid = true;
			else if ((curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDYC) || curDev
					.getDeviceRunType().equals(
							CBSystemConstants.RunTypeSwitchFHC))
					&& curDev.getDeviceType().equals(SystemConstants.Switch)
					&& exp.equals("主变开关"))
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchPL)
					&& exp.indexOf("旁路开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDR)
					&& exp.indexOf("电容开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchDJ)
					&& exp.indexOf("电机开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchZYB)
					&& exp.indexOf("站用变开关") >= 0)
				isValid = true;
			else if (curDev.getDeviceRunType().equals(
					CBSystemConstants.RunTypeSwitchQT)
					&& exp.indexOf("其它开关") >= 0)
				isValid = true;
			else if (exp.indexOf("电源侧") >= 0
					&& RuleExeUtil.isSourceSide(curDev))
				isValid = true;
			else if (exp.indexOf("负荷侧") >= 0
					&& !RuleExeUtil.isSourceSide(curDev))
				isValid = true;
			else if (exp.indexOf("牵引线") >= 0
					&& RuleExeUtil.isQYline(curDev, desc)) // 广西牵引线特殊操作
				isValid = true;
			else if (exp.indexOf("牵引刀闸") >= 0
					&& RuleExeUtil.isQYKnife(curDev, desc)) // 广西牵引线特殊操作
				isValid = true;
			else if (exp.indexOf("正常线路") >= 0
					&& !RuleExeUtil.isQYline(curDev, desc)) // 广西牵引线特殊操作
				isValid = true;
			else
				isValid = false;
			if (!isValid)
				return cardModelList;
		}

		
//		System.out.println("--------------WordCardBuild.execute:"+22222);
		
		// 一、变电站类型判定
		PowerDevice lineDev = curDev;
		if (curDev.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)) {
			List<PowerDevice> lineList = RuleExeUtil.getDeviceList(curDev,
					SystemConstants.InOutLine,
					SystemConstants.PowerTransformer, true, true, true);
			if (lineList.size() > 0)
				lineDev = lineList.get(0);
		}
		PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(lineDev
				.getPowerDeviceID());
		List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad
				.get(lineDev.getPowerDeviceID());

		List<PowerDevice> pdList = new ArrayList<PowerDevice>();
		if (desc.indexOf("(源)") >= 0) {
			desc = desc.replace("(源)", "");
			if (sourceLineTrans != null)
				pdList.add(sourceLineTrans);
		} else if (desc.indexOf("(负)") >= 0) {
			desc = desc.replace("(负)", "");
			if (loadLineTrans != null && loadLineTrans.size() > 0)
				pdList.addAll(loadLineTrans);
		} else if (desc.indexOf("(对)") >= 0) {
			PowerDevice dev = curDev;
			desc = desc.replace("(对)", "");
			if (curDev.getDeviceType().equals(SystemConstants.Switch)) {
				if (curDev.getDeviceRunType().equals(
						CBSystemConstants.RunTypeSwitchPL)) {
					PowerDevice plLine = null;
					// 由旁路刀闸查找相关代路线；（由旁路开关本身出发，刀闸状态变更后不能收到）
					Map<Integer, DispatchTransDevice> curDtdMap = CBSystemConstants
							.getDtdMap();
					if (curDtdMap != null) {

						for (int i = 1; i < curDtdMap.size() + 1; i++) {
							DispatchTransDevice dtd = CBSystemConstants
									.getDtdMap().get(i);
							PowerDevice device = dtd.getTransDevice();
							if (device.getDeviceRunType().equals(
									CBSystemConstants.RunTypeKnifePL)) {
								List<PowerDevice> list = RuleExeUtil
										.getDeviceDirectList(device,
												SystemConstants.InOutLine);
								if (list.size() > 0) {
									dev = list.get(0);
									break;
								}

							}
						}
					}
				} else {
					List<PowerDevice> list = RuleUtil
							.getSwitchOrderLines(curDev);
					if (list != null && list.size() > 0)
						dev = RuleUtil.getSwitchOrderLines(curDev).get(0);
				}
			}
			if (dev != null
					&& dev.getDeviceType().equals(SystemConstants.InOutLine)) {
				List<PowerDevice> list = RuleExeUtil.getLineOtherSideList(dev);
				if (list.size() > 0)
					pdList.add(list.get(0));
			}
			if (dev != null
					&& dev.getDeviceType().equals(
							SystemConstants.PowerTransformer)) {

				Map<Integer, DispatchTransDevice> curDtdMap = CBSystemConstants
						.getDtdMap();
				if (curDtdMap != null) {

					for (int i = 1; i < curDtdMap.size() + 1; i++) {
						DispatchTransDevice dtd = CBSystemConstants.getDtdMap()
								.get(i);
						PowerDevice device = dtd.getTransDevice();
						if (device.getDeviceType().equals(
								SystemConstants.InOutLine)) {
							List<PowerDevice> list = RuleExeUtil
									.getLineOtherSideList(device);
							if (list.size() > 0) {
								pdList.add(list.get(0));
								break;
							}
						}
					}
				}

			}
		} else
			pdList.add(curDev);

		String dw = "";

		// 术语(负)是否按线路名称顺序出指令
		if (CBSystemConstants.isLoadLineSort) {
			if (pdList.size() > 1
					&& pdList.get(0).getDeviceType()
							.equals(SystemConstants.InOutLine)) {
				RuleExeUtil.sortByLineName(pdList);
				try {
					if (Integer.parseInt(CBSystemConstants.getCurRBM()
							.getBeginStatus()) < Integer
							.parseInt(CBSystemConstants.getCurRBM()
									.getEndState())) {
						Collections.reverse(pdList);
					}
				} catch (Exception e) {
					// TODO: handle exception
				}
				loadLineList.clear();
				loadLineList.addAll(pdList);
			}
		}

		for (PowerDevice dev : pdList) {
			String attention = "";
			
			if (desc.indexOf("[") >= 0) {
		        pattern = Pattern.compile("\\[(.*?)]");
		        matcher = pattern.matcher(desc);
		        if (matcher.find()) {
		          attention = matcher.group();
		          desc = desc.replace(attention, "");
		          attention = attention.replace("[", "").replace("]", "");
		          attentionList.add(attention);
		        }
		    }
			
			
			if (desc.indexOf("(集控)") >= 0) {
				desc = desc.replace("(集控)", "");
				PowerDevice station = CBSystemConstants.getMapPowerStation()
						.get(dev.getPowerStationID());
				PowerDevice organ = CBSystemConstants.getMapPowerOrgan().get(
						station.getOrgaId());

				String dictionarys = CBSystemConstants
						.getDictionaryName(CZPService.getService().getDevName(
								station));
				if (dictionarys == null || "".equals(dictionarys)) {
					stationDescList.add(new Object[] { dev, desc });
				} else {
					stationDescList.add(new Object[] { dev, desc, dictionarys, attention });
				}
			} else if (desc.indexOf("()") >= 0) {
				stationDescList.add(new Object[] { dev, desc.replace("()", ""),
						"()" });

			} else if (desc.indexOf("(") >= 0) {
				pattern = Pattern.compile("(?<=\\()[^\\)]+");
				matcher = pattern.matcher(desc);
				while (matcher.find()) {
					dw = matcher.group();
					if (dw.contains("代替")) {
						stationDescList.add(new Object[] { dev, desc });
						break;
					}
					desc = desc.replace("(" + dw + ")", "");
					String organ = ReplaceUtil.strReplace(dw, dev, desc);
					if (organ.equals(""))
						organ = dw;
		            stationDescList.add(new Object[] { dev, desc, organ, attention });
					break;
				}

			} else
				stationDescList.add(new Object[] { dev, desc });
		}

		String tempDesc = "";
		String SuperDev = "";
		PowerDevice stationDev = null;
		List<Object[]> tempDescList = new ArrayList<Object[]>();
		for (int i = 0; i < stationDescList.size(); i++) {
			Object[] stationdesc = stationDescList.get(i);
			stationDev = (PowerDevice) stationdesc[0];
			if (stationdesc.length > 2 && stationdesc[2] != null) {
				SuperDev = String.valueOf(stationdesc[2]);
			}
			tempDesc = stationdesc[1].toString();
			pattern = Pattern.compile("\\$\\{P:([^\\}]*)\\}");
			matcher = pattern.matcher(desc);
			if (matcher.find()) {
				String exp = matcher.group(1);
				DeviceFind df = new DeviceFind();
				df.execute(exp, stationDev);
				for (int j = 0; j < df.dtdLists.size(); j++) {
					PowerDevice dev = df.dtdLists.get(j).getTransDevice();
					List<String> wordList = ccdd.getProtectWord(
							dev.getPowerDeviceID(),
							df.devParam.getDevTagStatus(),
							df.devParam.getDevEndStatus(),
							df.devParam.getDevProp());
					for (int n = 0; n < wordList.size(); n++) {
						String word = wordList.get(n);
						tempDescList.add(new Object[] { stationDev,
								tempDesc.replace("${P:" + exp + "}", word) });
					}
				}

			} else
				tempDescList.add(stationdesc);
		}
		stationDescList.clear();
		stationDescList.addAll(tempDescList);

		
//		System.out.println("--------------WordCardBuild.execute:"+33333);
		
		// 二、表达式判断 格式：$(表达式)XXX*XXX
		for (int i = 0; i < stationDescList.size(); i++) {
			Object[] stationdesc = stationDescList.get(i);
			tempDesc = stationdesc[1].toString();
			Object[] splitExp = init(tempDesc, '(', ')'); // 自定义表达式获取：格式$(aaa)
															// 获取aaa
			List<String> expKeyList = (ArrayList<String>) splitExp[1];
			String lastStr = splitExp[1].toString();
			if (expKeyList.size() > 0) {
				String expKeyStr = expKeyList.get(0).toString(); // 表达式
				String[] expItems = new String[] { "", "" };
				if (lastStr.indexOf("*") > 0) {
					expItems = lastStr.split("*");
				} else {
					expItems[0] = lastStr;
				}
				ExepressReplace expReplace = new ExepressReplace();
				if (expReplace.execute(expKeyStr)) {
					tempDesc = expItems[0];
				} else {
					tempDesc = expItems[1];
				}
				stationdesc[1] = tempDesc;
			}
		}

		// 三、变量替换
		// CardItemModel cardModel=new CardItemModel(); //指令数据模型
		// 黄彩凤注释：针对鹰潭月西线接线方式为T接线并且三个变电站都归属鹰潭地调管辖出票有问题，存在两个负荷侧，修改 2017、10、19
		// 代码移动至737行
		for (int i = 0; i < stationDescList.size(); i++) {
			CardItemModel cardModel = new CardItemModel(); // 指令数据模型
			cardModel = new CardItemModel();// 2018.1.23 修改
											// 重新NEW对象，否则T接线（负）只能出一个操作单位

			 String attention = "";

		      if (attentionList.size() > 0 && i < attentionList.size()) {
		        attention = (String)attentionList.get(i);
		      }
			
			Object[] stationdesc = stationDescList.get(i);
			tempDesc = stationdesc[1].toString();
			stationDev = (PowerDevice) stationdesc[0];
			if (stationDev == null)
				continue;
			PowerDevice station = null;
			if (!stationDev.getPowerStationID().equals(""))
				station = CBSystemConstants.getPowerStation(stationDev
						.getPowerStationID());
			else
				station = stationDev;
			String stationName = CZPService.getService().getDevName(station);
			cardModel.setBdzName(stationName);// 黄正20191214修改，为了不影响其他代码，新增变量再储存一次设备所属变电站
			cardModel.setCzdwID(station.getCimID());

			Object[] splitParams = init(tempDesc, '{', '}'); // 自定义变量获取：格式${aaa}
																// 获取aaa
			boolean isDel = false; // 是否删除指令
			boolean isStatic = false; // 指令是否不关联状态

			String[] descDWs = null;
			StringBuffer descBuff = new StringBuffer();
			List<String> paramsKey = (ArrayList<String>) splitParams[1]; // 标签集合
			boolean isOrderAdd = false;
			if (paramsKey.size() > 0) {
				List firstStr = (ArrayList) splitParams[0];
				String lastStr = splitParams[2].toString();
				String tempStr = ""; // 标签
				for (int j = 0; j < paramsKey.size(); j++) {
					boolean isSplit = false;// 是否分割指令
					String replaceStr = ""; // 标签替换字符串
					tempStr = paramsKey.get(j).toString(); // 标签
					if (tempStr.indexOf(":") > 0) {
						String[] tags = tempStr.split(":");
						// 设备对象函数 格式为： EQ：N=线路开关,S=1,T=2
						//排除安装类型对相格式以@分隔 EQ：N=线路开关|主变开关@母联开关@电容器开关,S=1,T=2
						if ("EQ".equals(tags[0].trim().toUpperCase())) {
							DeviceFind df = new DeviceFind();
							replaceStr = df.execute(tags[1].trim(), stationDev);
							
							if (df.dtdLists.size() == 0) {
								isDel = true;
								break;
							} else {
								dtdLists = df.dtdLists;
							}
							isStatic = false;
						}
						if ("EQS".equals(tags[0].trim().toUpperCase())) { // 用于EQ表达式出多条指令
							DeviceFind df = new DeviceFind();
							replaceStr = df.execute(tags[1].trim(), stationDev);
							if (df.dtdLists.size() == 0) {
								isDel = true;
								break;
							} else {
								dtdLists = df.dtdLists;
							}
							isStatic = false;
							isSplit = true;
						}
						if ("ES".equals(tags[0].trim().toUpperCase())) {
							DeviceFind df = new DeviceFind();
							replaceStr = df.execute(tags[1].trim(), stationDev);
							if (df.dtdLists.size() == 0) { // 存在
								isDel = true;
								break;
							} else {
								dtdLists = df.dtdLists;
							}
							isStatic = true;
						}
						if ("EC".equals(tags[0].trim().toUpperCase())) {
							DeviceFind df = new DeviceFind();
							replaceStr = df.execute(tags[1].trim(), stationDev);
							if (df.dtdLists.size() > 0) { // 不存在
								isDel = true;
								break;
							}
							isStatic = false;
						}
						// 顺序
						if ("X".equals(tags[0].trim().toUpperCase())) {
							cardModel.setCardItem(tags[1].trim());
						}
						// 下令顺序
						if ("Y".equals(tags[0].trim().toUpperCase())) {
							cardModel.setOrderNumber(tags[1].trim());
						}
						if ("S".equals(tags[0].trim().toUpperCase())) {
							String sx = tags[1].trim();
							if (sx.equals("1")) {
								order = 1;
								isOrderAdd = true;
							} else if (sx.equals(String.valueOf(order))) {
							} else {
								order++;
								isOrderAdd = true;
							}
							descBuff.append(String.valueOf(order));
						}
						// 自定义变量
						if ("UP".equals(tags[0].trim().toUpperCase())) {
							DeviceStateMentManager dsmm = new DeviceStateMentManager();
							String beanClass = dsmm.getWCBean(tags[1].trim(),
									"0");
							try {
								WordStringInf ws = (WordStringInf) Class
										.forName(beanClass).newInstance();
								replaceStr = ws.execute();
							} catch (ClassNotFoundException e) {
								ShowMessage.view("标签[" + tags[1].trim()
										+ "]找不到处理类：" + beanClass);
								isDel = true;
								break;
							} catch (Exception e) {
								e.printStackTrace();
								ShowMessage.view("标签[" + tags[1].trim()
										+ "]处理类：" + beanClass + "内部异常！");
								isDel = true;
								break;
							}
						}
					} else {
						replaceStr = ReplaceUtil.strReplace(tempStr, curDev,
								stationDev, desc, cardModelList, descBuff,
								firstStr.get(j).toString(), lastStr);
						if (replaceStr == null) {
							isDel = true;
							break;
						}
					}
					if (CBSystemConstants.splitEQ && dtdLists.size() > 1) {
						String[] strs = replaceStr.split("、");
						String strbak = descBuff.toString();
						descBuff.setLength(0);
						isSplit = true;
						for (String str : strs) {
							descBuff.append(strbak);
							descBuff.append(firstStr.get(j).toString());
							descBuff.append(str);
							descBuff.append("@@");
						}

					} else if (isSplit && dtdLists.size() > 1) {
						descDWs = new String[dtdLists.size()];
						String[] strs = replaceStr.split("、");
						String strbak = descBuff.toString();
						descBuff.setLength(0);
						int in = 0;
						for (String str : strs) {
							descBuff.append(strbak);
							descBuff.append(firstStr.get(j).toString());
							descBuff.append(str);
							descBuff.append("@@");

							if (!dw.equals("")) {
								String organ = ReplaceUtil
										.strReplace(dw, dtdLists.get(in)
												.getTransDevice(), desc);
								if (organ.equals(""))
									organ = dw;
								descDWs[in] = organ;
							} else {
								descDWs[in] = "";
							}

							in++;
						}

					} else {
						descBuff.append(firstStr.get(j).toString());
						descBuff.append(replaceStr);
					}

				}
				if (isDel && isOrderAdd) {
					if (order >= 1)
						order--;
				}
				if (!isDel) {
					String[] descBuffs = descBuff.toString().split("@@");
					int in = 0;
					for (String str : descBuffs) {
						if (descDWs != null && in < descDWs.length)
							SuperDev = descDWs[in];
						in++;
						descBuff.setLength(0);
						String item = cardModel.getCardItem();
						String ordernum = cardModel.getOrderNumber();
						String bdzName = cardModel.getBdzName();
						String czdwid = cardModel.getCzdwID();
						cardModel = new CardItemModel();
						cardModel.setCardItem(item);
						cardModel.setOrderNumber(ordernum);
						cardModel.setBdzName(bdzName);
						cardModel.setCzdwID(czdwid);
						descBuff.append(str);
						// 添加术语
						descBuff.append(lastStr);
						cardModel.setCardDesc(descBuff.toString());
						if (cardModel.getStationName().equals("")) {
							if (SuperDev.equals("")) {
								cardModel.setStationName(stationName);
							} else {
								cardModel.setStationName(SuperDev);
							}
						}
						if (SuperDev.equals("()")) {
							cardModel.setStationName("");
						}
						String uuID = UUID.randomUUID().toString();
						if (!isStatic) {
							if (dtdLists.size() > 0) {
								for (int k = 0; k < dtdLists.size(); k++) {
									DispatchTransDevice dtd = dtdLists.get(k);
									dtd.setUuID(uuID);
								}
							} else if (tempDesc.indexOf("设备名称") >= 0) {
								Map<Integer, DispatchTransDevice> dtds = CBSystemConstants
										.getDtdMap();
								DispatchTransDevice dtd = null;
								PowerDevice dev = null;
								List<PowerDevice> devList = new ArrayList<PowerDevice>();
								for (Iterator iterator = dtds.values()
										.iterator(); iterator.hasNext();) {
									dtd = (DispatchTransDevice) iterator.next();
									if (dtd.getTransDevice().equals(curDev)
											&& dtd.getUuID().equals("")) {
										dtd.setUuID(uuID);
									}
								}
							}
						}

						// 判断CardDesc是否含有关键字'/r/n'
						String cardDescs = cardModel.getCardDesc();
						if (cardDescs != null && !cardDescs.equals("")) {
							String[] cardDescArr = null;
							if (cardDescs.contains("/r/n"))
								cardDescArr = cardDescs.split("/r/n");
							else if (cardDescs.contains("\r\n"))
								cardDescArr = cardDescs.split("\r\n");
							else
								cardDescArr = new String[] { cardDescs };
							if (cardDescArr.length >= 1) {
								for (int k = 0; k < cardDescArr.length; k++) {
									String cardDesc = cardDescArr[k];
									CardItemModel cardModel1 = new CardItemModel(); // 指令数据模型

									// 提取大项序号逻辑
									String extractedDaxiang = null;
									if (cardDesc.contains("[DAXIANG:") && cardDesc.contains("]")) {
										int startIndex = cardDesc.indexOf("[DAXIANG:") + 9; // "[DAXIANG:"的长度是9
										int endIndex = cardDesc.indexOf("]", startIndex);
										if (endIndex > startIndex) {
											extractedDaxiang = cardDesc.substring(startIndex, endIndex);
											// 移除大项标记，保留原始指令内容
											cardDesc = cardDesc.substring(endIndex + 1);
										}
									}

									if (cardDesc.contains("@")) {
										cardModel1.setStationName(cardDesc
												.split("@")[0]);
										cardDesc = cardDesc.split("@")[1];
									} else
										cardModel1.setStationName(cardModel
												.getStationName());

									uuID = UUID.randomUUID().toString();
									cardModel1.setUuIds(uuID);
				                    cardModel1.setRemark(attention);
									cardModel1.setBdzName(cardModel
											.getBdzName());
									// 优先使用提取的大项序号，如果没有则使用原有逻辑
									if (extractedDaxiang != null && !extractedDaxiang.trim().isEmpty()) {
										cardModel1.setCardItem(extractedDaxiang);
									} else if (CBSystemConstants.addXH
											&& isInteger(cardModel
													.getCardItem())) {
										cardModel1.setCardItem((Integer
												.parseInt(cardModel
														.getCardItem()) + k)
												+ "");
									} else {
										cardModel1.setCardItem(cardModel
												.getCardItem());
									}
				                    cardModel1.setRemark(attention);
									cardModel1.setCardDesc(cardDesc);
									cardModel1.setCzdwID(cardModel.getCzdwID());
									cardModelList.add(cardModel1);
								}
							} else {
								cardModel.setUuIds(uuID);
								cardModel.setRemark(attention);
								cardModelList.add(cardModel);
							}
						} else {
							cardModel.setUuIds(uuID);
							cardModel.setRemark(attention);
							cardModelList.add(cardModel);
						}
					}
				}
			} else {
				cardModel.setCardDesc(tempDesc);
				if (SuperDev.equals("")) {
					cardModel.setStationName(stationName);
				} else if (SuperDev.equals("()")) {
					cardModel.setStationName("");
				} else {
					cardModel.setStationName(SuperDev);
				}
				cardModel.setRemark(attention);
				cardModelList.add(cardModel);
			}
		}
		// 对于内部添加的指令统一序号
		for (int i = 0; i < cardModelList.size(); i++) {
			CardItemModel cmode = cardModelList.get(i);
			cmode.setCardItem(cmode.getCardItem());// 黄彩凤 2017，10，，19
													// 代码已做修改请svn核对
		}
		return cardModelList;
	}

	public boolean isInteger(String str) {
		if (str.equals("")) {
			return false;
		}
		Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
		return pattern.matcher(str).matches();
	}

	public Object[] init(String dynStr, char beginSpilt, char endSplit) {
		Object[] results = new Object[3];
		List<String> keyFirst = new ArrayList<String>();
		List<String> keyStr = new ArrayList<String>();
		String lastStr = "";
		StringBuffer buff = new StringBuffer(dynStr);
		int len = buff.length();
		int start = 0, ptr = 1;
		boolean noMatching = true;
		for (ptr = start; ptr < len; ptr++) {
			if (buff.charAt(ptr) == '$' && buff.charAt(ptr + 1) == beginSpilt) {
				if (ptr == 0 || buff.charAt(ptr - 1) != '\\') {
					if (ptr > 2 && buff.charAt(ptr - 2) == '\\') {
						buff.deleteCharAt(ptr - 2);
						--ptr;
					}
					int end = ptr;
					noMatching = false;
					keyFirst.add(buff.substring(start, end));
					ptr += 2;
					for (; ptr < len; ptr++) {
						if (buff.charAt(ptr) == endSplit
								&& buff.charAt(ptr - 1) != '\\') {
							if (buff.charAt(ptr - 2) == '\\') {
								buff.deleteCharAt(ptr - 2);
								--ptr;
							}
							keyStr.add(buff.substring(end + 2, ptr));
							start = ptr + 1;
							noMatching = true;
							break;
						}
					}
				}
			}
		}
		if (noMatching && ptr <= len) {
			lastStr = buff.substring(start, ptr);
		}
		results[0] = keyFirst;
		results[1] = keyStr;
		results[2] = lastStr;
		return results;
	}

	public static String getSideName(PowerDevice curDev, String type,
			String desc, int countKnife) {
		if (curDev.getDeviceType().equals(type)) {
			List<PowerDevice> glines = RuleUtil.getDirectDevice(curDev,
					SystemConstants.SwitchFlowGroundLine);
			if (SystemConstants.PowerTransformer.equals(type)) {
				List<PowerDevice> zxddd = RuleUtil.getZxdddByTransform(curDev);
				// 排除主变中性点地刀
				if (zxddd != null && zxddd.size() != 0) {
					glines.removeAll(zxddd);
				}
			}
			if (glines == null || glines.size() == 0) {
				List<PowerDevice> rms = CBSystemConstants
						.getlinkRMDevice(curDev);
				for (PowerDevice rm : rms) {
					if (desc.contains("挂") && rm.getDeviceStatus().equals("0"))
						continue;
					if (desc.contains("拆") && rm.getDeviceStatus().equals("1"))
						continue;
					rms.remove(rm);
					if (rms.size() < 1) {
						// 阻止异常操作
						break;
					}
				}
				if (rms.size() > countKnife) {
					PowerDevice rm = rms.get(countKnife);
					PowerDevice knife = CBSystemConstants.getPowerDevice(
							curDev.getPowerStationID(), rm.getKnife());
					String knifename = knife.getPowerDeviceName();
					if (!knifename.contains("刀闸")) {
						knifename += "刀闸";
					}
					countKnife++;
					return knifename;

				}
			} else {
				for (PowerDevice line : glines) {
					if (desc.contains("挂")
							&& line.getDeviceStatus().equals("0"))
						continue;
					if (desc.contains("拆")
							&& line.getDeviceStatus().equals("1"))
						continue;
					glines.remove(line);
				}
				if (glines.size() > countKnife) {
					PowerDevice line = glines.get(countKnife);
					List<PowerDevice> knife = RuleUtil.getDirectDevice(curDev,
							line, SystemConstants.SwitchSeparate);
					countKnife++;
					return knife.get(0).getPowerDeviceName();

				}
			}
		}
		return null;
	}

	// 按照变电站对List排序
	private void sortListByBdz(List<CardItemModel> oldList,
			List<CardItemModel> newList) {
		int count=0;
		while (oldList.size() > 0&&count<100) {
			count++;
			if (loadLineList.size() > 0) {
				for (int i = 0; i < oldList.size(); i++) {
					if (CZPService
							.getService()
							.getDevName(
									CBSystemConstants
											.getPowerStation(loadLineList
													.get(0).getPowerStationID()))
							.equals(oldList.get(i).getBdzName())) {
						newList.add(oldList.get(i));
						oldList.remove(i);
						loadLineList.remove(0);
						break;
					}
				}
			} else {
				newList.add(oldList.get(0));
				oldList.remove(0);
			}

			if (newList.size() > 0) {
				for (int i = 0; i < oldList.size(); i++) {
					if (newList.get(newList.size() - 1).getBdzName()
							.equals(oldList.get(i).getBdzName())) {
						newList.add(oldList.get(i));
						oldList.remove(i);
						i--;
					}
				}
			}

		}
		if(newList.size()==0){
			newList.addAll(oldList);
		}
		// int asd =1;
	}
}

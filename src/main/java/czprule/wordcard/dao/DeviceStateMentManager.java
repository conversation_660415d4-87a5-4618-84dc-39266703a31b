package czprule.wordcard.dao;

import java.sql.Connection;
import java.sql.SQLException;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Vector;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.czp.datebase.QueryDeviceDao;
import com.tellhow.czp.staticsql.OpeInfo;
import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;


public class DeviceStateMentManager {
	/**
	 * 
	 * @return 所有设备类型集合（如：开关、母线、线路）
	 */
	public  List<CodeNameModel> getDeviceType()
	{
        List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
//		String sql=" SELECT T.EQUIPTYPE_NAME,T.EQUIPTYPE_FLAG FROM "+CBSystemConstants.opcardUser+"t_e_EQUIPTYPE T WHERE T.EQUIPTYPE_FLAG IN"+
//                   " (SELECT T.DEVICETYPEID FROM "+CBSystemConstants.opcardUser+"t_a_devicestateinfo T GROUP BY T.DEVICETYPEID)  ORDER BY T.EQUIPTYPE_CODE";
        //edit 2014.6.24
        List results=DBManager.query(OPEService.getService().getDeviceTypeSql());
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
			cnm.setName(StringUtils.ObjToString(temp.get("EQUIPTYPE_NAME")));
			devtypes.add(cnm);
		}
		return devtypes;	
	}
	
	/**
	 * 
	 * @return 由设备目标状态获取操作编码
	 */
	public  String getStateCodeByStatus(String devType, String devStatus){
		
		String opcode = CBSystemConstants.opRuleCode;
		String statecode = "";
		String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and  t.statevalue='"+devStatus+"'";
        List results=DBManager.queryForList(sql);
        if(results.size() > 0) {
        	Map map = (Map)results.get(0);
        	statecode = map.get("statecode").toString();
        }
        else {
        	sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='0' and  t.statevalue='"+devStatus+"'";
            results=DBManager.queryForList(sql);
            if(results.size() > 0) {
            	Map map = (Map)results.get(0);
            	statecode = map.get("statecode").toString();
            }
        }
		return statecode;	
	}
	
	public  String getStateCodeByStatus(String devType, String devStatus, String cardType){
		
		String opcode = CBSystemConstants.opRuleCode;
		String statecode = "";
		String sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='"+cardType+"' and  t.statevalue='"+devStatus+"'";
        List results=DBManager.queryForList(sql);
        if(results.size() > 0) {
        	Map map = (Map)results.get(0);
        	statecode = map.get("statecode").toString();
        }else {
        	sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and  t.statevalue='"+devStatus+"'";
            results=DBManager.queryForList(sql);
            if(results.size() > 0) {
            	Map map = (Map)results.get(0);
            	statecode = map.get("statecode").toString();
            } else {
            	sql="select t.statecode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='0' and  t.statevalue='"+devStatus+"'";
                results=DBManager.queryForList(sql);
                if(results.size() > 0) {
                	Map map = (Map)results.get(0);
                	statecode = map.get("statecode").toString();
                }
            }
        }
       
		return statecode;	
	}
	/**
	 *
	 * @return 由设备目标状态获取操作编码
	 */
	public  String getStateCodeByStatus(String devType, String devStatus,String stateName, String expStateName){

		String opcode = CBSystemConstants.opRuleCode;
		String statecode = "";
		String sql="select t.statecode from opcard.t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='"+CBSystemConstants.cardbuildtype+"' and  t.statevalue='"+devStatus+"' and t.statename like '%"+stateName+"%' and t.statename not like '%"+expStateName+"%' ";
		List results=DBManager.queryForList(sql);
		if(results.size() > 0) {
			Map map = (Map)results.get(0);
			statecode = map.get("statecode").toString();
		}
		else {
			sql="select t.statecode from opcard.t_a_devicestateinfo t where t.islock = '0' and t.devicetypeid='"+devType+"' and t.opcode='"+opcode+"' and t.cardbuildtype='0' and  t.statevalue='"+devStatus+"' and t.statename like '%"+stateName+"%' and t.statename not like '%"+expStateName+"%' ";
			results=DBManager.queryForList(sql);
			if(results.size() > 0) {
				Map map = (Map)results.get(0);
				statecode = map.get("statecode").toString();
			}
		}
		return statecode;
	}
	/**
	 * 
	 * @return 所有设备状态集合（如：运行、热备用、冷备用、检修）
	 */
	public  List<CodeNameModel> getDeviceStatus(String deviceType){
      List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
//		String sql="SELECT NVL(T.SWITCHSTATE_CODE,T.STATE_CODE) STATECODE,NVL(T.STATE_NAME,T.SWITCHSTATE_NAME) STATENAME FROM "+CBSystemConstants.opcardUser+"t_E_EQUIPTYPESTATE T,"+CBSystemConstants.opcardUser+"t_E_EQUIPTYPE S WHERE T.EQUIPTYPE_ID=S.EQUIPTYPE_ID AND S.EQUIPTYPE_FLAG='"+deviceType+"' ORDER BY T.STATE_CODE";
        //edit 2014.6.24
		List results=DBManager.query(OPEService.getService().getDeviceStatusSql(deviceType));
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("STATECODE")));
			cnm.setName(StringUtils.ObjToString(temp.get("STATENAME")));
			devtypes.add(cnm);
		}
		return devtypes;	
	}
	/**
	 * 
	 * @return 所有设备右键操作集合（如：运行、倒母）
	 */
	public  List<CodeNameModel> getDeviceState(String deviceType, String cardBuildType){
        List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
        String sql = "SELECT T.STATECODE,DECODE(S.STATENAME,NULL,T.STATENAME,S.STATENAME||'-'||T.STATENAME) STATENAME FROM "+CBSystemConstants.opcardUser+"t_A_DEVICESTATEINFO T LEFT JOIN "+CBSystemConstants.opcardUser+"t_A_DEVICESTATEINFO S ON T.PARENTCODE=S.STATECODE WHERE T.opcode='"+CBSystemConstants.opCode+"' AND T.DEVICETYPEID='"+deviceType+"' AND T.STATETYPE='0' AND T.CARDBUILDTYPE='"+cardBuildType+"' and t.islock = '0' ORDER BY S.STATEORDER,T.STATEORDER";
		List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("STATECODE")));
			cnm.setName(StringUtils.ObjToString(temp.get("STATENAME")));
			devtypes.add(cnm);
		}
		return devtypes;	
	}
	/**
	 * 返回设备操作指令集合
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @return 指令集合
	 */
	public  List<String> getStateMents(String devType,String beginState,String tagState,String statetype,String cardtype){
		String opcode = CBSystemConstants.opRuleCode;
		List<String> descs=new ArrayList<String>();
		String sql="SELECT T2.DEVICESTATEMENTCB FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T1,"+CBSystemConstants.opcardUser+"t_a_CARDWORDCB T2 WHERE\n " +
                   "T1.ZBID=T2.F_ZBID AND T1.DEVICETYPEID='"+devType+"' AND T1.BEGINSTATUS='"+beginState+"' AND T1.ENDSTATE='"+tagState+"' AND T1.STATETYPE='"+statetype+"' AND T1.CARDTYPE='"+cardtype+"' AND T1.OPCODE='"+opcode+"' ORDER BY T2.ORDERID";
        List results=DBManager.queryForList(sql);
        if(results.size() == 0) {
        	sql="SELECT T2.DEVICESTATEMENTCB FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T1,"+CBSystemConstants.opcardUser+"t_a_CARDWORDCB T2 WHERE\n " +
                    "T1.ZBID=T2.F_ZBID AND T1.DEVICETYPEID='"+devType+"' AND T1.BEGINSTATUS='-1' AND T1.ENDSTATE='"+tagState+"' AND T1.STATETYPE='"+statetype+"' AND T1.CARDTYPE='"+cardtype+"' AND T1.OPCODE='"+opcode+"' ORDER BY T2.ORDERID";
        	results=DBManager.queryForList(sql);
        }
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			String word = StringUtils.ObjToString(temp.get("DEVICESTATEMENTCB"));
			if(!word.equals(""))
				descs.add(word);
		}
		return descs;	
	}
	/**
	 * 返回设备操作任务
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @param statetype 开票类型  状态令or元件令
	 * @param cardtype 开票方式 正常票or点图票
	 * @return 操作任务
	 */
	public String getCZRW(String devType,String beginstatus,String tagState,String statetype) {
		return getCZRW(devType,beginstatus,tagState,statetype,CBSystemConstants.cardbuildtype);
}	
	public String getCZRW(String devType,String beginstatus,String tagState,String statetype,String cardtype)
{
		String opcode = CBSystemConstants.opRuleCode;
		String sql="SELECT T.CZRW FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+
		"' AND BEGINSTATUS='"+beginstatus+"' AND T.ENDSTATE='"+tagState+"' AND T.CARDTYPE='"+cardtype+"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
		opcode+"'";
		
        List results=DBManager.queryForList(sql);
        if(results.size() == 0) {
        	sql="SELECT T.CZRW FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+"' AND T.CARDTYPE='"+cardtype+"' AND BEGINSTATUS='-1' AND T.ENDSTATE='"+
        	tagState+"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
        	opcode+"'";
        	
            results=DBManager.queryForList(sql);
        }
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			return StringUtils.ObjToString(temp.get("CZRW"));
		}
		return "";	
	}
	/**
	 * 返回备注事项
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @param statetype 开票类型  状态令or元件令
	 * @param cardtype 开票方式 正常票or点图票
	 * @return 备注事项
	 */
	public String getBZSX(String devType,String beginstatus,String tagState,String statetype)
	{
		String opcode = CBSystemConstants.opRuleCode;
		String sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+
			"' AND BEGINSTATUS='"+beginstatus+"' AND T.ENDSTATE='"+tagState+"'  AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
			opcode+"'";
			
	        List results=DBManager.query(sql);
	        if(results.size() == 0) {
	        	sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+"' AND BEGINSTATUS='-1' AND T.ENDSTATE='"+
	        	tagState+"'  AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
	        	opcode+"'";
	        	
	            results=DBManager.query(sql);
	        }
	        Map temp=new HashMap();
			for (int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				return StringUtils.ObjToString(temp.get("BZSX"));
			}
			return "";	
		}
	/**
	 * 返回备注事项
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @param statetype 开票类型  状态令or元件令
	 * @param cardtype 开票方式 正常票or点图票
	 * @return 备注事项
	 */
	public String getBZSX(String devType,String beginstatus,String tagState,String statetype,String cardtype)
	{
		String opcode = CBSystemConstants.opRuleCode;
		String sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+
			"' AND BEGINSTATUS='"+beginstatus+"' AND T.ENDSTATE='"+tagState+"'  AND T.CARDTYPE='"+cardtype+"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
			opcode+"'";
			
	        List results=DBManager.queryForList(sql);
	        if(results.size() == 0) {
	        	sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+"' AND BEGINSTATUS='-1' AND T.ENDSTATE='"+
	        	tagState+"'  AND T.CARDTYPE='"+cardtype+"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
	        	opcode+"'";
	        	
	            results=DBManager.queryForList(sql);
	        }
	        Map temp=new HashMap();
			for (int i = 0; i < results.size(); i++) {
				temp=(Map)results.get(i);
				return StringUtils.ObjToString(temp.get("BZSX"));
			}
			return "";	
		}
	/**
	 * 返回设备主表ID
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @return 操作任务
	 */
	public String getZBID(String devType,String beginstate,String tagState,String statetype, String cardtype){
		String sql="SELECT T.ZBID FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+"' AND BEGINSTATUS='"+beginstate+"' AND T.ENDSTATE='"+tagState+"' AND T.STATETYPE='"+statetype+"' AND T.CARDTYPE='"+cardtype+"' AND T.OPCODE='"+CBSystemConstants.opCode+"'";
        List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			return StringUtils.ObjToString(temp.get("ZBID"));
		}
		return "";	
	}
	
	public void deleteZBRecord(String zbid){
		String sql= "";
		sql = "delete from "+CBSystemConstants.opcardUser+"t_a_cardwordcb t where t.f_zbid='"+zbid+"'";
		DBManager.execute(sql);
		sql = "delete from "+CBSystemConstants.opcardUser+"t_a_cardwordzb t where t.zbid='"+zbid+"'";
		DBManager.execute(sql);
	}
	
	/**
	 * 插入设备术语主变记录
	 * @param devType
	 * @param beginStatus
	 * @param state
	 * @param czrw
	 * @return
	 */
	
	public String insertZBRecord(String devType,String beginStatus,String state,String statetype, String cardtype,String czrw,String bz)
	{
		
		String seq=java.util.UUID.randomUUID().toString();
	    String sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB(ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID,BZSX) VALUES('"+seq+"','"+devType+"','"+beginStatus+"','"+state+"','"+statetype+"','"+
	    CBSystemConstants.opCode+"','"+czrw+"',0,'"+cardtype+"','"+0+"','"+bz+"')";

		DBManager.execute(sql);
		return seq;
	}
	/**
	 * 更新主表记录操作任务
	 * @param zbid 主表ID
	 * @param czrw 操作任务
	 * @return
	 */
	
	/*public void updateZBRecord(String zbid,String czrw){
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB SET CZRW='"+czrw+"' WHERE ZBID='"+zbid+"'";
		DBManager.execute(sql);
	}*/
	
	public void updateZBRecord(String zbid,String czrw,String bz){
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_A_CARDWORDZB SET CZRW='"+czrw+"',BZSX='"+bz+"' WHERE ZBID='"+zbid+"'";
		DBManager.execute(sql);
	}
	
	/*
	 * 获得备注信息
	 * */
	public String getZBBeiZhu(String devType,String beginstatus,String tagState,String statetype)
	{
		Map map=new HashMap();
		String Beizhu=null;
		
		String sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+
		"' AND BEGINSTATUS='"+beginstatus+"' AND T.ENDSTATE='"+tagState+"' AND T.STATETYPE='"+statetype+"'";
		
		List list=DBManager.query(sql);
		if(list.size()==0)
		{
			return "";
		}
	
			map=(Map) list.get(0);
			Beizhu=(String) map.get("bzxs");
			
			for (int i = 0; i < list.size(); i++) 
            {
		        map=(Map)list.get(i);
		        return StringUtils.ObjToString(map.get("BZSX"));
	        }
			return "";
	}
	/*
	 * 获得备注信息（传参正常票0，点图票1）
	 * */
	public String getZBBeiZhu(String devType,String beginstatus,String tagState,String statetype,String cardtype)
	{
		Map map=new HashMap();
		String Beizhu=null;
		
		String sql="SELECT T.BZSX FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devType+
		"' AND BEGINSTATUS='"+beginstatus+"' AND T.ENDSTATE='"+tagState+"' AND T.STATETYPE='"+statetype+"' AND T.CARDTYPE='"+cardtype+"' ";
		
		List list=DBManager.query(sql);
		if(list.size()==0)
		{
			return "";
		}
	
			map=(Map) list.get(0);
			Beizhu=(String) map.get("bzxs");
			
			for (int i = 0; i < list.size(); i++) 
            {
		        map=(Map)list.get(i);
		        return StringUtils.ObjToString(map.get("BZSX"));
	        }
			return "";
	}
	
	/**
	 * 插入主表ID对应的设备术语集合
	 * @param zbid 主表ID
	 * @param descs 术语集合
	 */
	public void insertCBrecords(String zbid,String[] descs){
		String sql="DELETE "+CBSystemConstants.opcardUser+"t_a_CARDWORDCB T WHERE T.F_ZBID='"+zbid+"'";
		try {
			DBManager.execute(sql);
			
			//二、插入从表记录  要求事物处理
			String tempStr="";
			for (int i = 0; i < descs.length; i++) {
				tempStr=new String(descs[i].trim());
				sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CARDWORDCB VALUES('"+tempStr+"','"+zbid+"',"+(i+1)+")";
				DBManager.execute(sql);
			}
			
		} catch (Exception e) {
			e.printStackTrace();
		} 
	}

	/**
	 * 复制设备术语
	 * @param zbid 主表ID
	 * @param descs 术语集合
	 */
	public void copyRecord(String srcType,String tagType){
		String sql= "";
		sql = "delete from "+CBSystemConstants.opcardUser+"t_a_cardwordcb t where t.f_zbid in (select a.zbid from "+CBSystemConstants.opcardUser+"t_a_cardwordzb a where a.devicetypeid='"+tagType+"' AND a.OPCODE='"+CBSystemConstants.opCode+"')";
		DBManager.execute(sql);
		sql = "delete from "+CBSystemConstants.opcardUser+"t_a_cardwordzb t where t.devicetypeid='"+tagType+"' AND T.OPCODE='"+CBSystemConstants.opCode+"'";
		DBManager.execute(sql);
		sql = "SELECT T.ZBID FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+srcType+"' AND T.OPCODE='"+CBSystemConstants.opCode+"'";
        List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			temp=(Map)results.get(i);
			String srcZBID = StringUtils.ObjToString(temp.get("ZBID"));
			String tagZBID = java.util.UUID.randomUUID().toString();
			sql = "insert into "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB(ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID) select '"+tagZBID+"','"+tagType+"',t.beginstatus,t.endstate,t.statetype,t.opcode,t.czrw,t.volt,t.cardtype,t.equipid from "+CBSystemConstants.opcardUser+"t_a_cardwordzb t where t.zbid='"+srcZBID+"'";
			DBManager.execute(sql);
			sql = "insert into "+CBSystemConstants.opcardUser+"t_a_cardwordcb(devicestatementcb,f_zbid,orderid) select t.devicestatementcb,'"+tagZBID+"',t.orderid from "+CBSystemConstants.opcardUser+"t_a_cardwordcb t where t.f_zbid='"+srcZBID+"'";
			DBManager.execute(sql);
		}
	}
	
	//返回电压等级集合
	public List<CodeNameModel> getDeviceVolts() {
        List<CodeNameModel>  deviceVolts=new ArrayList<CodeNameModel>();
//		String sql="SELECT T.VOLTAGE_CODE,T.VOLTAGE_NAME FROM "+CBSystemConstants.opcardUser+"t_e_VOLTAGELEVEL T ORDER BY T.VOLTAGE_CODE";
        //edit 2014.6.25
        String sql=OPEService.getService().DeviceStateMentManagerSql();
        List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("voltage_code")));
			cnm.setName(StringUtils.ObjToString(temp.get("voltage_name")));
			deviceVolts.add(cnm);
		}
		return deviceVolts;	
	}
	//返回自定义规则集合
	public List<CodeNameModel> getUserRules() {
        List<CodeNameModel>  ruleList=new ArrayList<CodeNameModel>();
//		String sql="SELECT T.WORDVALUE,T.WORDBEANCLASS FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T WHERE T.RULETYPE <>'system'";
		String sql="SELECT T.WORDVALUE,T.WORDBEANCLASS FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN T WHERE T.RULETYPE ='01'";
        List results=DBManager.query(sql);
        Map temp=new HashMap();
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("WORDBEANCLASS")));
			cnm.setName(StringUtils.ObjToString(temp.get("WORDVALUE")));
			ruleList.add(cnm);
		}
		return ruleList;	
	}
	/**
	 * @param wordvalue 自定义标签
	 * @param wordCardType 0:自定义变量 1：自定义表达式
	 * @return 自定义术语标签对应的处理类
	 */
	public String getWCBean(String wordvalue,String wordCardType){
		String resultStr="";
		String sql="SELECT WORDBEANCLASS FROM "+CBSystemConstants.opcardUser+"t_A_CARDWORDTEMPREPLACE T "
				+ " WHERE T.WORDCARDTYPE="+wordCardType+" AND T.WORDVALUE='"+wordvalue+"' order by tempstr";
		List results=DBManager.query(sql);
        Map temp=null;
        if(results.size()>0){
        	temp=(Map)results.get(0);
			resultStr=StringUtils.ObjToString(temp.get("WORDBEANCLASS"));
        }
		return resultStr;	
	}
	
	public void insertWCBean(String wordvalue,String wordBeanClass,String wordCardType){
		String sql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CARDWORDBEAN(WORDID,WORDVALUE,WORDBEANCLASS,EQUIPTYPE)" +
				   "VALUES('"+java.util.UUID.randomUUID().toString()+"','"+wordvalue+"','"+wordBeanClass+"',1)";
		DBManager.execute(sql);
	}
	
	/**
	 * 变电站返回设备操作指令集合
	 * @param devType 设备类型
	 * @param beginstate 起始状态
	 * @param tagState 目标状态
	 * @return 指令集合
	 */
	public  List<String> getBdzStateMents(String classname){
		List<String> descs=new ArrayList<String>();
		String sql="SELECT CARDWORD FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDBYRULE WHERE RULEZBID IN (SELECT WORDID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDBEAN WHERE WORDBEANCLASS = '"+classname+"')  ORDER BY ORDERID";
        List<Map<String,String>> results=DBManager.query(sql);
        
        Map<String,String> temp=new HashMap<String, String>();
		for (int i = 0; i < results.size(); i++) {
			temp= results.get(i);
			String word = StringUtils.ObjToString(temp.get("CARDWORD"));
			if(!word.equals(""))
				descs.add(word);
		}
        
		return descs;	
	}
	
	
	public String getBdzCZRW(String devType,String beginstatus,String tagState){
		String sql="";
		
        List<Map<String,String>> results=DBManager.query(sql);
        
        if(results.size()>0){
        	return StringUtils.ObjToString(results.get(0).get("CZRW"));
        }
        
		return "";	
	}
	
}

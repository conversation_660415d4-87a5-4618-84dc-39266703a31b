/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 设备规则选择器
 * 作    者 : 张余平
 * 开发日期 : 2011-06-30
 * 修改日期 ：
 * 修改说明 ： 
 * 修 改 人 ：
 **/

package czprule.wordcard.view;

import java.awt.Toolkit;
import java.util.ArrayList;
import java.util.List;

import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

import com.tellhow.graphicframework.utils.StringUtils;
import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.rule.dao.RuleManagerDao;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.RuleManager;
import czprule.system.CBSystemConstants;

/** 
 *   
 * <AUTHOR>
 */
public class StateCustomDialog extends javax.swing.JDialog {

	private List<Object[]> copeDate1 = new ArrayList<Object[]>();
	private List<Object[]> copeDate2 = new ArrayList<Object[]>();

	/** Creates new form CustomRuleDialog_ */
	public StateCustomDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.init();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
    // <editor-fold defaultstate="collapsed" desc="Generated Code">//GEN-BEGIN:initComponents
    private void initComponents() {

        buttonGroup1 = new javax.swing.ButtonGroup();
        jComboBox1 = new javax.swing.JComboBox();
        jComboBox3 = new javax.swing.JComboBox();
        jLabel7 = new javax.swing.JLabel();
        jLabel9 = new javax.swing.JLabel();
        jPanel1 = new javax.swing.JPanel();
        jButton1 = new javax.swing.JButton();
        jButton2 = new javax.swing.JButton();
        jScrollPane1 = new javax.swing.JScrollPane();
        jTable1 = new javax.swing.JTable();
        jButton7 = new javax.swing.JButton();
        jButton8 = new javax.swing.JButton();
        jButton5 = new javax.swing.JButton();
        jButton6 = new javax.swing.JButton();
        jButton11 = new javax.swing.JButton();

        setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

        jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
        jComboBox1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox1ActionPerformed(evt);
            }
        });

        jComboBox3.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
        jComboBox3.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jComboBox3ActionPerformed(evt);
            }
        });

        jLabel7.setText("设备类型选择：");

        jLabel9.setText("操作动作：");

        jPanel1.setBorder(javax.swing.BorderFactory.createTitledBorder("专家规则库"));

        jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.png"))); // NOI18N
        jButton1.setToolTipText("删除");
        jButton1.setBorder(null);
        jButton1.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton1ActionPerformed(evt);
            }
        });

        jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.png"))); // NOI18N
        jButton2.setToolTipText("新增");
        jButton2.setBorder(null);
        jButton2.setFocusPainted(false);
        jButton2.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton2ActionPerformed(evt);
            }
        });

        jTable1.setFont(new java.awt.Font("宋体", 0, 13));
        jScrollPane1.setViewportView(jTable1);

        jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
        jButton7.setToolTipText("上移");
        jButton7.setBorder(null);
        jButton7.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton7ActionPerformed(evt);
            }
        });

        jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
        jButton8.setToolTipText("下移");
        jButton8.setBorder(null);
        jButton8.setFocusPainted(false);
        jButton8.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton8ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout jPanel1Layout = new org.jdesktop.layout.GroupLayout(jPanel1);
        jPanel1.setLayout(jPanel1Layout);
        jPanel1Layout.setHorizontalGroup(
            jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jPanel1Layout.createSequentialGroup()
                .addContainerGap(689, Short.MAX_VALUE)
                .add(jButton2)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton7)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton8)
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jButton1)
                .add(2, 2, 2))
            .add(org.jdesktop.layout.GroupLayout.TRAILING, jScrollPane1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 772, Short.MAX_VALUE)
        );
        jPanel1Layout.setVerticalGroup(
            jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(jPanel1Layout.createSequentialGroup()
                .add(jPanel1Layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(jButton1)
                    .add(jButton8)
                    .add(jButton7)
                    .add(jButton2))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(jScrollPane1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 218, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                .addContainerGap(30, Short.MAX_VALUE))
        );

        jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
        jButton5.setToolTipText("保存");
        jButton5.setBorder(null);
        jButton5.setFocusPainted(false);
        jButton5.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton5ActionPerformed(evt);
            }
        });

        jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/copy.gif"))); // NOI18N
        jButton6.setToolTipText("复制");
        jButton6.setBorder(null);
        jButton6.setFocusPainted(false);
        jButton6.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton6ActionPerformed(evt);
            }
        });

        jButton11.setVisible(false);
        jButton11.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/paste.gif"))); // NOI18N
        jButton11.setToolTipText("取消复制");
        jButton11.setBorder(null);
        jButton11.setFocusPainted(false);
        jButton11.addActionListener(new java.awt.event.ActionListener() {
            public void actionPerformed(java.awt.event.ActionEvent evt) {
                jButton11ActionPerformed(evt);
            }
        });

        org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(getContentPane());
        getContentPane().setLayout(layout);
        layout.setHorizontalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(layout.createSequentialGroup()
                        .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING, false)
                            .add(jComboBox3, 0, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                            .add(jLabel9, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                            .add(jComboBox1, 0, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
                            .add(org.jdesktop.layout.GroupLayout.TRAILING, jLabel7, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 121, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jPanel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE))
                    .add(org.jdesktop.layout.GroupLayout.TRAILING, layout.createSequentialGroup()
                        .add(jButton11)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton6)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jButton5)
                        .add(9, 9, 9)))
                .addContainerGap())
        );
        layout.setVerticalGroup(
            layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
            .add(layout.createSequentialGroup()
                .addContainerGap()
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.TRAILING)
                    .add(jButton6)
                    .add(jButton11)
                    .add(jButton5, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, 16, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                .add(layout.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
                    .add(layout.createSequentialGroup()
                        .add(39, 39, 39)
                        .add(jLabel7)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jComboBox1, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE)
                        .add(56, 56, 56)
                        .add(jLabel9)
                        .addPreferredGap(org.jdesktop.layout.LayoutStyle.RELATED)
                        .add(jComboBox3, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
                    .add(jPanel1, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)))
        );

        pack();
    }// </editor-fold>//GEN-END:initComponents

	private void jButton11ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		copeDate1.clear();
		copeDate2.clear();
		this.jButton11.setVisible(false);
		this.jButton6.setToolTipText("复制");
	}

	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (jButton6.getToolTipText().trim().equals("复制")) {

			for (int i = 0; i < jTable1.getRowCount(); i++) {
				CodeNameModel word = null;
				CodeNameModel beginstatus = null;
				CodeNameModel state = null;
				CodeNameModel devRunType = null;
				String tranType = "";
				word = ((CodeNameModel) jtableModel1.getValueAt(i, 2)).copy();
				if (jtableModel1.getValueAt(i, 3) != null) {
					devRunType = ((CodeNameModel) jtableModel1.getValueAt(i, 3))
							.copy();
				}
				if (jtableModel1.getValueAt(i, 4) != null) {
					beginstatus = ((CodeNameModel) jtableModel1
							.getValueAt(i, 4)).copy();
				}
				if (jtableModel1.getValueAt(i, 5) != null) {
					state = ((CodeNameModel) jtableModel1.getValueAt(i, 5))
							.copy();
				}
				if (jtableModel1.getValueAt(i, 1) != null) {
					tranType = jtableModel1.getValueAt(i, 1).toString();
				}
				copeDate1.add(new Object[] { i + 1, tranType, word, devRunType,
						beginstatus, state });
			}
			

			jButton6.setToolTipText("粘贴");
			jButton11.setVisible(true);
		} else {
			for (int i = 0; i < copeDate1.size(); i++) {
				jtableModel1.addRow(copeDate1.get(i));
			}
			for (int i = 0; i < copeDate2.size(); i++) {
				jtableModel2.addRow(copeDate2.get(i));
			}
		}

	}



	

	/**
	 * 场景表下移
	 * @param evt
	 */
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.movedownTableRow(jTable1);
	}

	/**
	 * 场景表上移
	 * @param evt
	 */
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.moveupTableRow(jTable1);
	}

	/**
	 * 保存按钮事件
	 */
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		RuleManagerDao rm = new RuleManagerDao();
		String runmodel = ""; //接线方式
		String devicetype = ""; //设备类型
		String status = ""; //设备状态
		String state = ""; //设备动作
		String voltvalue = ""; //电压等级

		
		devicetype = ((CodeNameModel) this.jComboBox1.getSelectedItem())
				.getCode();
		
		state = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();
		String zbid = rm.getZBID(runmodel, devicetype, status, state, "");

		RuleBaseMode rbm = new RuleBaseMode();
		String wordid = "";
		rm.delRuleCB(zbid); //保存前先删除原来的信息
		for (int i = 0; i < jtableModel1.getRowCount(); i++) {
			String beginStatus = ""; //起始状态
			String devstate = ""; //操作动作
			String tranType = ""; //变电站类型
			String devRunType = ""; //设备运行类型
			if (jtableModel1.getValueAt(i, 2) == null
					|| jtableModel1.getValueAt(i, 2).equals(""))
				continue;
			wordid = ((CodeNameModel) jtableModel1.getValueAt(i, 2)).getCode();
			if (jtableModel1.getValueAt(i, 4) != null) {
				beginStatus = ((CodeNameModel) jtableModel1.getValueAt(i, 4))
						.getCode();
			}
			rbm.setBeginStatus(beginStatus);
			if (jtableModel1.getValueAt(i, 5) != null) {
				devstate = ((CodeNameModel) jtableModel1.getValueAt(i, 5))
						.getCode();
			}
			rbm.setEndState(devstate);
			if (jtableModel1.getValueAt(i, 3) != null) {
				devRunType = ((CodeNameModel) jtableModel1.getValueAt(i, 3))
						.getCode();
			}
			rbm.setDeviceruntype(devRunType);
			tranType = jtableModel1.getValueAt(i, 1).toString();
			rbm.setTranType(tranType);
			rm.insertRuleCB(zbid, wordid, StringUtils.ObjToString(i), rbm);
		}
		for (int i = 0; i < jtableModel2.getRowCount(); i++) {
			String beginStatus = ""; //起始状态
			String devstate = ""; //操作动作
			String tranType = ""; //变电站类型
			String devRunType = ""; //设备运行类型
			if (jtableModel2.getValueAt(i, 2) == null
					|| jtableModel2.getValueAt(i, 2).equals(""))
				continue;
			wordid = ((CodeNameModel) jtableModel2.getValueAt(i, 2)).getCode();
			if (jtableModel2.getValueAt(i, 4) != null) {
				beginStatus = ((CodeNameModel) jtableModel2.getValueAt(i, 4))
						.getCode();
			}
			rbm.setBeginStatus(beginStatus);
			if (jtableModel2.getValueAt(i, 5) != null) {
				devstate = ((CodeNameModel) jtableModel2.getValueAt(i, 5))
						.getCode();
			}
			rbm.setEndState(devstate);
			if (jtableModel2.getValueAt(i, 3) != null) {
				devRunType = ((CodeNameModel) jtableModel2.getValueAt(i, 3))
						.getCode();
			}
			rbm.setDeviceruntype(devRunType);
			tranType = jtableModel2.getValueAt(i, 1).toString();
			rbm.setTranType(tranType);
			rm.insertRuleCB(zbid, wordid, StringUtils.ObjToString(i), rbm);
		}
		javax.swing.JOptionPane.showMessageDialog(this, "保存成功!",
				CBSystemConstants.SYSTEM_TITLE, JOptionPane.WARNING_MESSAGE);
	}

	/**
	 * 场景新增按钮
	 * @param evt
	 */
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (!isok()) {
			javax.swing.JOptionPane
					.showMessageDialog(this, "请选择对应条件!",
							CBSystemConstants.SYSTEM_TITLE,
							JOptionPane.WARNING_MESSAGE);
			return;
		}
		RuleManager rm = new RuleManager(this, true, "1");
		Object[] returnValues = rm.getSelectRuleClass();
		if (returnValues == null) {
			return;
		}
		if (returnValues[0] == null)
			return;
		Object[] rowData = { jtableModel2.getRowCount() + 1, returnValues[4],
				returnValues[0], returnValues[1], returnValues[2],
				returnValues[3] };
		this.jtableModel2.addRow(rowData);
	}

	/**
	 * 前置条件保存按钮事件
	 * @param evt
	 */
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (!isok()) {
			javax.swing.JOptionPane
					.showMessageDialog(this, "请选择对应条件!",
							CBSystemConstants.SYSTEM_TITLE,
							JOptionPane.WARNING_MESSAGE);
			return;
		}
		RuleManager rm = new RuleManager(this, true, "0");
		Object[] returnValues = rm.getSelectRuleClass();
		if (returnValues == null) {
			return;
		}
		if (returnValues[0] == null)
			return;
		Object[] rowData = { jtableModel2.getRowCount() + 1, returnValues[4],
				returnValues[0], returnValues[1], returnValues[2],
				returnValues[3] };
		this.jtableModel1.addRow(rowData);
	}



	/**
	 * 前置场景表删除按钮
	 * @param evt
	 */
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		WindowUtils.removeTableRow(jTable1);
	}

	private void jRadioButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable1();
	}

	private void jRadioButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable1();
	}

	private void jComboBox3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable1();
	}

	private void init() {
		this.jComboBox1.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
		
		this.jComboBox3.setModel(InitDeviceTypeChockBox
				.getDeviceStateCheckBox("",""));
		
		this.initTable1();

	}

	//初始化场景设置表模型
	private void initTableModel(String flag, DefaultTableModel tableModel) {

		if (!isok()) {
			return;
		}
		String runmodel = ""; //接线方式
		String devicetype = ""; //设备类型
		String status = ""; //设备状态
		String state = ""; //设备动作
		String voltvalue = ""; //电压等级
		String cardbuildtype="0";//开票方式
		

		devicetype = ((CodeNameModel) this.jComboBox1.getSelectedItem())
				.getCode();
		
		state = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();
		RuleManagerDao rmd = new RuleManagerDao();
		
		List<Object[]> results = rmd.getRuleCB(cardbuildtype,runmodel, devicetype, status,
				state, flag, "");
		for (int i = 0; i < results.size(); i++) {
			tableModel.addRow(results.get(i));
		}
	}

	//初始化场景逻辑表
	private void initTable1() {
		jtableModel1 = new DefaultTableModel(null, new String[] { "序号", "电站类型",
				"逻辑条件", "设备运行类型", "状态", "执行动作" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				return false;
			}
		};
		jTable1.setModel(jtableModel1);
		this.initTableModel("0", jtableModel1);
		jTable1.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable1.getColumnModel().getColumn(1).setMaxWidth(80);
		jTable1.getColumnModel().getColumn(2).setMinWidth(200);
	}



	private void jRadioButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable1();
	}

	private void jComboBox4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
	}

	private void jComboBox2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTable1();
	}

	private void jComboBox1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		CodeNameModel cnm = (CodeNameModel) this.jComboBox1.getSelectedItem();
		
		this.jComboBox3.setModel(InitDeviceTypeChockBox
				.getDeviceStateCheckBox(cnm.getCode(),""));
	}

	private boolean isok() {
		String value = ((CodeNameModel) this.jComboBox1.getSelectedItem())
				.getCode();
		if ("".equals(value)) {
			return false;
		}
	
		value = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();
		if ("".equals(value)) {
			return false;
		}
		return true;
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				StateCustomDialog dialog = new StateCustomDialog(
						new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

    // Variables declaration - do not modify//GEN-BEGIN:variables
    private javax.swing.ButtonGroup buttonGroup1;
    private javax.swing.JButton jButton1;
    private javax.swing.JButton jButton11;
    private javax.swing.JButton jButton2;
    private javax.swing.JButton jButton5;
    private javax.swing.JButton jButton6;
    private javax.swing.JButton jButton7;
    private javax.swing.JButton jButton8;
    private javax.swing.JComboBox jComboBox1;
    private javax.swing.JComboBox jComboBox3;
    private javax.swing.JLabel jLabel7;
    private javax.swing.JLabel jLabel9;
    private javax.swing.JPanel jPanel1;
    private javax.swing.JScrollPane jScrollPane1;
    private javax.swing.JTable jTable1;
    // End of variables declaration//GEN-END:variables

	private javax.swing.table.DefaultTableModel jtableModel1 = null;
	private javax.swing.table.DefaultTableModel jtableModel2 = null;

}

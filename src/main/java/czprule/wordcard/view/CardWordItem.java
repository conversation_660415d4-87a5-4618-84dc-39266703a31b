/*
 * CardWordItem.java
 *
 * Created on __DATE__, __TIME__
 */

package czprule.wordcard.view;

import java.awt.Toolkit;

import czprule.system.ShowMessage;

/**
 *
 * <AUTHOR>
 */
public class CardWordItem extends javax.swing.JDialog {

	String wordCardType = ""; //标签类型

	/** Creates new form CardWordItem */
	public CardWordItem(javax.swing.JDialog parent, boolean modal,
			String wordCardType) {
		super(parent, modal);
		this.wordCardType = wordCardType;
		if ("0".equals(wordCardType)) {
			this.setTitle("自定义变量");
		} else {
			this.setTitle("自定义表达式");
		}
		initComponents();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jLabel1 = new javax.swing.JLabel();
		jLabel2 = new javax.swing.JLabel();
		jTextField1 = new javax.swing.JTextField();
		jTextField2 = new javax.swing.JTextField();
		jButton1 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jLabel1.setText("\u5904\u7406\u7c7b\uff1a");

		jLabel2.setText("\u6807    \u7b7e\uff1a");

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/save.png"))); // NOI18N
		jButton1.setToolTipText("\u4fdd\u5b58");
		jButton1.setBorder(null);
		jButton1.setBorderPainted(false);
		jButton1.setFocusPainted(false);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.LEADING)
								.add(layout
										.createSequentialGroup()
										.add(layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.TRAILING,
														false)
												.add(org.jdesktop.layout.GroupLayout.LEADING,
														jLabel2,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														Short.MAX_VALUE)
												.add(org.jdesktop.layout.GroupLayout.LEADING,
														jLabel1,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														64, Short.MAX_VALUE))
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(layout
												.createParallelGroup(
														org.jdesktop.layout.GroupLayout.LEADING)
												.add(jTextField1,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														401, Short.MAX_VALUE)
												.add(jTextField2,
														org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
														401, Short.MAX_VALUE)))
								.add(org.jdesktop.layout.GroupLayout.TRAILING,
										jButton1)).addContainerGap()));
		layout.setVerticalGroup(layout
				.createParallelGroup(org.jdesktop.layout.GroupLayout.LEADING)
				.add(layout
						.createSequentialGroup()
						.addContainerGap()
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.BASELINE)
								.add(jLabel2)
								.add(jTextField1,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
						.add(18, 18, 18)
						.add(layout
								.createParallelGroup(
										org.jdesktop.layout.GroupLayout.BASELINE)
								.add(jLabel1)
								.add(jTextField2,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										org.jdesktop.layout.GroupLayout.PREFERRED_SIZE))
						.addPreferredGap(
								org.jdesktop.layout.LayoutStyle.RELATED, 23,
								Short.MAX_VALUE).add(jButton1)
						.addContainerGap()));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		String wordValue = this.jTextField1.getText().trim();
		String wordBeanClass = this.jTextField2.getText().trim();
		if ("".equals(wordValue) || "".equals(wordBeanClass)) {
			ShowMessage.view("请填写完整信息！");
			return;
		}
		this.setVisible(false);
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				CardWordItem dialog = new CardWordItem(
						new javax.swing.JDialog(), true, "0");
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton1;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JLabel jLabel2;
	private javax.swing.JTextField jTextField1;
	private javax.swing.JTextField jTextField2;
	// End of variables declaration//GEN-END:variables

}

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 刘志刚
 * 开发日期: 2013-7-11 
 *  修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 *
 */
package czprule.wordcard.view;

import java.awt.BorderLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;
import java.util.HashMap;
import java.util.LinkedHashMap;
import java.util.List;
import java.util.Map;
import java.util.Set;

import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JPanel;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableColumnModel;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.ShowMessage;

/**   
 * 用  途:公共界面查询某个table数据选择单挑数据并返回  
 * 使用地方： 需要弹出Dialog查询某个表数据然后选定单挑数据点击确定并返回选定数据
 * @参数1(Map)：输入参数 开关（oprSrcDevice）目标状态tagState
 * @参数2(Map): 输出参数 returnValue 0代表可转换，1代表不可转换
 * @参数2(Map): 
 * @返回值：无
 *
 */
public class TableChooseDialog extends JDialog{
	private JPanel panel1;
	private JPanel panel3;
	private JPanel panel4;
	private JComboBox comboBox;
	private JButton button1;
	private JButton button2;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JTable operatorConfigTable;
	String dialogTitle;  //窗口标题
	List dataList;   //执行的SQL语句
   String[] columnName ; //表头列名
   String[] columnValue ; //表值
	private Map<String,String> chooseDataMap = new HashMap<String,String>();//用于返回选择行数据
	
	public TableChooseDialog (java.awt.Frame parent, boolean modal,Map input) {	
		///传入参数数据准备
		super(parent, modal);
		this.setModal(true);
		dialogTitle=String.valueOf(input.get("dialogtitle"));
		dataList=(List)input.get("dataList");	
		
		 Map<String,String> colQueue = new LinkedHashMap<String,String>();
		colQueue=(Map<String,String>)input.get("columnqueue");
		Set setcolumn=colQueue.keySet();	//key集合
		String[]curstrkey=new String[setcolumn.size()];
		String[]curstrvalue=new String[setcolumn.size()];		
		int j=0;
		for (Object curkey:colQueue.keySet()){
			curstrkey[j]=curkey.toString();//存放key值数组
			  curstrvalue[j]=colQueue.get(curstrkey[j]);//存放value值数组
			  j++;
		}	    
		  columnName=curstrkey;
		  columnValue=curstrvalue;	
		if(dialogTitle==null || dataList==null || columnName==null  || columnValue==null){
			ShowMessage.view("打开公共界面异常，传入参数有误!");
			return;
		}
		
		//初始化界面
		initComponents();
		this.setLocationCenter();
		initOperatorConfigTable();
		this.setVisible(true);	
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	/**
	 * 初始化组件
	 */
	private void initComponents() {
		this.setSize(800, 500);
		button1=new JButton();
		//button1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ok.png"))); // NOI18N
		button1.setText("确定");
		button1.setToolTipText("确定");
		button1.setFocusPainted(false);
		button2=new JButton();
		//button2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/back.png"))); // NOI18N
		button2.setText("取消");
		button2.setToolTipText("取消");
		button2.setFocusPainted(false);
		jScrollPane1 = new javax.swing.JScrollPane();
		operatorConfigTable = new javax.swing.JTable();	
		panel1 = new JPanel(new BorderLayout());
		panel3 = new JPanel();
		panel4 = new JPanel();
		panel3.add(button1);
		panel3.add(button2);
		//panel4 .add(jScrollPane1);
		panel4 .add(operatorConfigTable);
//		panel1.add(panel2, BorderLayout.NORTH);
		panel1.add(panel3, BorderLayout.SOUTH);
		panel1.add(jScrollPane1, BorderLayout.CENTER);
		jScrollPane1.setViewportView(operatorConfigTable);
		
		this.setTitle(dialogTitle);
		this.setContentPane(panel1);
		this.setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		
		button1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		button2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});
	}
	/**
	 * 初始化表格数据
	 */
	public void initOperatorConfigTable() {
		 List results;
		 boolean Communicat=true;
		try {
		results = dataList;
		}catch (Exception e) {
			e.printStackTrace();
			Communicat=false;
			results=null;
			// TODO: handle exception
		}
		
		
		Map temp = null;

		DefaultTableModel dtm = new DefaultTableModel(null, columnName) { //根据传入参数 初始化表头
			@Override
			public boolean isCellEditable(int rowIndex, int columnIndex) {
					return false;		
			}
		};
		//如果DB执行正常则装载数据
		if(Communicat){
		//判断显示table的value值是否在查询语句map内
		for (int k=0;k<columnValue.length;k++){
			temp = (Map) results.get(0);
			if(!temp.containsKey((Object)columnValue[k])){
				ShowMessage.view("打开公共界面异常，显示列【"+columnValue[k]+"】不在查询语句当中!");
				return;
			}
		}
		//装载查询数据
		
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			//	Object[] obj = new Object[] {temp.get("statecode"), temp.get("statename"), temp.get("statevalue"), temp.get("unitcode") };
			Object[] obj = new Object[columnValue.length] ;
			for(int j=0;j<columnValue.length;j++){
				obj[j]=temp.get(columnValue[j]);
			}
			dtm.addRow(obj);
		}
		}
		this.operatorConfigTable.setModel(dtm);
		TableColumnModel tcm = operatorConfigTable.getColumnModel();
		tcm.getColumn(0).setMaxWidth(0);
		tcm.getColumn(0).setMinWidth(0);
		tcm.getColumn(0).setPreferredWidth(0);
		tcm.getColumn(1).setMaxWidth(200);
		tcm.getColumn(1).setMinWidth(200);

		operatorConfigTable.getTableHeader().setReorderingAllowed(false); // 不可整列移动
		operatorConfigTable.getTableHeader().setResizingAllowed(false); // 不可拉动表

		/*隐藏ID首列 视业务需求增减*/ 
	/*   TableColumn tc = operatorConfigTable.getColumnModel().getColumn(0);
		tc.setMaxWidth(0);
		tc.setPreferredWidth(0);
		tc.setWidth(0);
		tc.setMinWidth(0);
		operatorConfigTable.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
		operatorConfigTable.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
		operatorConfigTable.setRowHeight(30);*/
	}
	
	private void jButton1ActionPerformed(ActionEvent evt) {
		this.dispose();
		for(int i=0;i<columnName.length;i++)
		chooseDataMap.put(columnName[i], String.valueOf(operatorConfigTable.getValueAt(operatorConfigTable.getSelectedRow(), i)));   
	}
	
	private void jButton2ActionPerformed(ActionEvent evt) {
		this.dispose();
	}
	
	public Map<String, String>  getChooseData() {
		return this.chooseDataMap;
	}
	
	public static void main(String args[]) {
		Map inputMap=new HashMap();
		Map<String, String> columnMap=new LinkedHashMap<String, String>();
		columnMap.put( "操作编码","statecode");
		columnMap.put( "操作名称","statename");
		columnMap.put("对应状态值","statevalue");
		columnMap.put( "机构编码","unitcode");	
		inputMap.put("dialogtitle"," 标题");
		inputMap.put("dataList", DBManager.queryForList("select * from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' and  order by t.statecode asc"));
		inputMap.put("columnqueue",columnMap );
		JDialog a = new TableChooseDialog(null, false,inputMap);
		
	}
}

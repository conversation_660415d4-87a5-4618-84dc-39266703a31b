/*
 * To change this template, choose Too<PERSON> | Templates
 * and open the template in the editor.
 */

/*
 * MenuConfig.java
 *
 * Created on 2013-3-21, 15:58:43
 */

package czprule.wordcard.view;

import java.awt.Component;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;

import javax.swing.AbstractCellEditor;
import javax.swing.JComboBox;
import javax.swing.JOptionPane;
import javax.swing.JTable;
import javax.swing.event.TableModelEvent;
import javax.swing.event.TableModelListener;
import javax.swing.table.DefaultTableModel;
import javax.swing.table.TableCellEditor;
import javax.swing.table.TableColumn;

import org.apache.commons.collections.map.ListOrderedMap;

import com.tellhow.graphicframework.utils.WindowUtils;

import czprule.model.CodeNameModel;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

/** 
 *    c
 * <AUTHOR>
 */
public class MenuConfig extends javax.swing.JDialog {

	/** Creates new form MenuConfig */
	public MenuConfig(java.awt.Frame parent) {

		initComponents();
		initOperatorConfigTable();
		initMenuConfig();
	}

	public void initMenuConfig() {
		this.jComboBox1.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
		this.menuTable.setRowHeight(30);
	}

	private List results;

	public void initOperatorConfigTable() {
		results = DBManager.query("select t.statecode,t.statename,t.statevalue,t.opcode from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' order by t.statecode asc");
		Map temp = null;

		DefaultTableModel dtm = new DefaultTableModel(null, new String[] { "是否新增的", "操作编码", "操作名称", "对应状态值", "机构编码" }) {
			@Override
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex == 0)
					return false;
				else
					return true;
			}
		};
		/*Collections.sort(results, new Comparator<ListOrderedMap>() {
			public int compare(ListOrderedMap o1, ListOrderedMap o2) {
				int sc1 = Integer.parseInt((String) o1.get("statecode"));
				int sc2 = Integer.parseInt((String) o2.get("statecode"));
				if (sc2 > sc1)
					return 0;
				else
					return 1;
			}
		});*/
		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			Object[] obj = new Object[] { "0", temp.get("statecode"), temp.get("statename"), temp.get("statevalue"), temp.get("unitcode") };
			dtm.addRow(obj);
		}

		this.operatorConfigTable.setModel(dtm);

		operatorConfigTable.getTableHeader().setReorderingAllowed(false); // 不可整列移动
		operatorConfigTable.getTableHeader().setResizingAllowed(false); // 不可拉动表

		TableColumn tc = operatorConfigTable.getColumnModel().getColumn(0);
		tc.setMaxWidth(0);
		tc.setPreferredWidth(0);
		tc.setWidth(0);
		tc.setMinWidth(0);
		operatorConfigTable.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
		operatorConfigTable.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
		operatorConfigTable.setRowHeight(30);
	}

	/**
	 * This method is called from within the constructor to initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is always
	 * regenerated by the Form Editor.
	 */
	@SuppressWarnings("unchecked")
	// <editor-fold defaultstate="collapsed"
	// desc="Generated Code">//GEN-BEGIN:initComponents
	private void initComponents() {

		jTabbedPane1 = new javax.swing.JTabbedPane();
		jPanel1 = new javax.swing.JPanel();
		jScrollPane1 = new javax.swing.JScrollPane();
		operatorConfigTable = new javax.swing.JTable();
		jButton7 = new javax.swing.JButton();
		jButton8 = new javax.swing.JButton();
		jButton1 = new javax.swing.JButton();
		jButton2 = new javax.swing.JButton();
		jButton3 = new javax.swing.JButton();
		jPanel2 = new javax.swing.JPanel();
		jPanel3 = new javax.swing.JPanel();
		jLabel1 = new javax.swing.JLabel();
		jComboBox1 = new javax.swing.JComboBox();
		jScrollPane2 = new javax.swing.JScrollPane();
		menuTable = new javax.swing.JTable();
		jButton4 = new javax.swing.JButton();
		jButton5 = new javax.swing.JButton();
		jButton9 = new javax.swing.JButton();
		jButton10 = new javax.swing.JButton();
		jButton6 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		operatorConfigTable.setModel(new javax.swing.table.DefaultTableModel(new Object[][] { { null, null, null, null }, { null, null, null, null }, { null, null, null, null }, { null, null, null, null } }, new String[] { "Title 1", "Title 2", "Title 3", "Title 4" }));
		jScrollPane1.setViewportView(operatorConfigTable);

		jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton7.setToolTipText("上移");
		jButton7.setBorder(null);
		jButton7.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton7ActionPerformed(evt);
			}
		});

		jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton8.setToolTipText("下移");
		jButton8.setBorder(null);
		jButton8.setFocusPainted(false);
		jButton8.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton8ActionPerformed(evt);
			}
		});

		jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.gif"))); // NOI18N
		jButton1.setToolTipText("删除");
		jButton1.setBorder(null);
		jButton1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});

		jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.gif"))); // NOI18N
		jButton2.setToolTipText("新增");
		jButton2.setBorder(null);
		jButton2.setFocusPainted(false);
		jButton2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});

		jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ico5.gif"))); // NOI18N
		jButton3.setToolTipText("保存");
		jButton3.setBorder(null);
		jButton3.setFocusPainted(false);
		jButton3.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton3ActionPerformed(evt);
			}
		});

		javax.swing.GroupLayout jPanel1Layout = new javax.swing.GroupLayout(jPanel1);
		jPanel1.setLayout(jPanel1Layout);
		jPanel1Layout.setHorizontalGroup(jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGroup(jPanel1Layout.createSequentialGroup().addContainerGap(381, Short.MAX_VALUE).addComponent(jButton3).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton2).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton7).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton8).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton1).addContainerGap()).addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 494, Short.MAX_VALUE));
		jPanel1Layout.setVerticalGroup(jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGroup(jPanel1Layout.createSequentialGroup().addGap(10, 10, 10).addGroup(jPanel1Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addComponent(jButton1).addComponent(jButton8).addComponent(jButton7).addComponent(jButton2).addComponent(jButton3)).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jScrollPane1, javax.swing.GroupLayout.DEFAULT_SIZE, 347, Short.MAX_VALUE).addContainerGap()));

		jTabbedPane1.addTab("操作设置", jPanel1);

		jPanel3.setToolTipText("设置列表");

		javax.swing.GroupLayout jPanel3Layout = new javax.swing.GroupLayout(jPanel3);
		jPanel3.setLayout(jPanel3Layout);
		jPanel3Layout.setHorizontalGroup(jPanel3Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGap(0, 0, Short.MAX_VALUE));
		jPanel3Layout.setVerticalGroup(jPanel3Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGap(0, 390, Short.MAX_VALUE));

		jLabel1.setText("设备类型选择：");

		jComboBox1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jComboBox1ActionPerformed(evt);
			}
		});
		jComboBox1.addAncestorListener(new javax.swing.event.AncestorListener() {
			public void ancestorMoved(javax.swing.event.AncestorEvent evt) {
			}

			public void ancestorAdded(javax.swing.event.AncestorEvent evt) {
				jComboBox1AncestorAdded(evt);
			}

			public void ancestorRemoved(javax.swing.event.AncestorEvent evt) {
			}
		});

		menuTable.setModel(new javax.swing.table.DefaultTableModel(new Object[][] { {}, {}, {}, {} }, new String[] {

		}));
		jScrollPane2.setViewportView(menuTable);

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/ico5.gif"))); // NOI18N
		jButton4.setToolTipText("保存");
		jButton4.setBorder(null);
		jButton4.setFocusPainted(false);
		jButton4.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});

		jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/add.gif"))); // NOI18N
		jButton5.setToolTipText("新增");
		jButton5.setBorder(null);
		jButton5.setFocusPainted(false);
		jButton5.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton5ActionPerformed(evt);
			}
		});

		jButton9.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_up.png"))); // NOI18N
		jButton9.setToolTipText("上移");
		jButton9.setBorder(null);
		jButton9.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton9ActionPerformed(evt);
			}
		});

		jButton10.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/btn_down.png"))); // NOI18N
		jButton10.setToolTipText("下移");
		jButton10.setBorder(null);
		jButton10.setFocusPainted(false);
		jButton10.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton10ActionPerformed(evt);
			}
		});

		jButton6.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/delete.gif"))); // NOI18N
		jButton6.setToolTipText("删除");
		jButton6.setBorder(null);
		jButton6.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton6ActionPerformed(evt);
			}
		});

		javax.swing.GroupLayout jPanel2Layout = new javax.swing.GroupLayout(jPanel2);
		jPanel2.setLayout(jPanel2Layout);
		jPanel2Layout.setHorizontalGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGroup(
				jPanel2Layout.createSequentialGroup().addGroup(
						jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGroup(
								jPanel2Layout.createSequentialGroup().addGap(2, 2, 2).addComponent(jLabel1).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jComboBox1, javax.swing.GroupLayout.PREFERRED_SIZE, 144, javax.swing.GroupLayout.PREFERRED_SIZE).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED, 151, Short.MAX_VALUE).addComponent(jButton4).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton5).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton9).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton10).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jButton6)).addComponent(jScrollPane2,
								javax.swing.GroupLayout.PREFERRED_SIZE, 488, javax.swing.GroupLayout.PREFERRED_SIZE)).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jPanel3, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)));
		jPanel2Layout.setVerticalGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addComponent(jPanel3, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE).addGroup(
				jPanel2Layout.createSequentialGroup().addContainerGap().addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.BASELINE).addComponent(jLabel1).addComponent(jComboBox1, javax.swing.GroupLayout.PREFERRED_SIZE, javax.swing.GroupLayout.DEFAULT_SIZE, javax.swing.GroupLayout.PREFERRED_SIZE)).addComponent(jButton5).addComponent(jButton6).addComponent(jButton4).addGroup(jPanel2Layout.createParallelGroup(javax.swing.GroupLayout.Alignment.TRAILING).addComponent(jButton10).addComponent(jButton9))).addPreferredGap(javax.swing.LayoutStyle.ComponentPlacement.RELATED).addComponent(jScrollPane2, javax.swing.GroupLayout.DEFAULT_SIZE, 343, Short.MAX_VALUE).addContainerGap()));

		jTabbedPane1.addTab("菜单设置", jPanel2);

		javax.swing.GroupLayout layout = new javax.swing.GroupLayout(getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addComponent(jTabbedPane1));
		layout.setVerticalGroup(layout.createParallelGroup(javax.swing.GroupLayout.Alignment.LEADING).addComponent(jTabbedPane1));

		pack();
	}// </editor-fold>//GEN-END:initComponents

	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton7ActionPerformed
		// TODO add your handling code here:
		WindowUtils.moveupTableRow(this.operatorConfigTable);
	}// GEN-LAST:event_jButton7ActionPerformed

	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton8ActionPerformed
		// TODO add your handling code here:
		WindowUtils.movedownTableRow(this.operatorConfigTable);
	}// GEN-LAST:event_jButton8ActionPerformed

	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton1ActionPerformed
		// TODO add your handling code here:
		DefaultTableModel model = (DefaultTableModel) operatorConfigTable.getModel();
		int[] selectRows = operatorConfigTable.getSelectedRows();
		if (selectRows.length == 0) {
			WindowUtils.showMessage("请选择需要删除的记录");
			return;
		}
		if (JOptionPane.showConfirmDialog(this, "是否要删除数据！", "提示", JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION) {
			operatorConfigTable.removeEditor();
			model = (DefaultTableModel) operatorConfigTable.getModel();
			for (int i = selectRows.length - 1; i >= 0; i--) {
				String statecode = String.valueOf(model.getValueAt(selectRows[i], 1));
				DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where statecode='" + statecode + "'");
				model.removeRow(selectRows[i]);
			}
		}
	}// GEN-LAST:event_jButton1ActionPerformed

	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton2ActionPerformed
		if (this.operatorConfigTable.getRowCount() == 1) {
			((DefaultTableModel) operatorConfigTable.getModel()).addRow(new Object[] { "1", "1" });
		} else {
			DefaultTableModel model = (DefaultTableModel) operatorConfigTable.getModel();
			int statecode = Integer.parseInt(String.valueOf((model.getValueAt(model.getRowCount() - 1, 1))));
			((DefaultTableModel) operatorConfigTable.getModel()).addRow(new Object[] { "1", String.valueOf(statecode + 1) });
		}
	}// GEN-LAST:event_jButton2ActionPerformed

	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton3ActionPerformed
		// TODO add your handling code here:
		String isnew, statecode, statename, statevalue, unitcode;
		for (int i = 0; i < this.operatorConfigTable.getRowCount(); i++) {
			if (operatorConfigTable.getValueAt(i, 1) == null) {
				WindowUtils.showMessage("第[" + (i + 1) + "]行[操作编码]必须填写");
				return;
			} else if (operatorConfigTable.getValueAt(i, 2) == null) {
				WindowUtils.showMessage("第[" + (i + 1) + "]行[操作名称]必须填写");
				return;
			} else if (operatorConfigTable.getValueAt(i, 3) == null || !String.valueOf(operatorConfigTable.getValueAt(i, 3)).matches("^[-+]?(([0-9]+)([.]([0-9]+))?|([.]([0-9]+))?)$")) {
				WindowUtils.showMessage("第[" + (i + 1) + "]行[对应状态值]必须填写为有效数字");
				return;
			}
			isnew = String.valueOf(operatorConfigTable.getValueAt(i, 0));
			statecode = String.valueOf(operatorConfigTable.getValueAt(i, 1));
			statename = String.valueOf(operatorConfigTable.getValueAt(i, 2));
			statevalue = String.valueOf(operatorConfigTable.getValueAt(i, 3) == null ? "0" : operatorConfigTable.getValueAt(i, 3));
			unitcode = String.valueOf(operatorConfigTable.getValueAt(i, 4) == null ? "" : operatorConfigTable.getValueAt(i, 4));

			if ("1".equals(isnew)) {
				DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_devicestateinfo(statecode,statename,statevalue,opcode) values('" + statecode + "','" + statename + "'," + statevalue + ",'" + unitcode + "')");
				operatorConfigTable.setValueAt("0", i, 0);
			} else if ("0".equals(isnew)) {
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestateinfo set statename='" + statename + "', statevalue=" + statevalue + ", opcode='" + unitcode + "' where statecode='" + statecode + "'");
			}
		}
	}// GEN-LAST:event_jButton3ActionPerformed

	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton4ActionPerformed
		// TODO add your handling code here:
		if (this.jComboBox1.getSelectedIndex() == 0) {
			WindowUtils.showMessage(this, "未选中设备类型、不能进行保存");
		}
		for (int i = 0; i < this.menuTable.getRowCount(); i++) {
			String isnew, devicetypeid, statecode, parentcode = "0", stateorder, unitcode;
			isnew = String.valueOf(menuTable.getValueAt(i, 0));
			if (isnew.equals("0")) {
				continue;
			}
			devicetypeid = ((CodeNameModel) jComboBox1.getSelectedItem()).getCode();
			statecode = ((CodeNameModel) menuTable.getValueAt(i, 1)).getCode();
			stateorder = String.valueOf(menuTable.getValueAt(i, 2) == null ? "0" : menuTable.getValueAt(i, 2));
			parentcode = String.valueOf(menuTable.getValueAt(i, 3) == null ? "-1" : menuTable.getValueAt(i, 3));
			unitcode = String.valueOf(menuTable.getValueAt(i, 4) == null ? "" : menuTable.getValueAt(i, 4));

			if ("1".equals(isnew)) {
				DBManager.execute("insert into "+CBSystemConstants.opcardUser+"t_a_devicestatevalue(devicetypeid,statecode,parentcode,stateorder,opcode) values('" + devicetypeid + "','" + statecode + "','" + parentcode + "'," + stateorder + ",'" + unitcode + "')");
				menuTable.setValueAt("0", i, 0);
			} else if ("2".equals(isnew)) {
				DBManager.execute("update "+CBSystemConstants.opcardUser+"t_a_devicestatevalue set   parentcode='" + parentcode + "' ,stateorder='" + stateorder + "' ,opcode= '" + unitcode + "'where DEVICETYPEID= '" + devicetypeid + "'" + " and statecode='" + statecode + "'");
			}
		}
	}// GEN-LAST:event_jButton4ActionPerformed

	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton5ActionPerformed
		// TODO add your handling code here:
		if (this.jComboBox1.getSelectedIndex() == 0)
			return;
		if (this.menuTable.getRowCount() == 0) {
			((DefaultTableModel) menuTable.getModel()).addRow(new Object[] { "1", "", "1" });
		} else {
			DefaultTableModel model = (DefaultTableModel) menuTable.getModel();
			int stateorder = Integer.parseInt(String.valueOf((model.getValueAt(model.getRowCount() - 1, 2))));
			((DefaultTableModel) menuTable.getModel()).addRow(new Object[] { "1", "", stateorder + 1 });
		}
	}// GEN-LAST:event_jButton5ActionPerformed

	/**
	 * 上移事件监听
	 * */
	private void jButton9ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton9ActionPerformed
		int row=menuTable.getSelectedRow();
		if(row==-1)
			return;
		
		if(row!=0){
			BigDecimal order=((BigDecimal)menuTable.getValueAt(row, 2));
			menuTable.setValueAt(order, row-1, 2);
			order=order.subtract(new BigDecimal(1));
			menuTable.setValueAt(order, row, 2);
			String isnew1 = String.valueOf(menuTable.getValueAt(row, 0));
		    String isnew2 = String.valueOf(menuTable.getValueAt(row-1, 0));
		}
		
	    
		WindowUtils.moveupTableRow(this.menuTable);
	}// GEN-LAST:event_jButton9ActionPerformed
    /**
     * 下移事件
     * */
	private void jButton10ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton10ActionPerformed
		int row=menuTable.getSelectedRow();
		if(row==-1)
			return;
		
		int rowCount=menuTable.getRowCount();
		if(row<rowCount-1){
			BigDecimal order=((BigDecimal)menuTable.getValueAt(row, 2));
			menuTable.setValueAt(order, row+1, 2);
			order=order.add(new BigDecimal(1));
			menuTable.setValueAt(order, row, 2);
			String isnew1 = String.valueOf(menuTable.getValueAt(row, 0));
		    String isnew2 = String.valueOf(menuTable.getValueAt(row+1, 0));
		    
		}
		WindowUtils.movedownTableRow(this.menuTable);
	}// GEN-LAST:event_jButton10ActionPerformed

	private void jButton6ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jButton6ActionPerformed
		// TODO add your handling code here:
		DefaultTableModel model = (DefaultTableModel) menuTable.getModel();
		int[] selectRows = menuTable.getSelectedRows();
		if (selectRows.length == 0) {
			WindowUtils.showMessage("请选择需要删除的记录");
			return;
		}
		if (JOptionPane.showConfirmDialog(this, "是否要删除数据！", "提示", JOptionPane.YES_NO_OPTION) == JOptionPane.YES_OPTION) {
			menuTable.removeEditor();
			model = (DefaultTableModel) menuTable.getModel();
			for (int i = selectRows.length - 1; i >= 0; i--) {
				String devicetypeid = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
				String statecode = ((CodeNameModel) (model.getValueAt(selectRows[i], 1))).getCode();
				String stateorder = String.valueOf(model.getValueAt(selectRows[i], 2));
				String parentcode = String.valueOf(menuTable.getValueAt(selectRows[i], 3) == null ? "-1" : menuTable.getValueAt(selectRows[i], 3));
				String unitcode = String.valueOf(model.getValueAt(selectRows[i], 4) == null ? "" : model.getValueAt(selectRows[i], 4));

				int return1 = DBManager.update("delete from  "+CBSystemConstants.opcardUser+"t_a_devicestatevalue where devicetypeid=? and statecode=? and stateorder=? and unitcode=? and parentcode=?", new Object[] { devicetypeid, statecode, stateorder, unitcode, parentcode });
				model.removeRow(selectRows[i]);
			}
		}

	}// GEN-LAST:event_jButton6ActionPerformed

	private void jComboBox1AncestorAdded(javax.swing.event.AncestorEvent evt) {// GEN-FIRST:event_jComboBox1AncestorAdded
		// TODO add your handling code here:
	}// GEN-LAST:event_jComboBox1AncestorAdded

	static int count;

	private void jComboBox1ActionPerformed(java.awt.event.ActionEvent evt) {// GEN-FIRST:event_jComboBox1ActionPerformed
		// TODO add your handling code here:
		CodeNameModel cnm = (CodeNameModel) this.jComboBox1.getSelectedItem();

		List results = DBManager.query("select t.statecode,(select statename from  "+CBSystemConstants.opcardUser+"t_a_devicestateinfo b where b.statecode=t.statecode) as statename ,t.stateorder,t.opcode ,t.parentcode from  "+CBSystemConstants.opcardUser+"t_a_devicestatevalue t  where t.islock = '0' and devicetypeid='" + cnm.getCode() + "' order by t.stateorder asc");
		Map temp = null;
		count = results.size();
		DefaultTableModel dtm = new DefaultTableModel(null, new String[] { "是否新增的", "设备操作状态", "排序", "父菜单", "机构编码" }) {
			@Override
			public boolean isCellEditable(int rowIndex, int columnIndex) {

				if (rowIndex > count - 1)
					return true;
				else

				if (columnIndex == 2 || columnIndex == 3) {
					return true;
				} else
					return false;
			}

		};
		this.menuTable.setModel(dtm);

		dtm = (DefaultTableModel) this.menuTable.getModel();
		for (int i = dtm.getRowCount() - 1; i >= 0; i--) {
			dtm.removeRow(i);
		}
		// this.menuTable.setCellEditor(new MyComboBoxEditor() );
		TableColumn tc = this.menuTable.getColumnModel().getColumn(1);
		tc.setCellEditor(new MyComboBoxEditor());

		for (int i = 0; i < results.size(); i++) {
			temp = (Map) results.get(i);
			Object[] obj = new Object[] { "0", new CodeNameModel(String.valueOf(temp.get("statecode")), String.valueOf(temp.get("statename"))), temp.get("stateorder"), temp.get("parentcode"), temp.get("unitcode") };
			dtm.addRow(obj);
		}
		menuTable.getTableHeader().setReorderingAllowed(false); // 不可整列移动
		menuTable.getTableHeader().setResizingAllowed(false); // 不可拉动表
		tc = menuTable.getColumnModel().getColumn(0);
		tc.setMaxWidth(0);
		tc.setPreferredWidth(0);
		tc.setWidth(0);
		tc.setMinWidth(0);
		menuTable.getTableHeader().getColumnModel().getColumn(0).setMaxWidth(0);
		menuTable.getTableHeader().getColumnModel().getColumn(0).setMinWidth(0);
		menuTable.getModel().addTableModelListener(new TableModelListener() {
			public void tableChanged(TableModelEvent e) {
				if (e.getType() == e.UPDATE) {
					int row = e.getFirstRow();
					String isnew = (String) menuTable.getValueAt(row, 0);
					if (isnew.equals("0")) {
						menuTable.setValueAt("2", row, 0);
					}
				}
			}
		});

	}// GEN-LAST:event_jComboBox1ActionPerformed

	/**
	 * @param args
	 *            the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				MenuConfig dialog = new MenuConfig(new javax.swing.JFrame());
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	// Variables declaration - do not modify//GEN-BEGIN:variables
	private javax.swing.JButton jButton1;
	private javax.swing.JButton jButton10;
	private javax.swing.JButton jButton2;
	private javax.swing.JButton jButton3;
	private javax.swing.JButton jButton4;
	private javax.swing.JButton jButton5;
	private javax.swing.JButton jButton6;
	private javax.swing.JButton jButton7;
	private javax.swing.JButton jButton8;
	private javax.swing.JButton jButton9;
	private javax.swing.JComboBox jComboBox1;
	private javax.swing.JLabel jLabel1;
	private javax.swing.JPanel jPanel1;
	private javax.swing.JPanel jPanel2;
	private javax.swing.JPanel jPanel3;
	private javax.swing.JScrollPane jScrollPane1;
	private javax.swing.JScrollPane jScrollPane2;
	private javax.swing.JTabbedPane jTabbedPane1;
	private javax.swing.JTable menuTable;
	private javax.swing.JTable operatorConfigTable;
	// End of variables declaration//GEN-END:variables

}

class MyComboBoxEditor extends AbstractCellEditor implements TableCellEditor {

	private JComboBox cb = new JComboBox();

	public MyComboBoxEditor() {
		List results = DBManager.queryForList("select t.statecode,t.statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo t where t.islock = '0' order by t.statecode asc");
		Collections.sort(results, new Comparator<ListOrderedMap>() {
			public int compare(ListOrderedMap o1, ListOrderedMap o2) {
				int sc1 = Integer.parseInt((String) o1.get("statecode"));
				int sc2 = Integer.parseInt((String) o2.get("statecode"));
				if (sc2 > sc1)
					return 0;
				else
					return 1;
			}
		});
		for (int i = 0; i < results.size(); i++) {
			Map map = (Map) results.get(i);
			cb.addItem(new CodeNameModel(String.valueOf(map.get("statecode")), String.valueOf(map.get("statename"))));

		}
	}

	public Object getCellEditorValue() {
		return cb.getSelectedItem();
	}

	public Component getTableCellEditorComponent(JTable arg0, Object arg1, boolean arg2, int arg3, int arg4) {
		if (!"java.lang.String".equals(arg1.getClass().getName())) {

			CodeNameModel selectCnm = (CodeNameModel) arg1;
			for (int i = 0; i < cb.getItemCount(); i++) {
				CodeNameModel cnm = (CodeNameModel) cb.getItemAt(i);
				if (cnm.getCode().equals(selectCnm.getCode())) {
					cb.setSelectedIndex(i);
					break;
				}
			}
		}
		return cb;

	}

}

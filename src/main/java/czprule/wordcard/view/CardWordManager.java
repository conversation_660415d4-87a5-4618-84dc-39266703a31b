/*
 * AddRuleDialog_.java
 *
 * Created on __DATE__, __TIME__
 */

package czprule.wordcard.view;

import java.awt.Toolkit;

import javax.swing.JOptionPane;
import javax.swing.table.DefaultTableModel;

import czprule.model.CodeNameModel;
import czprule.rule.dao.RuleManagerDao;
import czprule.system.ShowMessage;

/**  
 *    
 * <AUTHOR>
 */
public class CardWordManager extends javax.swing.JDialog {

	/** Creates new form AddRuleDialog_ */
	public CardWordManager(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		initTable();
		setLocationCenter();

	}

	/**
	 * 初始化表格
	 */
	private void initTable() {

		DefaultTableModel jtableModel1 = new DefaultTableModel(null,
				new String[] { "序号", "变量", "class类" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex > 1)
					return true;
				else
					return false;
			}
		};
		DefaultTableModel jtableModel2 = new DefaultTableModel(null,
				new String[] { "序号", "变量", "class类" }) {
			public boolean isCellEditable(int rowIndex, int columnIndex) {
				if (columnIndex > 1)
					return true;
				else
					return false;
			}
		};
		jTable3.setModel(jtableModel1);
		jTable3.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable3.getColumnModel().getColumn(2).setMinWidth(400);

		jTable4.setModel(jtableModel2);
		jTable4.getColumnModel().getColumn(0).setMaxWidth(40);
		jTable4.getColumnModel().getColumn(2).setMinWidth(400);
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}

	//GEN-BEGIN:initComponents
	// <editor-fold defaultstate="collapsed" desc="Generated Code">
	private void initComponents() {

		jPanel1 = new javax.swing.JPanel();
		jTabbedPane1 = new javax.swing.JTabbedPane();
		jPanel4 = new javax.swing.JPanel();
		jScrollPane3 = new javax.swing.JScrollPane();
		jTable3 = new javax.swing.JTable();
		jButton4 = new javax.swing.JButton();
		jButton5 = new javax.swing.JButton();
		jPanel5 = new javax.swing.JPanel();
		jScrollPane4 = new javax.swing.JScrollPane();
		jTable4 = new javax.swing.JTable();
		jButton7 = new javax.swing.JButton();
		jButton8 = new javax.swing.JButton();

		setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

		jScrollPane3.setViewportView(jTable3);

		jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton4.setToolTipText("\u65b0\u589e");
		jButton4.setBorder(null);
		jButton4.setBorderPainted(false);
		jButton4.setFocusPainted(false);
		jButton4.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton4ActionPerformed(evt);
			}
		});

		jButton5.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton5.setToolTipText("\u5220\u9664");
		jButton5.setBorder(null);
		jButton5.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton5ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout jPanel4Layout = new org.jdesktop.layout.GroupLayout(
				jPanel4);
		jPanel4.setLayout(jPanel4Layout);
		jPanel4Layout
				.setHorizontalGroup(jPanel4Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(org.jdesktop.layout.GroupLayout.TRAILING,
								jPanel4Layout
										.createSequentialGroup()
										.addContainerGap(605, Short.MAX_VALUE)
										.add(jButton4)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton5).addContainerGap())
						.add(jScrollPane3,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								657, Short.MAX_VALUE));
		jPanel4Layout
				.setVerticalGroup(jPanel4Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jPanel4Layout
								.createSequentialGroup()
								.addContainerGap()
								.add(jPanel4Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jButton5).add(jButton4))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jScrollPane3,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										386, Short.MAX_VALUE)));

		jTabbedPane1.addTab("\u81ea\u5b9a\u4e49\u53d8\u91cf", jPanel4);

		jScrollPane4.setViewportView(jTable4);

		jButton7.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/add.png"))); // NOI18N
		jButton7.setToolTipText("\u65b0\u589e");
		jButton7.setBorder(null);
		jButton7.setFocusPainted(false);
		jButton7.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton7ActionPerformed(evt);
			}
		});

		jButton8.setIcon(new javax.swing.ImageIcon(getClass().getResource(
				"/tellhow/btnIcon/delete.png"))); // NOI18N
		jButton8.setToolTipText("\u5220\u9664");
		jButton8.setBorder(null);
		jButton8.setFocusPainted(false);
		jButton8.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton8ActionPerformed(evt);
			}
		});

		org.jdesktop.layout.GroupLayout jPanel5Layout = new org.jdesktop.layout.GroupLayout(
				jPanel5);
		jPanel5.setLayout(jPanel5Layout);
		jPanel5Layout
				.setHorizontalGroup(jPanel5Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(org.jdesktop.layout.GroupLayout.TRAILING,
								jPanel5Layout
										.createSequentialGroup()
										.addContainerGap(606, Short.MAX_VALUE)
										.add(jButton7)
										.addPreferredGap(
												org.jdesktop.layout.LayoutStyle.RELATED)
										.add(jButton8).addContainerGap())
						.add(jScrollPane4,
								org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
								657, Short.MAX_VALUE));
		jPanel5Layout
				.setVerticalGroup(jPanel5Layout
						.createParallelGroup(
								org.jdesktop.layout.GroupLayout.LEADING)
						.add(jPanel5Layout
								.createSequentialGroup()
								.addContainerGap()
								.add(jPanel5Layout
										.createParallelGroup(
												org.jdesktop.layout.GroupLayout.BASELINE)
										.add(jButton8).add(jButton7))
								.addPreferredGap(
										org.jdesktop.layout.LayoutStyle.RELATED)
								.add(jScrollPane4,
										org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
										387, Short.MAX_VALUE)));

		jTabbedPane1.addTab("\u81ea\u5b9a\u4e49\u8868\u8fbe\u5f0f", jPanel5);

		org.jdesktop.layout.GroupLayout jPanel1Layout = new org.jdesktop.layout.GroupLayout(
				jPanel1);
		jPanel1.setLayout(jPanel1Layout);
		jPanel1Layout.setHorizontalGroup(jPanel1Layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jTabbedPane1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 662,
				Short.MAX_VALUE));
		jPanel1Layout.setVerticalGroup(jPanel1Layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(
				org.jdesktop.layout.GroupLayout.TRAILING, jTabbedPane1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, 456,
				Short.MAX_VALUE));

		org.jdesktop.layout.GroupLayout layout = new org.jdesktop.layout.GroupLayout(
				getContentPane());
		getContentPane().setLayout(layout);
		layout.setHorizontalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));
		layout.setVerticalGroup(layout.createParallelGroup(
				org.jdesktop.layout.GroupLayout.LEADING).add(jPanel1,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE,
				org.jdesktop.layout.GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE));

		pack();
	}// </editor-fold>
		//GEN-END:initComponents

	/**
	 * 删除自定义表达式
	 * @param evt
	 */
	private void jButton8ActionPerformed(java.awt.event.ActionEvent evt) {
		int isOk = JOptionPane.showConfirmDialog(this, "是否确定删除记录！", "提示",
				JOptionPane.OK_CANCEL_OPTION);
		if (isOk == JOptionPane.CANCEL_OPTION) {
			return;
		}
		int rows[] = jTable4.getSelectedRows();
		if (rows.length <= 0) {
			ShowMessage.view("请选择您要删除的记录！");
			return;
		}
		RuleManagerDao dmd = new RuleManagerDao();
		for (int i = 0; i < rows.length; i++) {
			// 删除数据库记录
			CodeNameModel cnm = (CodeNameModel) jTable4.getModel().getValueAt(
					rows[i], 1);
			dmd.delWordBean(cnm.getCode());
		}
		this.initTable();
		ShowMessage.view("删除成功！");
	}

	/**
	 * 删除自定义变量
	 * @param evt
	 */
	private void jButton5ActionPerformed(java.awt.event.ActionEvent evt) {
		int isOk = JOptionPane.showConfirmDialog(this, "是否确定删除记录！", "提示",
				JOptionPane.OK_CANCEL_OPTION);
		if (isOk == JOptionPane.CANCEL_OPTION) {
			return;
		}
		int rows[] = jTable3.getSelectedRows();
		if (rows.length <= 0) {
			ShowMessage.view("请选择您要删除的记录！");
			return;
		}
		RuleManagerDao dmd = new RuleManagerDao();
		for (int i = 0; i < rows.length; i++) {
			// 删除数据库记录
			CodeNameModel cnm = (CodeNameModel) jTable3.getModel().getValueAt(
					rows[i], 1);
			dmd.delWordBean(cnm.getCode());
		}
		this.initTable();
		ShowMessage.view("删除成功！");
	}

	/**
	 * 增加自定义表达式
	 * @param evt
	 */
	private void jButton7ActionPerformed(java.awt.event.ActionEvent evt) {
		CardWordItem cwi = new CardWordItem(this, true, "1");
		cwi.setVisible(true);
		this.initTable();
	}

	/**
	 * 增加自定义变量
	 * @param evt
	 */
	private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		CardWordItem cwi = new CardWordItem(this, true, "0");
		cwi.setVisible(true);
		this.initTable();
	}

	/**
	 * @param args
	 *            the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				CardWordManager dialog = new CardWordManager(
						new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

	//GEN-BEGIN:variables
	// Variables declaration - do not modify
	private javax.swing.JButton jButton4;
	private javax.swing.JButton jButton5;
	private javax.swing.JButton jButton7;
	private javax.swing.JButton jButton8;
	private javax.swing.JPanel jPanel1;
	private javax.swing.JPanel jPanel4;
	private javax.swing.JPanel jPanel5;
	private javax.swing.JScrollPane jScrollPane3;
	private javax.swing.JScrollPane jScrollPane4;
	private javax.swing.JTabbedPane jTabbedPane1;
	private javax.swing.JTable jTable3;
	private javax.swing.JTable jTable4;
	// End of variables declaration//GEN-END:variables

}

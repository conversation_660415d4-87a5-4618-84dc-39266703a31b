package czprule.wordcard.view;

import java.awt.BorderLayout;
import java.awt.Toolkit;
import java.awt.event.ActionEvent;

import javax.swing.ComboBoxModel;
import javax.swing.DefaultComboBoxModel;
import javax.swing.JButton;
import javax.swing.JComboBox;
import javax.swing.JDialog;
import javax.swing.JLabel;
import javax.swing.JPanel;

import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.NodeList;

import com.tellhow.graphicframework.utils.DOMUtil;

import czprule.system.CBSystemConstants;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-12-3 下午04:48:19 
 */
public class CardDefDialog extends JDialog {

	private JPanel panel1;
	private JPanel panel2;
	private JPanel panel3;
	private JLabel label;
	private J<PERSON>omboBox comboBox;
	private JButton button1;
	private JButton button2;
	
	public CardDefDialog (java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		initComponents();
		this.setLocationCenter();
	}
	
	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setLocation((w - this.getSize().width) / 2,
				(h - this.getSize().height) / 2);
	}
	
	private void initComponents() {
		this.setSize(300, 150);
		label = new JLabel("当前使用模板：");
		comboBox = new JComboBox();
		comboBox.setSize(200, 30);
		ComboBoxModel model = new DefaultComboBoxModel(new String[] {"状态令模板", "元件令模板"});
		comboBox.setModel(model);
		comboBox.setSelectedIndex(Integer.valueOf(CBSystemConstants.cardtype));
		button1 = new JButton("保存");
		button2 = new JButton("关闭");
		panel1 = new JPanel(new BorderLayout());
		panel2 = new JPanel();
		panel3 = new JPanel();
		panel2.add(label);
		panel2.add(comboBox);
		panel3.add(button1);
		panel3.add(button2);
		panel1.add(panel2, BorderLayout.CENTER);
		panel1.add(panel3, BorderLayout.SOUTH);
		this.setTitle("操作票模板定义");
		this.setContentPane(panel1);
		this.setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);
		
		button1.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton1ActionPerformed(evt);
			}
		});
		button2.addActionListener(new java.awt.event.ActionListener() {
			public void actionPerformed(java.awt.event.ActionEvent evt) {
				jButton2ActionPerformed(evt);
			}
		});
	}
	
	private void jButton1ActionPerformed(ActionEvent evt) {
		CBSystemConstants.cardtype = String.valueOf(comboBox.getSelectedIndex());
		Document doc = DOMUtil.readXMLFile(CBSystemConstants.SYS_CONFIG_XML_FILE);
		Element rootE = doc.getDocumentElement();
        NodeList childEs = rootE.getChildNodes();
        Element childE = null;
        for (int i = 0; i < childEs.getLength(); i++) {
        	if(childEs.item(i).getNodeName().equals("#text"))
        		continue;
        	childE = (Element) childEs.item(i);
        	if (childE.getAttribute("name").toUpperCase().equals("PROJECTPARAM")) { 
        		Element codeElem = null;
                String codeKey = "";
                String codeValue="";
        		NodeList allVlaues = childE.getElementsByTagName("value");
                for (int j = 0; j < allVlaues.getLength(); j++) {
                    codeElem = (Element) allVlaues.item(j);
                    codeKey = codeElem.getAttribute("key").trim();
                    codeValue=codeElem.getTextContent().trim();
                    if("cardtype".equals(codeKey)){
                    	codeElem.setTextContent(CBSystemConstants.cardtype);
                    	DOMUtil.writeXMLFile(doc, CBSystemConstants.SYS_CONFIG_XML_FILE);
                    	break;
                    }
                }
                break;
        	}
        }
		this.dispose();
	}
	
	private void jButton2ActionPerformed(ActionEvent evt) {
		this.dispose();
	}
	
	
	public static void main(String args[]) {
		JDialog a = new CardDefDialog(null, false);
		a.setVisible(true);
	}
}

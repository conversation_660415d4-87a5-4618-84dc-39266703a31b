/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 初始化设备类型，设备操作动作下拉框
 * 作    者 : 张余平
 * 开发日期 : 2010-10-13
 * 修改日期 ：
 * 修改说明 ： 
 * 修 改 人 ：
 **/
package czprule.wordcard.view;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Comparator;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import javax.swing.DefaultComboBoxModel;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.user.User;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CodeNameModel;
import czprule.model.DictionarysModel;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;
import czprule.wordcard.dao.DeviceStateMentManager;

/**   
 * 
 * <AUTHOR>
 *
 */
public class InitDeviceTypeChockBox {
	
	/**
	 * 初始化设备类型下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDevTypeCheckBox(){
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=dsmm.getDeviceType();
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			model.addElement(cnm);
		}
		return model;
	}
	
	/**
	 * 设备安装类型下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getEquipRunTypeComboBox(String equipType){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=dsmm.getDeviceType();
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			model.addElement(cnm);
		}
		return model;
	}
	
	/**
	 * 初始化操作类型下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getOperationType(){
		
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		CodeNameModel cnm = new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		model.addElement(new CodeNameModel("0","综令票"));
		model.addElement(new CodeNameModel("1","逐项票"));
		model.addElement(new CodeNameModel("2","新投票"));
		return model;
	}
	
	/**
	 * 设备状态初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDeviceStatusCheckBox(String deviceType){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=dsmm.getDeviceStatus(deviceType);
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		cnm=new CodeNameModel("-1","全部");
		model.addElement(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			model.addElement(cnm);
		}
		return model;
	}
	
	/**
	 * 设备状态初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDeviceStatusCheckBox(String deviceType, List<String> statusList, String defaultStatus){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=dsmm.getDeviceStatus(deviceType);
		CodeNameModel cnm=null;
		//cnm=new CodeNameModel("","请选择");
		//model.addElement(cnm);
		//model.setSelectedItem(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			if(!statusList.contains(cnm.getCode()))
				model.addElement(cnm);
			if(defaultStatus.equals(cnm.getCode()))
				model.setSelectedItem(cnm);
		}
		return model;
	}
	/**
	 * 设备状态初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getMXCheckBox(List<String> statusList, String defaultStatus){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=new ArrayList<CodeNameModel>();
		CodeNameModel cnm=null;
		int j=0;
		for(String str:statusList ){
			cnm=new CodeNameModel(j+"",str);
			j++;
			devTypes.add(cnm);
		}

		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			if(!statusList.contains(cnm.getCode()))
				model.addElement(cnm);
			if(defaultStatus.equals(cnm.getCode()))
				model.setSelectedItem(cnm);
		}
		return model;
	}
	
	/**
	 * 设备分位合位初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDeviceFHCheckBox(String deviceType, List<String> statusList, String defaultStatus){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		List<CodeNameModel> devTypes=new ArrayList<CodeNameModel>();
		
		CodeNameModel cnm=null;

		cnm=new CodeNameModel("0","合位");
		devTypes.add(cnm);
		cnm=new CodeNameModel("1","分位");
		devTypes.add(cnm);
		
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			if(!statusList.contains(cnm.getCode()))
				model.addElement(cnm);
			if(defaultStatus.equals(cnm.getCode()))
				model.setSelectedItem(cnm);
		}
		return model;
	}
	
	/**
	 * 接地选择下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getLineGoundCheckBox(List<String> statusList, String defaultStatus){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		List<CodeNameModel> devTypes=new ArrayList<CodeNameModel>();
		
		CodeNameModel cnm=null;
		if(CBSystemConstants.getCurRBM().getBeginStatus().equals("3")) {
			cnm=new CodeNameModel("0","拉地刀");
			devTypes.add(cnm);
			cnm=new CodeNameModel("1","拆地线");
			devTypes.add(cnm);
		}
		else {
			cnm=new CodeNameModel("0","合地刀");
			devTypes.add(cnm);
			cnm=new CodeNameModel("1","挂地线");
			devTypes.add(cnm);
		}
		//model.addElement(cnm);
		//model.setSelectedItem(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			if(!statusList.contains(cnm.getCode()))
				model.addElement(cnm);
			if(defaultStatus.equals(cnm.getCode()))
				model.setSelectedItem(cnm);
		}
		return model;
	}
	/**
	 * 设备右键操作初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDeviceStateCheckBox(String deviceType, String cardBuildType){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devTypes=dsmm.getDeviceState(deviceType, cardBuildType);
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devTypes.size();i++){
			cnm=devTypes.get(i);
			model.addElement(cnm);
		}
		return model;
	}
	/**
	 * 电压等级初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getDeviceVoltCheckBox(){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		List<CodeNameModel> devVolts=dsmm.getDeviceVolts();
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devVolts.size();i++){
			cnm=devVolts.get(i);
			model.addElement(cnm);
		}
		return model;
	}
	
	/**
	 * 
	 * @return
	 */
	public static DefaultComboBoxModel getDictionaryCheckBox(String codeType){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		List<DictionarysModel> lineRunModeList = CBSystemConstants.getDictionary(codeType);
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<lineRunModeList.size();i++){
			cnm = new CodeNameModel(lineRunModeList.get(i).getCode(),lineRunModeList.get(i).getName());
			model.addElement(cnm);
		}
		return model;
	}
	/**
	 * 
	 * @return
	 */
	public static DefaultComboBoxModel getDictionaryComboBox(String codeType){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		List<DictionarysModel> lineRunModeList = CBSystemConstants.getDictionary(codeType);
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<lineRunModeList.size();i++){
			cnm = new CodeNameModel(lineRunModeList.get(i).getCode(),lineRunModeList.get(i).getName());
			model.addElement(cnm);
		}
		return model;
	}
	/**
	 * 业务角色初始化下拉框
	 */
	public static DefaultComboBoxModel getRoleCheckBox(){
		User user = CBSystemConstants.getUser();
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		String usersql = "select rolecode from "+CBSystemConstants.opcardUser+"t_a_userrole t where t.userid='"+user.getUserID()+"' order by rolecode";
		List urlist = DBManager.query(usersql);
		String rosql = "";
		Map map = new HashMap();
		CodeNameModel cnm=null;
		if(urlist.size()>0){
			for(int i=0;i<urlist.size();i++){
				map = (Map)urlist.get(i);
				String role =String.valueOf(map.get("rolecode"));
				 if(role.equals("0")){
					 cnm = new CodeNameModel(role, "调度");
					 
				 }else if(role.equals("1")){
					 cnm = new CodeNameModel(role, "配网");
				 }else if(role.equals("2"))
				 {
					 cnm = new CodeNameModel(role, "监控");
				 }else{
					 continue;
				 }
				 model.addElement(cnm);
				 if(i==0){
					 model.setSelectedItem(cnm); 
				 }
				
			}
		}else{
			 cnm = new CodeNameModel("0", "无角色");
			 model.setSelectedItem(cnm); 
		}
		
		
		return model;
	}
	
	/**
	 * 设备初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getStationComboBox(){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
	    CodeNameModel cnm=new CodeNameModel("", "");
	    model.addElement(cnm);
		for(Iterator it = CBSystemConstants.getMapPowerStation().values().iterator();it.hasNext();) {
			PowerDevice station = (PowerDevice)it.next();
			CodeNameModel codeNameModel=new CodeNameModel(station.getPowerDeviceID(),CZPService.getService().getDevName(station));
			model.addElement(codeNameModel);
		}
		return model;
	}
	
	/**
	 * 设备初始化下拉框
	 * @return
	 */
	public static DefaultComboBoxModel getEquipComboBox(String stationID, String equipType, String runType, String vol, String pm){
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		List<String> volList = null;
		if(!vol.equals("")) {
			String volarr[] = vol.split(",");
			volList = Arrays.asList(volarr);
		}
		if(stationID.equals("")) {
			if(equipType.equals(SystemConstants.InOutLine)) {
				for(Iterator it = CBSystemConstants.getMapPowerLine().values().iterator();it.hasNext();){
					PowerDevice line = (PowerDevice)it.next();
					if(volList != null && !volList.contains(String.valueOf((int)line.getPowerVoltGrade())))
						continue;
					String lineName = "";
					if(pm.equals("EDN"))
						lineName = CZPService.getService().getDevName(line);
					else if(pm.equals("ENO"))
						lineName = CZPService.getService().getDevNum(line);
					cnm=new CodeNameModel(lineName,lineName);
					model.addElement(cnm);
				}
			}
		}
		else {
			ArrayList<String> equipNameList = new ArrayList<String>();
			CreatePowerStationToplogy.loadFacData(stationID);
			for(Iterator it = CBSystemConstants.getMapPowerStationDevice().get(stationID).values().iterator();it.hasNext();){
				PowerDevice dev = (PowerDevice)it.next();
				if(volList != null && !volList.contains(String.valueOf((int)dev.getPowerVoltGrade())))
					continue;
				if(dev.getDeviceType().equals(equipType)) {
					if(dev.getDeviceType().equals(SystemConstants.MotherLine) && dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
						continue;
					if(!runType.equals("") && !runType.equals(dev.getDeviceSetType()))
						continue;
					String lineName = "";
					if(pm.equals("EDN"))
						lineName = CZPService.getService().getDevName(dev);
					else if(pm.equals("ENO"))
						lineName = CZPService.getService().getDevNum(dev);
					equipNameList.add(lineName);
					
				}
			}
			
			Collections.sort(equipNameList);
			
			for(String equipName : equipNameList) {
				cnm=new CodeNameModel(equipName,equipName);
				model.addElement(cnm);
			}
		}
		
		
		
		
		return model;
	}
}

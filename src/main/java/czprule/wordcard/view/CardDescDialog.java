package czprule.wordcard.view;

import java.awt.Toolkit;
import java.awt.event.MouseAdapter;
import java.awt.event.MouseEvent;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.swing.DefaultComboBoxModel;
import javax.swing.GroupLayout;
import javax.swing.GroupLayout.Alignment;
import javax.swing.LayoutStyle.ComponentPlacement;
import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.utils.StringUtils;
import czprule.model.CodeNameModel;
import czprule.rule.view.WordCardTempStrDialog;
import czprule.system.DBManager;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

/**
*
* <AUTHOR>
*/
public class CardDescDialog extends javax.swing.JDialog {
	
	private static final long serialVersionUID = 1L;
	private WordCardTempStrDialog wctsDialog=new WordCardTempStrDialog(CardDescDialog.this,"0");
	String cardtype = "0"; //开票类型
	private String czrws="";//操作任务临时缓存
	private String czzls ="";//操作指令临时缓存
	private String czrws_2="";//czrws操作任务
	private String czzls_2 ="";//操作指令
	private String devtype ="";
	public CardDescDialog(java.awt.Frame parent, boolean modal) {
		super(parent, modal);
		setModalExclusionType(ModalExclusionType.APPLICATION_EXCLUDE);
		initComponents();
		this.setTitle("操作票模板维护");
		this.initData();
		this.setLocationCenter();
	}

	/**
	 * 屏幕中央位置
	 */
	public void setLocationCenter() {
		int w = (int) Toolkit.getDefaultToolkit().getScreenSize().getWidth();
		int h = (int) Toolkit.getDefaultToolkit().getScreenSize().getHeight();
		this.setBounds(100, 100, 820, 600);
		this.setLocation((w - this.getSize().width) / 2,(h - this.getSize().height) / 2);
	}

	/** This method is called from within the constructor to
	 * initialize the form.
	 * WARNING: Do NOT modify this code. The content of this method is
	 * always regenerated by the Form Editor.
	 */
   // <editor-fold defaultstate="collapsed" desc="Generated Code">
   private void initComponents() {

       buttonGroup1 = new javax.swing.ButtonGroup();
       buttonGroup2 = new javax.swing.ButtonGroup();
       jPanel1 = new javax.swing.JPanel();
       jComboBox1 = new javax.swing.JComboBox();
       jComboBox2 = new javax.swing.JComboBox();
       jComboBox3 = new javax.swing.JComboBox();
       jLabel1 = new javax.swing.JLabel();
       jLabel2 = new javax.swing.JLabel();
       jLabel3 = new javax.swing.JLabel();
       jButton1 = new javax.swing.JButton();
       jLabel4 = new javax.swing.JLabel();
       jRadioButton1 = new javax.swing.JRadioButton();
       jRadioButton2 = new javax.swing.JRadioButton();
       jRadioButton3 = new javax.swing.JRadioButton();
       jLabel5 = new javax.swing.JLabel();
       jLabel6 = new javax.swing.JLabel();
       jComboBox4 = new javax.swing.JComboBox();
       jScrollPane1 = new javax.swing.JScrollPane();
       jTextArea1 = new javax.swing.JTextArea();
	   jTextArea1.setLineWrap(true);
       jScrollPane2 = new javax.swing.JScrollPane();
       jEditorPane1 = new javax.swing.JEditorPane();
       jRadioButton4 = new javax.swing.JRadioButton();
       jButton2 = new javax.swing.JButton();
       jButton3 = new javax.swing.JButton();
       jButton4 = new javax.swing.JButton();
       jScrollPane3 = new javax.swing.JScrollPane();
       jTextArea2 = new javax.swing.JTextArea();
	   jTextArea2.setLineWrap(true);

       setDefaultCloseOperation(javax.swing.WindowConstants.DISPOSE_ON_CLOSE);

       jComboBox1.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
       jComboBox1.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jComboBox1ActionPerformed(evt);
           }
       });

       jComboBox2.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
       jComboBox2.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jComboBox2ActionPerformed(evt);
           }
       });

       jComboBox3.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
       jComboBox3.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jComboBox3ActionPerformed(evt);
           }
       });

       jLabel1.setText("设备类型选择");

       jLabel2.setText("起始状态");

       jLabel3.setText("操作动作");

       jButton1.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/save.png"))); // NOI18N
       jButton1.setText("保存");
       jButton1.setToolTipText("保存");
       jButton1.setMargin(new java.awt.Insets(1,1,1,1));
       //jButton1.setBorder(null);
       jButton1.setFocusPainted(false);
       jButton1.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jButton1ActionPerformed(evt);
           }
       });

       jLabel4.setText("命令票类型：");

       buttonGroup1.add(jRadioButton1);
       jRadioButton1.setSelected(true);
       jRadioButton1.setText("状态令");
       jRadioButton1.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jRadioButton1ActionPerformed(evt);
           }
       });

       buttonGroup1.add(jRadioButton2);
       jRadioButton2.setText("元件令");
       jRadioButton2.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jRadioButton2ActionPerformed(evt);
           }
       });

       buttonGroup2.add(jRadioButton3);
       jRadioButton3.setText("点图票");
       jRadioButton3.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jRadioButton3ActionPerformed(evt);
           }
       });

       jLabel5.setText("开票类型：");

       jLabel6.setText("电压等级");
       jLabel6.setVisible(false); //电压等级不再使用下拉框区分配置，在模板区分配置

       jComboBox4.setModel(new javax.swing.DefaultComboBoxModel(new String[] { "Item 1", "Item 2", "Item 3", "Item 4" }));
       this.jComboBox4.setVisible(false);
       
       jScrollPane1.setBorder(javax.swing.BorderFactory.createTitledBorder("操作任务"));
       jScrollPane1.setViewportView(jTextArea1);
       jTextArea1.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseClicked(MouseEvent e) {
				// TODO Auto-generated method stub
				wctsDialog.task=jTextArea1;
			}
		});

       jScrollPane2.setBorder(javax.swing.BorderFactory.createTitledBorder("操作内容"));
       jScrollPane2.setViewportView(jEditorPane1);
       jEditorPane1.addMouseListener(new MouseAdapter() {
			@Override
			public void mouseClicked(MouseEvent e) {
				// TODO Auto-generated method stub
				wctsDialog.task=jEditorPane1;
			}
		});

       buttonGroup2.add(jRadioButton4);
       jRadioButton4.setSelected(true);
       jRadioButton4.setText("正常票");
       jRadioButton4.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jRadioButton4ActionPerformed(evt);
           }
       });

       jButton2.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/replace.png"))); // NOI18N
       jButton2.setToolTipText("粘贴");
       jButton2.setText("粘贴");
       jButton2.setMargin(new java.awt.Insets(1,1,1,1));
       //jButton2.setBorder(null);
       jButton2.setFocusPainted(false);
       jButton2.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jButton2ActionPerformed(evt);
           }
       });

       jButton3.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/copy.gif"))); // NOI18N
       jButton3.setToolTipText("复制");
       jButton3.setText("复制");
       jButton3.setMargin(new java.awt.Insets(1,1,1,1));
       //jButton3.setBorder(null);
       jButton3.setFocusPainted(false);
       jButton3.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jButton3ActionPerformed(evt);
           }
       });

       jButton4.setIcon(new javax.swing.ImageIcon(getClass().getResource("/tellhow/btnIcon/import.gif"))); // NOI18N
       jButton4.setToolTipText("引用术语");
       jButton4.setText("引用术语");
       jButton4.setMargin(new java.awt.Insets(1,1,1,1));
       //jButton4.setBorder(null);
       jButton4.setFocusPainted(false);
       jButton4.addActionListener(new java.awt.event.ActionListener() {
           public void actionPerformed(java.awt.event.ActionEvent evt) {
               jButton4ActionPerformed(evt);
           }
       });

       jScrollPane3.setBorder(javax.swing.BorderFactory.createTitledBorder("备注"));
       jScrollPane3.setViewportView(jTextArea2);

       GroupLayout jPanel1Layout = new GroupLayout(jPanel1);
       jPanel1Layout.setHorizontalGroup(
       	jPanel1Layout.createParallelGroup(Alignment.LEADING)
       		.addGroup(jPanel1Layout.createSequentialGroup()
       			.addContainerGap()
       			.addGroup(jPanel1Layout.createParallelGroup(Alignment.LEADING)
       				.addGroup(jPanel1Layout.createSequentialGroup()
       					.addComponent(jLabel4)
       					.addGap(18)
       					.addComponent(jRadioButton1)
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addComponent(jRadioButton2)
       					.addGap(18)
       					.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 69, GroupLayout.PREFERRED_SIZE)
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addComponent(jRadioButton4)
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addComponent(jRadioButton3))
       				.addGroup(jPanel1Layout.createSequentialGroup()
       					.addGroup(jPanel1Layout.createParallelGroup(Alignment.LEADING, false)
       						.addComponent(jComboBox4, 0, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jLabel6, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jLabel1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jComboBox1, 0, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jLabel2, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jComboBox2, 0, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jLabel3, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       						.addComponent(jComboBox3, 0, 176, Short.MAX_VALUE))
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addGroup(jPanel1Layout.createParallelGroup(Alignment.TRAILING, false)
       						.addComponent(jScrollPane2, GroupLayout.DEFAULT_SIZE, 577, Short.MAX_VALUE)
       						.addGroup(jPanel1Layout.createSequentialGroup()
       							.addComponent(jButton4)
       							.addPreferredGap(ComponentPlacement.RELATED)
       							.addComponent(jButton3)
       							.addPreferredGap(ComponentPlacement.RELATED)
       							.addComponent(jButton2)
       							.addPreferredGap(ComponentPlacement.RELATED)
       							.addComponent(jButton1)
       							.addPreferredGap(ComponentPlacement.RELATED))
       						.addComponent(jScrollPane1, GroupLayout.DEFAULT_SIZE, 589, Short.MAX_VALUE)
       						.addComponent(jScrollPane3))))
       			.addContainerGap(197, Short.MAX_VALUE))
       );
       jPanel1Layout.setVerticalGroup(
       	jPanel1Layout.createParallelGroup(Alignment.LEADING)
       		.addGroup(jPanel1Layout.createSequentialGroup()
       			.addGap(37)
       			.addGroup(jPanel1Layout.createParallelGroup(Alignment.BASELINE)
       				.addComponent(jLabel4, GroupLayout.PREFERRED_SIZE, 26, GroupLayout.PREFERRED_SIZE)
       				.addComponent(jRadioButton1)
       				.addComponent(jRadioButton2)
       				.addComponent(jLabel5, GroupLayout.PREFERRED_SIZE, 27, GroupLayout.PREFERRED_SIZE)
       				.addComponent(jRadioButton3)
       				.addComponent(jRadioButton4))
       			.addPreferredGap(ComponentPlacement.RELATED)
       			.addGroup(jPanel1Layout.createParallelGroup(Alignment.BASELINE)
       				.addComponent(jButton1, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       				.addComponent(jButton2, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       				.addComponent(jButton3, GroupLayout.DEFAULT_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       				.addComponent(jButton4, GroupLayout.DEFAULT_SIZE, 22, Short.MAX_VALUE))
       			.addPreferredGap(ComponentPlacement.RELATED)
       			.addGroup(jPanel1Layout.createParallelGroup(Alignment.TRAILING)
       				.addGroup(jPanel1Layout.createSequentialGroup()
       					.addComponent(jLabel1)
       					.addPreferredGap(ComponentPlacement.UNRELATED)
       					.addComponent(jComboBox1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
       					.addPreferredGap(ComponentPlacement.RELATED, 16, Short.MAX_VALUE)
       					.addComponent(jLabel2))
       				.addComponent(jScrollPane1, GroupLayout.PREFERRED_SIZE, 100, GroupLayout.PREFERRED_SIZE))
       			.addPreferredGap(ComponentPlacement.RELATED)
       			.addGroup(jPanel1Layout.createParallelGroup(Alignment.LEADING)
       				.addGroup(jPanel1Layout.createSequentialGroup()
       					.addComponent(jComboBox2, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
       					.addGap(18)
       					.addComponent(jLabel3)
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addComponent(jComboBox3, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE)
       					.addGap(18)
       					.addComponent(jLabel6)
       					.addPreferredGap(ComponentPlacement.RELATED)
       					.addComponent(jComboBox4, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, GroupLayout.PREFERRED_SIZE))
       				.addComponent(jScrollPane2, GroupLayout.PREFERRED_SIZE, 234, GroupLayout.PREFERRED_SIZE))
       			.addPreferredGap(ComponentPlacement.RELATED)
       			.addComponent(jScrollPane3, GroupLayout.PREFERRED_SIZE, 128, GroupLayout.PREFERRED_SIZE)
       			.addContainerGap())
       );
       jPanel1.setLayout(jPanel1Layout);

       GroupLayout layout = new GroupLayout(getContentPane());
       layout.setHorizontalGroup(
       	layout.createParallelGroup(Alignment.LEADING)
       		.addGroup(layout.createSequentialGroup()
       			.addContainerGap()
       			.addComponent(jPanel1, GroupLayout.PREFERRED_SIZE, 835, GroupLayout.PREFERRED_SIZE)
       			.addContainerGap(153, Short.MAX_VALUE))
       );
       layout.setVerticalGroup(
       	layout.createParallelGroup(Alignment.LEADING)
       		.addGroup(layout.createSequentialGroup()
       			.addComponent(jPanel1, GroupLayout.PREFERRED_SIZE, GroupLayout.DEFAULT_SIZE, Short.MAX_VALUE)
       			.addContainerGap())
       );
       getContentPane().setLayout(layout);

       pack();
   }// </editor-fold>
   //
   /*    private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {//GEN-FIRST:event_jButton3ActionPerformed
   // TODO add your handling code here:
	final JDialog dialog = new JDialog(this);
	dialog.setTitle("选择要复制术语的设备类型");
	dialog.setSize(250, 150);
	WindowUtils.centerWindow(this, dialog);
	JPanel p1 = new JPanel(new BorderLayout());
	JPanel p2 = new JPanel();
	JPanel p3 = new JPanel();
	JPanel p4 = new JPanel();
	JLabel lb1 = new JLabel("源设备类型  ");
	final JComboBox cb1 = new JComboBox();
	cb1.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
	p2.add(lb1);
	p2.add(cb1);
	JLabel lb2 = new JLabel("目标设备类型");
	final JComboBox cb2 = new JComboBox();
	cb2.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
	p3.add(lb2);
	p3.add(cb2);
	JButton bt1 = new JButton("确定");
	bt1.addActionListener(new java.awt.event.ActionListener() {
		public void actionPerformed(java.awt.event.ActionEvent evt) {
			String srcType = ((CodeNameModel)cb1.getSelectedItem()).getCode();
			String tagType = ((CodeNameModel)cb2.getSelectedItem()).getCode();
			DeviceStateMentManager dsmm = new DeviceStateMentManager();
			dsmm.copyRecord(srcType, tagType);
		}
	});
	JButton bt2 = new JButton("取消");
	bt2.addActionListener(new java.awt.event.ActionListener() {
		public void actionPerformed(java.awt.event.ActionEvent evt) {
			dialog.dispose();
		}
	});
	p4.add(bt1);
	p4.add(bt2);
	p1.add(p2, BorderLayout.NORTH);
	p1.add(p3, BorderLayout.CENTER);
	p1.add(p4, BorderLayout.SOUTH);
	dialog.setContentPane(p1);
	
	dialog.setVisible(true);
}*/
                                       

   private void jButton4ActionPerformed(java.awt.event.ActionEvent evt) {
//		if (!isok()) {
//			javax.swing.JOptionPane.showMessageDialog(this, "请选择对应条件!",CBSystemConstants.SYSTEM_TITLE,JOptionPane.WARNING_MESSAGE);
//			return;
//		}
       this.wctsDialog.setVisible(true);
//   }
   
//	private boolean isok() {
//		String value = ((CodeNameModel) this.jComboBox1.getSelectedItem())
//				.getCode();
//		if ("".equals(value)) {
//			return false;
//		}
//		value = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
//		if ("".equals(value)) {
//			return false;
//		}
//		value = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();
//		if ("".equals(value)) {
//			return false;
//		}
		
//		return true;
	}
	
   //替换规则
   /*	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
   		CardWordManager word = new CardWordManager(
   				SystemConstants.getMainFrame(), true);
   		word.setVisible(true);
   	}*/
       //复制
	private void jButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		czrws_2 = "";
		czzls_2 = "";
		if (czrws_2.equals("")) {
			czrws_2 = czrws;
		}
		if (czzls_2.equals("")) {
			czzls_2 = czzls;
		}
		String qs = ((CodeNameModel) this.jComboBox2.getSelectedItem())
				.getCode();
		if (qs.equals("")) {
			devtype = ((CodeNameModel) this.jComboBox1.getSelectedItem())
					.getCode();
		}
	}
   //粘贴
	private void jButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		jTextArea1.setText(czrws_2);
		this.jEditorPane1.setText(czzls_2);
		String qs = ((CodeNameModel) this.jComboBox2.getSelectedItem())
				.getCode();
		/**
		if (qs.equals("")&&!devtype.equals("")) {
			DeviceStateMentManager dsmm = new DeviceStateMentManager();
			String nttype = ((CodeNameModel) this.jComboBox1.getSelectedItem())
			.getCode();
			String statetype="";
			if (this.jRadioButton1.isSelected()) 
			{
				statetype = "0";
			} else 
			{
				statetype = "1";
			}
			String dsql="SELECT T.zbid FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+nttype+
			"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
			QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode)+"'AND T.CZRW!=''";
			 List dresults=DBManager.query(dsql);
			 for(int i = 0 ;i<dresults.size();i++){
				 Map<String, String> map = (Map<String, String>) dresults.get(i);
				 DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB t where t.zbid ='"+map.get("zbid")+"'");
				 DBManager.execute("delete from "+CBSystemConstants.opcardUser+"t_a_CARDWORDCB t where t.f_zbid ='"+map.get("zbid")+"'");
			 }
			String isql="SELECT * FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDZB T WHERE T.DEVICETYPEID='"+devtype+
			"' AND T.STATETYPE='"+statetype+"' AND T.OPCODE='"+
			QueryDeviceDao.getOpcode(CBSystemConstants.unitCode, CBSystemConstants.roleCode)+"'AND T.CZRW!=''";
	        List iresults=DBManager.query(isql);
	        for(int i = 0 ;i<iresults.size();i++){
	        	Map<String, String> map = (Map<String, String>) iresults.get(i);
	        	String zbid  = map.get("zbid");
	        	String nzbid = dsmm.insertZBRecord(nttype, map.get("BEGINSTATUS"), map.get("ENDSTATE"), map.get("STATETYPE"),map.get("CARDTYPE"), map.get("CZRW"), map.get("BZSX"));
	        	List results=DBManager.query("SELECT * FROM "+CBSystemConstants.opcardUser+"t_a_CARDWORDCB");
	        	for (int j = 0; j < results.size(); j++) {
	        		Map<String, String> cmap = (Map<String, String>) results.get(i);
					String csql="INSERT INTO "+CBSystemConstants.opcardUser+"t_a_CARDWORDCB VALUES('"+cmap.get("DEVICESTATEMENTCB")+"','"+zbid+"',"+cmap.get("ORDERID")+")";
					DBManager.execute(csql);
				}
	        }
		}**/
	}
   
 //保护类型修改后的数据
   private DefaultComboBoxModel getcombobox1(){
   	List<CodeNameModel> devtypes=new ArrayList<CodeNameModel>();
//		List results = DBManager.query("select equiptype_flag ,equiptype_name from "+CBSystemConstants.opcardUser+"t_e_equiptype t where t.cim_id is not null");
   	//edit 2014.6.24
   	List results = DBManager.query(OPEService.getService().getcombobox1Sql());
		Map temp=new HashMap();  
		for (int i = 0; i < results.size(); i++) {
			CodeNameModel cnm=new CodeNameModel();
			temp=(Map)results.get(i);
			cnm.setCode(StringUtils.ObjToString(temp.get("EQUIPTYPE_FLAG")));
			cnm.setName(StringUtils.ObjToString(temp.get("EQUIPTYPE_NAME")));
			devtypes.add(cnm);
		}
		DefaultComboBoxModel model = new DefaultComboBoxModel();
		DeviceStateMentManager dsmm=new DeviceStateMentManager();
		CodeNameModel cnm=null;
		cnm=new CodeNameModel("","请选择");
		model.addElement(cnm);
		model.setSelectedItem(cnm);
		for(int i=0;i<devtypes.size();i++){
			cnm=devtypes.get(i);
			model.addElement(cnm);
		}
		return model;
   }
   
	//初始化条件下拉框
	private void initData() 
{
//		this.jComboBox1.setModel(InitDeviceTypeChockBox.getDevTypeCheckBox());
		this.jComboBox1.setModel(getcombobox1());
		this.jComboBox2.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(""));
		this.jComboBox3.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox("",""));
		this.jComboBox4.setModel(InitDeviceTypeChockBox.getDeviceVoltCheckBox());
}

	//初始化数据面板
	private void initTextModel()
{
		String statetype = "";     //操作票类型
		
		String devicetype = "";    //设备类型
		String status = "";        //设备状态
		String state = "";         //设备动作

		if (this.jRadioButton1.isSelected()) 
		{
			statetype = "0";
		} else 
		{
			statetype = "1";
		}
		
		if (this.jRadioButton4.isSelected()) 
		{
			cardtype = "0";
		} else
		{
			cardtype = "1";
		}
		
		devicetype = ((CodeNameModel) this.jComboBox1.getSelectedItem()).getCode();
		status = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		state = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();
		
		if ("".equals(devicetype))
		{
			return;
		}
		if (status.equals("")) 
		{
			return;
		}
		if (state.equals(""))
		{
			return;
		}
		
		//1.操作任务
		DeviceStateMentManager dsmm = new DeviceStateMentManager();
		String czrw = dsmm.getCZRW(devicetype, status, state, statetype, cardtype);
		czrws=czrw;
		jTextArea1.setText(czrw);
		
		//2.操作内容
		List<String> descs = dsmm.getStateMents(devicetype, status, state,statetype, cardtype);
		StringBuffer str = new StringBuffer();
		for (int i = 0; i < descs.size(); i++) {
			String tempStr = StringUtils.ObjToString(descs.get(i));
			if (str.length() == 0) {
				str.append(tempStr);
			} else {
				str.append("\r\n" + tempStr);
			}
		}
		czzls=str.toString();
		this.jEditorPane1.setText(str.toString());
		
		//备注
		DeviceStateMentManager dsmm_2 = new DeviceStateMentManager();
		String beizhu_second = dsmm_2.getZBBeiZhu(devicetype, status, state, statetype, cardtype);
   	//beizhu_second=dsmm.getZBBeiZhu(zbid);    //显示数据库中设备字段的信息
   	jTextArea2.setText(beizhu_second);
//   	System.out.println(beizhu_second+"运行过程不可断电");
	}

	//保存按钮
	private void jButton1ActionPerformed(java.awt.event.ActionEvent evt)
	{
		// TODO add your handling code here:
		DeviceStateMentManager dsmm = new DeviceStateMentManager();
		String statetype = ""; //操作票类型
		String cardtype = ""; //开票类型
		String devicetype = ""; //设备类型
		String status = ""; //设备状态
		String state = ""; //设备动作
		String voltvalue = ""; //电压等级
		String beizhu="";       
		String beizhu_second="";

		if (this.jRadioButton1.isSelected()) {
			statetype = "0";
		} else {
			statetype = "1";
		}
		if (this.jRadioButton3.isSelected()) {
			cardtype = "1";
		} else {
			cardtype = "0";
		}

		devicetype = ((CodeNameModel) this.jComboBox1.getSelectedItem())
				.getCode();
		status = ((CodeNameModel) this.jComboBox2.getSelectedItem()).getCode();
		state = ((CodeNameModel) this.jComboBox3.getSelectedItem()).getCode();

		if ("".equals(devicetype)) {
			ShowMessage.view("请选择设备类型！");
			return;
		}
		if (status.equals("")) {
			ShowMessage.view("请选择设备起始状态！");
			return;
		}
		if (state.equals("")) {
			ShowMessage.view("请选择设备操作动作！");
			return;
		}

		String czrw = jTextArea1.getText().trim();
		String conter = this.jEditorPane1.getText();
		if ("".equals(czrw) && "".equals(conter)) {
			ShowMessage.view(this,"填写内容为空，将删除该项术语配置！");
			String zbid = dsmm.getZBID(devicetype, status, state, statetype,
					cardtype);
			dsmm.deleteZBRecord(zbid);
			return;
		}

		//一、查询主表ID 如果没有新建一条主表记录
		
		String zbid = dsmm.getZBID(devicetype, status, state, statetype,cardtype);
		
		if ("".equals(zbid)) 
		{
			zbid = dsmm.insertZBRecord(devicetype, status, state, statetype,cardtype, czrw, beizhu);
		}
       else 
		{	
       	//beizhu_second=dsmm.getZBBeiZhu(zbid);    //显示数据库中设备字段的信息
       	//jTextArea2.setText(beizhu_second);
       	
       	beizhu=jTextArea2.getText();             //获得备注信息
       	dsmm.updateZBRecord(zbid,czrw,beizhu);

//			System.out.println(beizhu);
			//System.out.println(beizhu_second);
			
		}

		//二、插入从表记录

		String[] rowStrs = null;
		
		if (conter.indexOf("\r\n") >= 0) 
		{
			rowStrs = conter.split("\r\n");
		} 
		else if (conter.indexOf("\n") >= 0) 
		{
			rowStrs = conter.split("\n");
		} 
		else
       {
			rowStrs = new String[] { conter };
		}

		
		dsmm.insertCBrecords(zbid, rowStrs);
		ShowMessage.view(this, "保存成功！");
}
	
	
	

	private void jRadioButton3ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (this.jRadioButton4.isSelected()) {
			cardtype = "0";
		} else {
			cardtype = "1";
		}
		CodeNameModel cnm = (CodeNameModel) jComboBox1.getSelectedItem();
		jComboBox3.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(cnm
				.getCode(), cardtype));
		this.initTextModel();
	}

	private void jRadioButton4ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		if (this.jRadioButton4.isSelected()) {
			cardtype = "0";
		} else {
			cardtype = "1";
		}
		CodeNameModel cnm = (CodeNameModel) jComboBox1.getSelectedItem();
		jComboBox3.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(cnm
				.getCode(), cardtype));
		this.initTextModel();
	}

	private void jRadioButton1ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTextModel();
	}

	private void jComboBox2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTextModel();
	}

	private void jComboBox3ActionPerformed(java.awt.event.ActionEvent evt) {
		this.initTextModel();
	}

	private void jComboBox1ActionPerformed(java.awt.event.ActionEvent evt) {
		CodeNameModel cnm = (CodeNameModel) jComboBox1.getSelectedItem();
		jComboBox2.setModel(InitDeviceTypeChockBox.getDeviceStatusCheckBox(cnm
				.getCode()));
		jComboBox3.setModel(InitDeviceTypeChockBox.getDeviceStateCheckBox(cnm
				.getCode(), cardtype));
	}

	private void jRadioButton2ActionPerformed(java.awt.event.ActionEvent evt) {
		// TODO add your handling code here:
		this.initTextModel();
	}

	/**
	 * @param args the command line arguments
	 */
	public static void main(String args[]) {
		java.awt.EventQueue.invokeLater(new Runnable() {
			public void run() {
				CardDescDialog dialog = new CardDescDialog(
						new javax.swing.JFrame(), true);
				dialog.addWindowListener(new java.awt.event.WindowAdapter() {
					public void windowClosing(java.awt.event.WindowEvent e) {
						System.exit(0);
					}
				});
				dialog.setVisible(true);
			}
		});
	}

   // Variables declaration - do not modify
   private javax.swing.ButtonGroup buttonGroup1;
   private javax.swing.ButtonGroup buttonGroup2;
   private javax.swing.JButton jButton1;
   private javax.swing.JButton jButton2;
   private javax.swing.JButton jButton3;
   private javax.swing.JButton jButton4;
   private javax.swing.JComboBox jComboBox1;
   private javax.swing.JComboBox jComboBox2;
   private javax.swing.JComboBox jComboBox3;
   private javax.swing.JComboBox jComboBox4;
   private javax.swing.JEditorPane jEditorPane1;
   private javax.swing.JLabel jLabel1;
   private javax.swing.JLabel jLabel2;
   private javax.swing.JLabel jLabel3;
   private javax.swing.JLabel jLabel4;
   private javax.swing.JLabel jLabel5;
   private javax.swing.JLabel jLabel6;
   private javax.swing.JPanel jPanel1;
   private javax.swing.JRadioButton jRadioButton1;
   private javax.swing.JRadioButton jRadioButton2;
   private javax.swing.JRadioButton jRadioButton3;
   private javax.swing.JRadioButton jRadioButton4;
   private javax.swing.JScrollPane jScrollPane1;
   private javax.swing.JScrollPane jScrollPane2;
   private javax.swing.JScrollPane jScrollPane3;
   private javax.swing.JTextArea jTextArea1;
   private javax.swing.JTextArea jTextArea2;
   // End of variables declaration

}

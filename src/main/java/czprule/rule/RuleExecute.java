/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 设备操作规则执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule;

import com.tellhow.czp.app.CZPImpl;

import czprule.rule.model.RuleBaseMode;

public class RuleExecute implements RulebaseInf {

	protected static RulebaseInf rulebaseInf;
	protected static boolean isShowMessage = true;
	
	public boolean execute(RuleBaseMode Srcrbm) {
		
		if (rulebaseInf == null) {
			rulebaseInf=(RuleExecute)CZPImpl.getInstance("RuleExecute");
			if(rulebaseInf == null)
				rulebaseInf = new RuleExecuteDefault();
		}
		return rulebaseInf.execute(Srcrbm);
	}

	public boolean isShowMessage() {
		return isShowMessage;
	}


	public static void setShowMessage(boolean isShowMessage) {
		RuleExecute.isShowMessage = isShowMessage;
	}

}

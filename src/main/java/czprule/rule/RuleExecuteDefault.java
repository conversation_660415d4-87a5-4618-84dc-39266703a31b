/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 设备操作规则执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-7-8
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.CZPService;
import com.tellhow.czp.operationcard.VOSViewPanel;
import com.tellhow.czp.userrule.DeviceOperate;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.CheckMessage;
import czprule.model.PowerDevice;
import czprule.rule.dao.RuleManagerDao;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
import czprule.wordcard.dao.DeviceStateMentManager;

public class RuleExecuteDefault extends RuleExecute {
	
	public boolean execute(RuleBaseMode Srcrbm) {
		
		DeviceStateMentManager dsmm = new DeviceStateMentManager();
		if(Srcrbm.getStateCode().equals(""))
			Srcrbm.setStateCode(dsmm.getStateCodeByStatus(Srcrbm.getPd().getDeviceType(), Srcrbm.getEndState()));
		return execute(Srcrbm, Srcrbm);
		
	}
	
	public boolean execute(RuleBaseMode Steprbm, RuleBaseMode Srcrbm) {
		if(Steprbm==null)
			return false;
		PowerDevice pd=Steprbm.getPd();
		if(pd==null)
			return false;
//		if(pd.getDeviceStatus().equals(Steprbm.getEndState())){
//			return true;
//		}
		
		/*设备状态指令校核（暂不准确）
		 * 
		 
		if ((!pd.getDeviceStatus().equals(Steprbm.getBeginStatus()) ||pd.getDeviceStatus().equals(Steprbm.getEndState()))
				&& !Steprbm.getBeginStatus().equals("")
				&& (CBSystemConstants.jh_tai == 1 || CBSystemConstants.cardbuildtype == "1")&&CBSystemConstants.isRealTime) {
			List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
			pdlist.add(pd);
			CheckMessage cm = new CheckMessage();
			cm.setPd(pdlist);
			cm.setBottom("301");
			if(CBSystemConstants.lcm==null){
				CBSystemConstants.lcm = new ArrayList<CheckMessage>();
			}
			CBSystemConstants.lcm.add(cm);
			//VOSViewPanel vos = VOSViewPanel.getInstance();
			//vos.initData(Steprbm);
//			ShowMessage.viewWarning(SystemConstants.getMainFrame(), "操作指令与设备状态不符！");
			return true;
		}
		
		*
		*/
		if(!CBSystemConstants.cardbuildtype.equals("2")) {
			if(!Steprbm.getBeginStatus().equals("")) {
				
				if(CBSystemConstants.isCurrentSys) {
					if(CBSystemConstants.jh_tai == 1 ) {
						if(!pd.getDeviceStatus().equals(Steprbm.getBeginStatus())) {
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(pd);
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdlist);
							cm.setBottom("301");
							cm.setStatus(pd.getDeviceStatus());//记录设备当前的状态
							if(CBSystemConstants.lcm==null){
								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
							}
							CBSystemConstants.lcm.add(cm);
							return true;
						}
					}
				}
				else {
					if(CBSystemConstants.jh_tai == 1){
						if(!pd.getDeviceStatus().equals(Steprbm.getBeginStatus())) {
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(pd);
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdlist);
							cm.setStatus(pd.getDeviceStatus());//记录设备当前的状态
							cm.setBottom("301");
							if(CBSystemConstants.lcm==null){
								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
							}
							CBSystemConstants.lcm.add(cm);
							return true;
						}
						
						if(CBSystemConstants.roleCode.equals("0")) {
							
							//新增挂接母线校核
							if(CBSystemConstants.isRealTime){
								if(Steprbm.getInfoList().size()>0){
									String mxStr ="";
									if(Steprbm.getInfoList().get(0).contains("由正母")){
										mxStr="正母";
									}else if(Steprbm.getInfoList().get(0).contains("由副母")){
										mxStr="副母";
									}
									if(!mxStr.equals("")){
										List<PowerDevice> mxList = new ArrayList<PowerDevice>();
										if(pd.getDeviceType().equals(SystemConstants.Switch)){
											mxList = RuleExeUtil.getDeviceList(Steprbm.getPd(), SystemConstants.MotherLine, SystemConstants.PowerTransformer
													, false, true, true);
											if(mxList.size()>0&&!mxList.get(0).getPowerDeviceName().contains(mxStr)){
												List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
												pdlist.add(pd);
												CheckMessage cm = new CheckMessage();
												cm.setPd(pdlist);
												cm.setBottom("305");
												if(CBSystemConstants.lcm==null){
													CBSystemConstants.lcm = new ArrayList<CheckMessage>();
												}
												CBSystemConstants.lcm.add(cm);
											}
											if(Steprbm.getPd().getDeviceStatus().equals("0")&&
										            ((Steprbm.getInfoList().get(0).contains("改为正母")&&mxStr.contains("副母"))
										              ||Steprbm.getInfoList().get(0).contains("改为副母")&&mxStr.contains("正母"))){//进行热倒母线操作时，母联间隔处于非运行状态；（提示）
												if(mxList.size()>0){
													PowerDevice mlsw = RuleExeUtil.getMLSwitch(mxList.get(0));
													if(mlsw!=null&&!mlsw.getDeviceStatus().equals("0")){
														List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
														pdlist.add(pd);
														CheckMessage cm = new CheckMessage();
														cm.setPd(pdlist);
														cm.setBottom("610");
														if(CBSystemConstants.lcm==null){
															CBSystemConstants.lcm = new ArrayList<CheckMessage>();
														}
														CBSystemConstants.lcm.add(cm);
													}
												}
											
											}
										}else if(pd.getDeviceType().equals(SystemConstants.InOutLine)){
											List<PowerDevice> swList = RuleExeUtil.getDeviceList(Steprbm.getPd(), SystemConstants.Switch, SystemConstants.PowerTransformer,
													true, true, true);
											if(swList.size()>0){
												mxList = RuleExeUtil.getDeviceList(swList.get(0), SystemConstants.MotherLine, SystemConstants.PowerTransformer
														, false, true, true);
												if(mxList.size()>0&&!mxList.get(0).getPowerDeviceName().contains(mxStr)){
													List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
													pdlist.add(pd);
													CheckMessage cm = new CheckMessage();
													cm.setPd(pdlist);
													cm.setBottom("305");
													if(CBSystemConstants.lcm==null){
														CBSystemConstants.lcm = new ArrayList<CheckMessage>();
													}
													CBSystemConstants.lcm.add(cm);
												}
											}
										
										}
										
										if(Steprbm.getPd().getDeviceStatus().equals("0")&&
												(Steprbm.getInfoList().get(0).contains("改为正母")||Steprbm.getInfoList().get(0).contains("改为副母"))){//进行热倒母线操作时，母联间隔处于非运行状态；（提示）
											if(mxList.size()>0){
												PowerDevice mlsw = RuleExeUtil.getMLSwitch(mxList.get(0));
												if(mlsw!=null&&!mlsw.getDeviceStatus().equals("0")){
													List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
													pdlist.add(pd);
													CheckMessage cm = new CheckMessage();
													cm.setPd(pdlist);
													cm.setBottom("610");
													if(CBSystemConstants.lcm==null){
														CBSystemConstants.lcm = new ArrayList<CheckMessage>();
													}
													CBSystemConstants.lcm.add(cm);
												}
											}
										}
										
									}
								}
							}
						}
					}
							
					
					
					if(CBSystemConstants.jh_tai == 1&&CBSystemConstants.roleCode.equals("0")) {
						boolean isDevStatusChanged = false;
						Map<Integer, DispatchTransDevice> dtds= DeviceOperate.getAlltransDevMap();
						for (DispatchTransDevice dtd : dtds.values()) {
							if(dtd.getTransDevice().equals(pd)){
								isDevStatusChanged = true;
							}
						}
						
						if(isDevStatusChanged && !pd.getDeviceStatus().equals(Steprbm.getBeginStatus())) {
							List<PowerDevice> pdlist = new ArrayList<PowerDevice>();
							pdlist.add(pd);
							CheckMessage cm = new CheckMessage();
							cm.setPd(pdlist);
							cm.setBottom("301");
							cm.setStatus(pd.getDeviceStatus());//记录设备当前的状态

							if(CBSystemConstants.lcm==null){
								CBSystemConstants.lcm = new ArrayList<CheckMessage>();
							}
							CBSystemConstants.lcm.add(cm);
							return true;
						}
					}
				}
			}
		}
		
		//保存当前操作设备
		CBSystemConstants.putCurOperateDev(Steprbm.getPd());
		
		String runmode=pd.getDeviceRunModel().trim();  //runmode 设备接线方式，空值默认单母接线方式
		if("".equals(runmode)){
		    runmode=CBSystemConstants.RunModelOneMotherLine;  
		}
		String devType=pd.getDeviceType();
        RuleManagerDao rmd=new RuleManagerDao();
        RuleBaseMode rbm=null;
        RulebaseInf rb=null; 
        
        if(CBSystemConstants.isLock)
        {
        	boolean result = true;
           //执行设备操作场景条件
	       List<RuleBaseMode> scenelist=rmd.getRuleCBClass(runmode, devType, Steprbm.getBeginStatus(), Steprbm.getStateCode(), "0");
		   for (int i = 0; i < scenelist.size(); i++) {
			    rbm=scenelist.get(i);
			    rbm.setPd(pd); 
			    try {
				    rb=  (RulebaseInf)Class.forName(rbm.getRuleBeanClass()).newInstance();
				    System.out.println("执行场景类：   "+rbm.getRuleValue()+rbm.getRuleBeanClass());
				    Steprbm.getMessageList().clear();
				    result = rb.execute(Srcrbm);
				    if(isShowMessage&&CBSystemConstants.jh_tai == 0) {
						if(Srcrbm.getMessageList().size() > 0) {
							String message = "";
							for(String str : Srcrbm.getMessageList()) {
								message = message + str + "\r\n";
							}
							ShowMessage.viewWarning(SystemConstants.getMainFrame(), message);
							Srcrbm.getMessageList().clear();
						}
						if(Srcrbm.getInfoList().size() > 0) {
							String message = "";
							for(String str : Srcrbm.getInfoList()) {
								message = message + str + "\r\n";
							}
							ShowMessage.view(SystemConstants.getMainFrame(), message);
							Srcrbm.getInfoList().clear();
							
						}
				    }
				    else if(CBSystemConstants.jh_tai == 0){
				    	if(CBSystemConstants.isCurrentSys){
					    	if(Srcrbm.getMessageList().size() > 0) {
					    		//Srcrbm.getMessageList().clear();
					    		return false;
					    	}
					    	if(Srcrbm.getInfoList().size() > 0) {
					    		//Srcrbm.getInfoList().clear();
					    		//20131205
					    		if(CBSystemConstants.isSame)
					    			result = false;
					    		else
					    			result = true;
					    		continue;
					    	}
				    	}
				    }
			
					if(!result&&CBSystemConstants.jh_tai == 0)
						return false;
					
				} catch (ClassNotFoundException e) {
					ShowMessage.view("不存在逻辑类："+rbm.getRuleBeanClass());
					return false;
				} catch (Exception e) {
					e.printStackTrace();
					ShowMessage.view("逻辑类内部报错："+rbm.getRuleBeanClass());
					return false;
				}
        	}
		   if(CBSystemConstants.jh_tai != 1 && CBSystemConstants.isCurrentSys){
			   VOSViewPanel vos = VOSViewPanel.getInstance();
		   		vos.initData(Srcrbm);
		   		if(!vos.isOk())
					   return false;
		   }
		   else if(CBSystemConstants.roleCode.equals("2") && CBSystemConstants.jh_tai != 1 && CBSystemConstants.isCurrentSys){
			   VOSViewPanel vos = VOSViewPanel.getInstance();
		   		vos.initData(Srcrbm);
		   		if(!vos.isOk())
					   return false;
		   }
		   else if(CBSystemConstants.cardbuildtype.equals("1") && CBSystemConstants.jh_tai != 1 && !CBSystemConstants.isCurrentSys){ //Web开票
			   if(CBSystemConstants.lcm!= null && CBSystemConstants.lcm.size() > 0)
				   return false;
		   }
		   else if(!result&&CBSystemConstants.jh_tai == 0)
			   return false;
        }
       //执行设备操作动作
        runmode=pd.getDeviceRunModel().trim();  //runmode 设备接线方式，空值默认单母接线方式
		if("".equals(runmode)){
		    runmode=CBSystemConstants.RunModelOneMotherLine;  
		}
        	 List<RuleBaseMode> operatelist=rmd.getRuleCBClass(runmode, devType, Steprbm.getBeginStatus(), Steprbm.getStateCode(), "1");
             if(operatelist.size()==0){
      		   CBSystemConstants.getCurOperateDevs().remove(CBSystemConstants.getCurOperateDevs().size());
      		   return true;
      	   }
             for (int i = 0; i < operatelist.size(); i++) {
      		    rbm=operatelist.get(i);
      		    rbm.setPd(pd); 
      		    try {
      				rb = (RulebaseInf) Class.forName(rbm.getRuleBeanClass()).newInstance();
      			    System.out.println("执行动作类：   "+rbm.getRuleValue()+rbm.getRuleBeanClass());
      				if(!rb.execute(rbm))
      					return false;
      			} catch (ClassNotFoundException e) {
      				ShowMessage.view("不存在执行类："+rbm.getRuleBeanClass());
      				return true;
      			} catch (Exception e) {
      				e.printStackTrace();
      				ShowMessage.view("执行类内部报错："+rbm.getRuleBeanClass());
      				return false;
      			}
      	   }
        
        CBSystemConstants.getCurOperateDevs().remove(CBSystemConstants.getCurOperateDevs().size());
        
		if(CBSystemConstants.jh_tai != 1 && CBSystemConstants.lcm!=null){
			CBSystemConstants.lcm.clear();
		}
		
	    return true;
	}

}

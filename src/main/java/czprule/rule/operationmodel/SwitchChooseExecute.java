/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：预研部操作票产品
 * 功能说明 : 选择执行设备连接的开关执行器
 * 作    者 : 张余平
 * 开发日期 : 2011-08-4
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

public class SwitchChooseExecute implements RulebaseInf {

	/**
	 * 选择执行满足输入条件的开关  输入条件（变电站类型，设备运行类型，初始状态，执行动作）
	 */
	public boolean execute(RuleBaseMode rbm) {
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		String devRunType = rbm.getDeviceruntype();
		
		//一、搜索设备连接的开关
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		
		List<PowerDevice> switchs=new ArrayList<PowerDevice>();  //执行开关集合
		List<PowerDevice> tempswitchs=new ArrayList<PowerDevice>();  //开关集合
		PowerDevice tempDev=null;
		if(!"".equals(rbm.getTranType())){
			PowerDevice sourceLineTrans = CBSystemConstants.LineSource.get(pd.getPowerDeviceID());
			 List<PowerDevice> loadLineTrans = CBSystemConstants.LineLoad.get(pd.getPowerDeviceID());
			 if(sourceLineTrans==null||loadLineTrans==null){
				 ShowMessage.view("请先设置线路两端变电站属性！");
				 return false;
			 }
			 if("S".equals(rbm.getTranType())){
				 inPara.put("oprSrcDevice", sourceLineTrans);
	             inPara.put("tagDevType", SystemConstants.Switch);
	             inPara.put("excDevType", SystemConstants.PowerTransformer);
	             cs.execute(inPara, outPara);
	    	 	 inPara.clear();
	    	 	 tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
	    	  	 for (int i = 0; i < tempswitchs.size(); i++) {
	    	 		tempDev=(PowerDevice)tempswitchs.get(i);
	    	 		if (devRunType.equals("")) {
						switchs.add(tempDev);
					} else {
						if (tempDev.getDeviceRunType().equals(devRunType))
							switchs.add(tempDev);
					}
				 }
			 }else{
				 for (int i = 0; i < loadLineTrans.size(); i++) {
					inPara.put("oprSrcDevice", loadLineTrans.get(i));
					inPara.put("tagDevType", SystemConstants.Switch);
		            inPara.put("excDevType", SystemConstants.PowerTransformer);
		            cs.execute(inPara, outPara);
		    		inPara.clear();
		    		tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
		    		for (int j = 0; j < tempswitchs.size(); j++) {
		    			tempDev=(PowerDevice)tempswitchs.get(j);
		    	 		if (devRunType.equals("")) {
							switchs.add(tempDev);
						} else {
							if (tempDev.getDeviceRunType().equals(devRunType))
								switchs.add(tempDev);
						}
					}
				}
			 }
		}else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);
            inPara.put("excDevType", SystemConstants.PowerTransformer);
            cs.execute(inPara, outPara);
            tempswitchs = (ArrayList) outPara.get("linkedDeviceList");
   	  	    for (int i = 0; i < tempswitchs.size(); i++) {
	   	 		tempDev=(PowerDevice)tempswitchs.get(i);
	   	 		if (devRunType.equals("")) {
					switchs.add(tempDev);
				} else {
				    if (tempDev.getDeviceRunType().equals(devRunType))
					    switchs.add(tempDev);
				}
			}
		}	
	
	    List<PowerDevice> equips=new ArrayList<PowerDevice>(); //待选择开关集合
		for (int i = 0; i < switchs.size(); i++) {
			tempDev = (PowerDevice) switchs.get(i);
			if(!rbm.getBeginStatus().equals("")&&!rbm.getBeginStatus().equals(tempDev.getDeviceStatus()))
				continue;
			equips.add(tempDev);
        }
		if(equips.size()==0)
			return true;
		/*if(!CBSystemConstants.isCurrentSys)
			return true;*/
		String showMessage;	
		List<PowerDevice> chooseEquips = new ArrayList<PowerDevice>();

		if(!CBSystemConstants.isCurrentSys) {
			if(CBSystemConstants.mapPara.containsKey("lineStationID") && 
					!CBSystemConstants.mapPara.get("lineStationID").equals("")){
				String[] switchChooses = CBSystemConstants.mapPara.get("lineStationID").split("\\|\\|");
				String stationID = CBSystemConstants.mapPara.get("stationID");
				PowerDevice powerDevice= null;
				for(int i = 0;i < switchChooses.length;i++){
					powerDevice = CBSystemConstants.getPowerDevice(stationID, switchChooses[i]);
					if(powerDevice != null)
						chooseEquips.add(powerDevice);
				}
			}
		}
		else{
			//String showMessage="请选择由["+CBSystemConstants.getDeviceStateName(rbm.getBeginStatus())+"]转["+CBSystemConstants.getDeviceStateName(rbm.getEndState())+"]的开关";
			if(CBSystemConstants.jh_tai != 1) {
				showMessage="请选择需要转["+CBSystemConstants.getDeviceStateName(rbm.getEndState())+"]的开关";
				EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, equips, showMessage);
				chooseEquips=ecc.getChooseEquip();
				if(ecc.isCancel()){
					return false;
				}
			}
		}

		if(chooseEquips.size()==0)
			return true;
		boolean issess=false;
		for (int i = 0; i < chooseEquips.size(); i++) {
			
			tempDev = (PowerDevice) chooseEquips.get(i);
			String beginStatus="";
			if(rbm.getBeginStatus().equals(""))
				beginStatus=tempDev.getDeviceStatus();
			else
				beginStatus=rbm.getBeginStatus();
			RuleExecute ruleExecute=new RuleExecute();
			RuleBaseMode rbmode=new RuleBaseMode();
			rbmode.setPd(tempDev);
			rbmode.setBeginStatus(beginStatus);
			rbmode.setEndState(rbm.getEndState());
			if(ruleExecute.execute(rbmode)){
				issess=true;
			}
        }
		return issess;
	}

}

package czprule.rule.operationmodel;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;
import czprule.system.ShowMessage;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2012-9-19 上午11:13:13 
 */
public class SwitchUnloadSideMotherLine implements RulebaseInf {

	public boolean execute(RuleBaseMode rbm) {
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if(!pd.getDeviceType().equals(SystemConstants.Switch)){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]不是开关！");
        	return false;
        }
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
        	ShowMessage.view("该开关为旁路开关，请选择要倒回的开关操作！");
        	return false;
        }
		if(!"0".equals(pd.getDeviceStatus())){
			ShowMessage.view("开关["+pd.getPowerDeviceName()+"]不处于运行状态！");
        	return false;
		}
		if(pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchXL)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)||pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
        	
		CommonSearch cs=new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.MotherLine);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideML=null;
		PowerDevice sideKnife=null;
		List tempMLs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempMLs.size(); i++) {
			PowerDevice tempML = (PowerDevice) tempMLs.get(i);
            if(tempML.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
            	sideML = tempML;
            	ArrayList<PowerDevice> tempDevs= ((HashMap<PowerDevice,ArrayList<PowerDevice>>)outPara.get("pathList")).get(sideML);
            	for (int j = 0; j < tempDevs.size(); j++) {
            		if(tempDevs.get(j).getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL)) {
            			sideKnife = tempDevs.get(j);
            			break;
            		}
            	}
            	break;
            }
		}
		if(sideML == null){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]没有连接到旁路母线！");
        	return false;
        }
		if(!sideML.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+pd.getPowerDeviceName()+"]连接的旁路母线不在运行状态！");
        	return false;
        }
		if(sideKnife == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有连接到旁路刀闸！");
        	return false;
        }
		if(sideKnife.getDeviceStatus().equals("1")){
        	ShowMessage.view("["+sideKnife.getPowerDeviceName()+"]连接的旁路刀闸不在合上状态！");
        	return false;
        }
		inPara.put("oprSrcDevice", sideML);
        inPara.put("tagDevType", SystemConstants.Switch);
        cs.execute(inPara, outPara);
		inPara.clear();
		PowerDevice sideSwitch=null;
		List tempSwitchs = (ArrayList) outPara.get("linkedDeviceList");
		for (int i = 0; i < tempSwitchs.size(); i++) {
			PowerDevice tempSwitch = (PowerDevice) tempSwitchs.get(i);
            if(tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchPL) ||
            		tempSwitch.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchMLPL)){
            	sideSwitch = tempSwitch;
            	break;
            }
		}
		if(sideSwitch == null){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]没有连接到旁路开关！");
        	return false;
        }
		if(!sideSwitch.getDeviceStatus().equals("0")){
        	ShowMessage.view("["+sideML.getPowerDeviceName()+"]连接的旁路开关不在运行状态！");
        	return false;
        }
		
		if(pd.getPowerVoltGrade()>110){
			PowerDevice source =null;
			List<PowerDevice> load =new ArrayList<PowerDevice>();
			List<PowerDevice> xlList =RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine
					, SystemConstants.PowerTransformer, true, true, true);
			if(xlList.size()>0){
				source=xlList.get(0);
				load=RuleExeUtil.getLineOtherSideList(source);
			}
			if(source!=null&&load.size()>0){
				CBSystemConstants.putLineSource(source.getPowerDeviceID(), source);
				CBSystemConstants.putLineLoad(source.getPowerDeviceID(), load);
			}
	
		}
		
		
		
		boolean result = true;
		result = RuleExeUtil.deviceStatusChange(sideSwitch, sideSwitch.getDeviceStatus(), "1");
		if(!result)
			return false;
		result = RuleExeUtil.deviceStatusChange(sideKnife, "0", "1");
		if(!result)
			return false;
//		result = RuleExeUtil.deviceStatusChange(sideSwitch, "1", "2");
//		if(!result)
//			return false;
		
	}
	return true;
	}

}

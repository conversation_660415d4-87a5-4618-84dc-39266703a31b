package czprule.rule;

/**
 * 
 * 用于初始化数据时，对区域术语的复制
 */
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.system.CBSystemConstants;
import czprule.system.DBManager;

public class CopyCardWord {
	public static List listDecv = new ArrayList();// T_A_DEVICESTATEINFO集合

	
	/**
	 * sourceCode 源区域编码
	 * targetCode 目标域编码
	 * 查询所有操作任务，把endstate传递给T_A_CARDWORDZB表查询数据
	 * 如果T_A_CARDWORDZB有数据，则T_A_DEVICESTATEINFO插入一条该数据
	 */
	public static void copyDecvCard(String sourceCode, String targetCode, String sourceStateCode, String targetStateCode) {
//		List list = queryDecvState(sourceCode);
//		Map temp = new HashMap();
//		for (int i = 0; i < list.size(); i++) {
//			temp = (Map) list.get(i);
//			String stateCode = StringUtils.ObjToString(temp.get("STATECODE"));// 操作编码
//			//copyDevicesStateInfo(temp, targetCode);// 存在copy数据到T_A_DEVICESTATEINFO表中
//			if (decvCardWord(sourceCode,stateCode)) {// 判断endstate字段关联到主表中是否存在数据
//				long it = System.currentTimeMillis();
//				copyCardWordZB(stateCode, sourceCode,targetCode);//从源区域拷贝到目标区域
//			}
//
//		}
		if (decvCardWord(sourceCode,sourceStateCode)) {// 判断endstate字段关联到主表中是否存在数据
			copyCardWordZB(sourceStateCode, targetStateCode, sourceCode,targetCode);//从源区域拷贝到目标区域
		}
	}
	
	/*
	 * T_A_DEVICESTATEINFO表 查询所有设备的操作动作
	 */
	public static List queryDecvState(String sourceCode, String sourceStateCode) {
		String sql = "SELECT * FROM "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t where t.islock = '0' and t.opcode='"
				+ sourceCode + "' and t.statecode='"+sourceStateCode+"'";
		listDecv = DBManager.queryForList(sql);
		return listDecv;
	}
	
	/**
	 * 判断所有操作任务在规则定义中存在
	 * 
	 * @param endState
	 * @return
	 */
	public static boolean decvCardWord(String sourceCode,String endState) {

		String sql = "SELECT * FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.endstate='"
				+ endState + "'" +" and t.opcode= '"+sourceCode+"'";
		List listState = DBManager.queryForList(sql);
		if (listState.size() > 0) {
			return true;
		} else {
			return false;
		}
	}
	
	/**
	 * T_A_DEVICESTATEINFO插入一条该数据
	 * 
	 * @param temp
	 * @param stateCode
	 */
	public static String copyDevicesStateInfo(String sourceCode, String targetCode, String sourceStateCode, String parentStateCode) {
		
		String code_seq = "";
		List list = queryDecvState(sourceCode, sourceStateCode);
		Map temp = new HashMap();
		for (int i = 0; i < list.size(); i++) {
			temp = (Map) list.get(i);
			code_seq = java.util.UUID.randomUUID().toString();
			String stateName = StringUtils.ObjToString(temp.get("STATENAME"));
			String stateValue = StringUtils.ObjToString(temp.get("STATEVALUE"));
			String unitCode = targetCode;
			String devId = StringUtils.ObjToString(temp.get("DEVICETYPEID"));
			String parentCode = parentStateCode;
			String STATETYPE = StringUtils.ObjToString(temp.get("STATETYPE"));
			String runModel = StringUtils.ObjToString(temp.get("RUNMODEL"));
			String hassIde = StringUtils.ObjToString(temp.get("HASSIDE"));
			String runType = StringUtils.ObjToString(temp.get("RUNTYPE"));
			String stateOrder = StringUtils.ObjToString(temp.get("STATEORDER"));
			String secId = StringUtils.ObjToString(temp.get("SECONDTYPEID"));
			String secState = StringUtils.ObjToString(temp.get("SECONDSTATE"));
			String opeCode = StringUtils.ObjToString(temp.get("OPERATECODE"));
			String cardType = StringUtils.ObjToString(temp.get("CARDBUILDTYPE"));
			String stateKind = StringUtils.ObjToString(temp.get("STATEKIND"));
			String isLock = StringUtils.ObjToString(temp.get("ISLOCK"));
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO(STATECODE,STATENAME,STATEVALUE,"
					+ "OPCODE,DEVICETYPEID,PARENTCODE,STATETYPE,RUNMODEL,HASSIDE,RUNTYPE,STATEORDER,"
					+ "SECONDTYPEID,SECONDSTATE,OPERATECODE,CARDBUILDTYPE,STATEKIND,ISLOCK) VALUES('"
					+ code_seq
					+ "','"
					+ stateName
					+ "','"
					+ stateValue
					+ "','"
					+ unitCode
					+ "','"
					+ devId
					+ "','"
					+ parentCode
					+ "','"
					+ STATETYPE
					+ "','"
					+ runModel
					+ "','"
					+ hassIde
					+ "','"
					+ runType
					+ "','"
					+ stateOrder
					+ "','"
					+ secId
					+ "','"
					+ secState
					+ "','"
					+ opeCode
					+ "','"
					+ cardType
					+ "','"
					+ stateKind
					+ "','"
					+ isLock + "')";
			DBManager.execute(sql);
		}
		return code_seq;
		//copyDeviceParentCode(sourceCode,targetCode);//新复制的数据parentCode关联上stateCode字段
	}
	
	/**
	 * 查询stateCode中主表的数据
	 * @param stateCode
	 * @return
	 */
	public static List query(String stateCode,String sourceCode) {
		String sql = "SELECT * FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.ENDSTATE='"
				+ sourceCode + "'"+" and t.opcode='"+stateCode+"'";
		List list = DBManager.queryForList(sql);
		return list;
	}
	
	/**
	 * 根据T_A_DEVICESTATEINFO表中endState查询数据
	 * 
	 * @param endState
	 * @return
	 */
	public static void copyCardWordZB(String stateCode,String targetStateCode, String sourceCode, String areaCode) {

		List results = query(sourceCode,stateCode);
		Map temp = new HashMap();
		
		for (int i = 0; i < results.size(); i++) {//复制主表中已查询得到数据
			temp = (Map) results.get(i);
			String zbid_seq = java.util.UUID.randomUUID().toString();
			String zbid = StringUtils.ObjToString(temp.get("ZBID"));// 表ID
			String devType = StringUtils.ObjToString(temp.get("DEVICETYPEID"));// 设备类型编码
			String beginstate = StringUtils.ObjToString(temp.get("BEGINSTATUS"));// 起始状态
			String tagState = targetStateCode;// 操作动作
			String stateType = StringUtils.ObjToString(temp.get("STATETYPE"));// 0--状态令   1--元件令
			String czrw = StringUtils.ObjToString(temp.get("CZRW"));// 操作任务
			String volt = StringUtils.ObjToString(temp.get("VOLT"));// 电压等级
			String cardType = StringUtils.ObjToString((temp.get("CARDTYPE").toString()));// 0-正常票 1点图票
			String eqId = StringUtils.ObjToString(temp.get("EQUIPID"));// 设备ID
			String sql = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB(ZBID,DEVICETYPEID,BEGINSTATUS,ENDSTATE,STATETYPE,OPCODE,CZRW,VOLT,CARDTYPE,EQUIPID) "
					+ "VALUES('"
					+ zbid_seq
					+ "','"
					+ devType
					+ "','"
					+ beginstate
					+ "','"
					+ tagState
					+ "','"
					+ stateType
					+ "','"
					+ areaCode
					+ "','"
					+ czrw
					+ "','"
					+ volt
					+ "','"
					+ cardType
					+ "','"
					+ eqId + "')";
			DBManager.execute(sql);
			copyCardWordCB(zbid, zbid_seq);
		}
	}

	/**
	 * 描述：根据主表ID在找到对应的从表数据，并copy对应的从表数据到
	 * 
	 * @param F_ZBID
	 *            主表ID
	 * @param devicesCB
	 *            术语值
	 * @param orderid
	 *            排序
	 */
	public static void copyCardWordCB(String zbid, String zbid_seq) {
		
		String sql = "SELECT * FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB t where t.F_ZBID ='"
				+ zbid + "'";
		List list = DBManager.queryForList(sql);
		Map tempCB = new HashMap();
		for (int i = 0; i < list.size(); i++) {
			tempCB = (Map) list.get(i);
			String devicesCB = StringUtils.ObjToString(tempCB.get("DEVICESTATEMENTCB"));
			String orderid = StringUtils.ObjToString(tempCB.get("ORDERID"));
			String sql2 = "INSERT INTO "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB(F_ZBID,DEVICESTATEMENTCB,ORDERID) VALUES('"
					+ zbid_seq
					+ "','"
					+ devicesCB
					+ "','"
					+ orderid + "')";
			DBManager.execute(sql2);
		}
	}
	
	/**
	 * 删除语义的主从表已存在区域的数据
	 * @param code
	 */
	public static void delAreaWord(String code){
		//删除从表
		String sql = "DELETE FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDCB T WHERE T.F_ZBID IN\n" +
						"(SELECT ZBID FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB T WHERE T.OPCODE='"+code+"')";
		DBManager.execute(sql);
		//删除主表
		sql = " delete  FROM "+CBSystemConstants.opcardUser+"T_A_CARDWORDZB t where t.OPCODE='"
				+ code + "'";
		DBManager.execute(sql);
	}
	
	/**
	 * 复制T_A_DEVICESTATEINFO表数据后 新复制的数据parentCode与stateCode关联上
	 * 更新复制的parentCode不为0的数据为新的stateCode字段数据
	 * 
	 */
	public static void copyDeviceParentCode(String sourceCode,String tagertCode){
		String sql = " update "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO t "+
				" set t.parentcode=(select a2.statecode from "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a1, "+CBSystemConstants.opcardUser+"T_A_DEVICESTATEINFO a2"+
				" where a1.statename=a2.statename and a1.devicetypeid=a2.devicetypeid and a1.statetype=a2.statetype"+
				" and a1.cardbuildtype=a2.cardbuildtype"+
				" and a1.statecode=t.parentcode and  " +
				"a1.opcode='"+sourceCode+"' and a2.opcode='"+tagertCode+"')"+
				" where t.opcode='"+tagertCode+"' and t.parentcode!='0'";
		DBManager.execute(sql);
	}
}

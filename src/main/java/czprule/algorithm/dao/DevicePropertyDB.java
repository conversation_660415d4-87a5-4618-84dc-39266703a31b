package czprule.algorithm.dao;


import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.system.DeviceSVGPanelUtil;

public class DevicePropertyDB {
	
	//更新设备运行方式
	public void deviceRunTypeInsert(PowerDevice pd) {
		if(pd==null)
			return;
//		if(pd.getDeviceRunType().trim().equals(""))
//			return;
        String EquipID = pd.getPowerDeviceID();
        String deviceRunType = pd.getDeviceRunType();
        String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICERUNTYPE = '"+deviceRunType+"' WHERE T.EQUIPID = '"+EquipID+"'";
        DBManager.execute(sql);
    }
	
	//更新设备运行方式
	public void deviceRunTypeInsertList(String stationID) {
		
		List<String> sqlList = new ArrayList<String>();
		for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();){
			PowerDevice pd = (PowerDevice) iter.next();
	        String EquipID = pd.getPowerDeviceID();
	        String deviceRunType = pd.getDeviceRunType();
	        String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICERUNTYPE = '"+deviceRunType+"' WHERE T.EQUIPID = '"+EquipID+"'";
	        sqlList.add(sql);
		}
	    DBManager.batchUpdate(sqlList.toArray(new String[]{}));
    }
	
	//当前右键操作名称
	public static String getDeviceopName(String code){
		String sql="select statename from "+CBSystemConstants.opcardUser+"t_a_devicestateinfo where islock = '0' and  statecode='"+code+"'";
		List namel=DBManager.queryForList(sql);
		Map map=new HashMap();
		map=(Map) namel.get(0);
		return StringUtils.ObjToString(map.get("statename"));
	}
	
	//更新设备接线方式
	public void deviceModelInsert(PowerDevice pd){
		   String deviceRunModel = "";      //设备接线方式  
	       String EquipID = "";      //设备ID
	       String sql="";
	       EquipID = pd.getPowerDeviceID();
	       deviceRunModel = pd.getDeviceRunModel();
	       sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICERUNMODEL = '"+deviceRunModel+"' WHERE T.EQUIPID = '"+EquipID+"'";
	       DBManager.execute(sql);
	}
	
	public void deviceModelInsertList(String stationID) {
		
		List<String> sqlList = new ArrayList<String>();
		for(Iterator iter = CBSystemConstants.getStationPowerDevices(stationID).values().iterator();iter.hasNext();){
			PowerDevice pd = (PowerDevice) iter.next();
			String EquipID = pd.getPowerDeviceID();
	        String deviceRunModel = pd.getDeviceRunModel();
	        String sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICERUNMODEL = '"+deviceRunModel+"' WHERE T.EQUIPID = '"+EquipID+"'";
	        sqlList.add(sql);
		}
	    DBManager.batchUpdate(sqlList.toArray(new String[]{}));
    }
	
	/**
	 * 更新设备状态表
	 * @param equipid 设备ID
	 * @param status  设备状态
	 */
	public static void UpdateDeviceStatus(PowerDevice pd,String status){
		String sql="";
		String cb=CBSystemConstants.cardstatus;
		if(cb.equals("0")){
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICESTATUS='"+status+"' WHERE T.EQUIPID='"+pd.getPowerDeviceID()+"'";
		}else if(cb.equals("1")){
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DISPATCH='"+status+"' WHERE T.EQUIPID='"+pd.getPowerDeviceID()+"'";
		}else{
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.MONITORING='"+status+"' WHERE T.EQUIPID='"+pd.getPowerDeviceID()+"'";
		}
		
		DBManager.execute(sql);
		DeviceSVGPanelUtil.changeDeviceSVGColor(pd);
	}
	/**
	 * 更新设备状态表
	 * @param equipid 设备ID
	 * @param status  设备状态
	 */
	public static void UpdateDeviceStatus(List parameterList){
		String sql="";
		String cb=CBSystemConstants.cardstatus;
		if(cb.equals("0")){
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DEVICESTATUS=? WHERE T.EQUIPID=?";
		}else if(cb.equals("1")){
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.DISPATCH=? WHERE T.EQUIPID=?";
		}else{
			sql="UPDATE "+CBSystemConstants.deviceEquipUser +"t_a_DEVICEEQUIPINFO T SET T.MONITORING=? WHERE T.EQUIPID=?";
		}
		DBManager.update(sql, parameterList);
	}
	/**
	 * 将设备状态更新到设备遥信状态缓存表中
	 * @param equipid 设备ID
	 * @param status  遥信状态
	 */
	public static void updateDeviceEMSStatus(String equipid,String status){
		String sql="UPDATE "+CBSystemConstants.opcardUser+"t_a_TELESIGNALING T SET T.EQUIPSTATUS="+status+" WHERE T.EQUIPID='"+equipid+"'";
		DBManager.execute(sql);
	}
	/**
	 * 返回设备ID对应的操作票系统遥信状态即 元件状态
	 * @param equipid
	 * @return
	 */
	public static String getDeviceEMSStatus(String equipid){
		String sql="SELECT count(*) FROM  "+CBSystemConstants.opcardUser+"t_a_TELESIGNALING WHERE EQUIPID='"+equipid+"'";
		if(DBManager.queryForInt(sql) == 1) {
			sql="SELECT EQUIPSTATUS FROM  "+CBSystemConstants.opcardUser+"t_a_TELESIGNALING WHERE EQUIPID='"+equipid+"'";
			return DBManager.queryForString(sql);
		}
		else
			return "-1";
	}
	/**
	 * 返回遥信状态数据
	 * @param equipid
	 * @return
	 */
	public static boolean isEMSStatusExist(){
		String sql="SELECT count(*) FROM  "+CBSystemConstants.opcardUser+"t_a_TELESIGNALING";
		boolean result = DBManager.queryForInt(sql)==0?false:true;
		return result;
	}
}

/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 电容器状态判定
 * 作    者 : 张余平
 * 开发日期 : 2010-10-27
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.algorithm.devicestatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;
/**
 * 
 * <AUTHOR>
 *
 */
public class ElecCapacityStatus implements DeviceTypeJudgeInf{
	
	/**
	 * 电容器状态判定
	 * @return 0：判断成功、1：判断失败 -1没有判断
	 */
	public String doJudge(PowerDevice pd) {
		if(pd==null)
			return "-1";
		
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inPara=new HashMap<String,Object>();
		Map<String,Object> outPara=new HashMap<String,Object>();
		List results=null;
		PowerDevice temDev=null;
		String devStatus="";
		
		
		//一、搜索直接连接的开关
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.Switch);// 目标设备开关
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		inPara.clear();
	    results=(ArrayList)outPara.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //电抗器连接的开关，只有一个
		{
			 temDev=(PowerDevice)results.get(0);
			 devStatus=temDev.getDeviceStatus();
			 pd.setDeviceStatus(devStatus);
			 DevicePropertyDB.UpdateDeviceStatus(pd, devStatus);
			 return "0";
		}
	    pd.setDeviceStatus("0");
	    DevicePropertyDB.UpdateDeviceStatus(pd, "0");
		return "0";
	}

}

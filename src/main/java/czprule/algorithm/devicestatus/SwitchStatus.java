package czprule.algorithm.devicestatus;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 根据开关元件，刀闸的遥信数据判定开关的状态
 * 作          者 : zhangyp
 * 开发日期 : 2010-10-26
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class SwitchStatus implements DeviceTypeJudgeInf{
	private Map<String,String> DevChangeStatus=new HashMap<String,String>();

	public SwitchStatus(Map<String,String> DevChangeStatus){
	    this.DevChangeStatus=DevChangeStatus;
	}
	
/**
 * 开关状态判定
 * @return 0：判断成功、1：判断失败
 */
	public String doJudge(PowerDevice pd) {
		
		if(pd==null)
			return "-1";
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inPara=new HashMap<String,Object>();
		Map<String,Object> outPara=new HashMap<String,Object>();
		
		
		PowerDevice temDev=null;
		String devID="";
		String compState="";
		
		String knife_one="";
		String knife_two="";
		String groudknife_one="";
		String groudknife_two="";
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate+","+SystemConstants.SwitchFlowGroundLine);// 目标设备为刀闸
		inPara.put("validPort", "1");
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List devs = (ArrayList) outPara.get("linkedDeviceList");
		if(devs!=null&&devs.size()>0){
				for (int i = 0; i < devs.size(); i++) {   //刀闸
						temDev=(PowerDevice)devs.get(i);
						compState=temDev.getDeviceStatus();
						if(SystemConstants.SwitchSeparate.equals(temDev.getDeviceType())){
							//刀闸
							 if("0".equals(compState))
								 knife_one="0";
							 else{
								 if("".equals(knife_one))
									 knife_one="1";
							 }
							
						}else{
						     //接地刀闸	
							 if("0".equals(compState))
								 groudknife_one="0";
							 else{
								 if("".equals(groudknife_one))
									 groudknife_one="1";
							 }
						}
				}
		}
		inPara.put("validPort", "2");
		cs.execute(inPara, outPara);
		inPara.clear();
	    devs = (ArrayList) outPara.get("linkedDeviceList");
	    if(devs!=null&&devs.size()>0){
				for (int i = 0; i < devs.size(); i++) {   //刀闸
						temDev=(PowerDevice)devs.get(i);
						compState=temDev.getDeviceStatus();
						if(SystemConstants.SwitchSeparate.equals(temDev.getDeviceType())){
							//刀闸
							 if("0".equals(compState))
								 knife_two="0";
							 else{
								 if("".equals(knife_two))
									 knife_two="1";
							 }
							
						}else{
						     //接地刀闸	
							 if("0".equals(compState))
								 groudknife_two="0";
							 else{
								 if("".equals(groudknife_two))
									 groudknife_two="1";
							 }
							
						}
				}
	    }

		String switchCompState=(DevChangeStatus.get(pd.getPowerDeviceID())!=null)?DevChangeStatus.get(pd.getPowerDeviceID()):DevicePropertyDB.getDeviceEMSStatus(pd.getPowerDeviceID()); 
		if(switchCompState.equals("-1"))
			switchCompState = pd.getDeviceStatus().equals("0")?"0":"1";
		
		String switchStatus=""; //开关最终判定状态 
        
		//开关检修判定
		if("0".equals(groudknife_one)||"0".equals(groudknife_two)){
			switchStatus="3";
		}
		//开关冷备用判定
		else if("1".equals(knife_one)&&"1".equals(knife_two)&&"1".equals(switchCompState)){
			if("3".equals(pd.getDeviceStatus())&&"".equals(groudknife_one)&&"".equals(groudknife_two)){
        		//冷备用的情况下如果操作票系统中设备是检修，不更新 因为开关暂时没有接地刀闸，无法判断检修或者冷备用
        		return "-1";
        	}
			switchStatus="2";
		}
		//开关热备用
		else if(("0".equals(knife_one)||"0".equals(knife_two))&&"1".equals(switchCompState)){
			switchStatus="1";
		}
		else if(!switchCompState.equals("-1"))
			switchStatus = switchCompState;
		else
			switchStatus = "0";
//		//开关运行
//		if(("0".equals(knife_one)||"0".equals(knife_two))&&"0".equals(switchCompState)){
//			switchStatus="0";
//		}
		
		pd.setDeviceStatus(switchStatus);
		DevicePropertyDB.UpdateDeviceStatus(pd, switchStatus);
		return switchStatus;
	}
}

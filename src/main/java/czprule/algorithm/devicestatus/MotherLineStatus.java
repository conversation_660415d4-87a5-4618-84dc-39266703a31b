package czprule.algorithm.devicestatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class MotherLineStatus implements DeviceTypeJudgeInf{
	/**
	 * 母线状态判定
	 * @return 0：判断成功、1：判断失败 -1没有判断
	 */
	public String doJudge(PowerDevice pd) {
		if(pd==null)
			return "-1";
		
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inMap=new HashMap<String,Object>();
		Map<String,Object> outMap=new HashMap<String,Object>();
		PowerDevice tempDev=null;
		String devStatus="";
		List results=null;
		
        //母线连接的接地刀闸   
		inMap.put("oprSrcDevice", pd);
		inMap.put("isSearchDirectDevice", true);
		inMap.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
		cs.execute(inMap, outMap);
		inMap.clear();
	    results=(ArrayList)outMap.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //线路连接的接地刀闸，只有一个
		{
			tempDev=(PowerDevice)results.get(0);
			devStatus=tempDev.getDeviceStatus();
			if("0".equals(devStatus)){
				pd.setDeviceStatus("3");
				DevicePropertyDB.UpdateDeviceStatus(pd, "3");
				return "0";
			}
		}
		
        //母线连接的开关   根据开关的最高状态来定
		inMap.put("oprSrcDevice", pd);
		inMap.put("tagDevType", SystemConstants.Switch);
		inMap.put("excDevType",SystemConstants.PowerTransformer);
		cs.execute(inMap, outMap);
		inMap.clear();
	    Map allpath=(HashMap)outMap.get("pathList");
		if(allpath!=null&&allpath.size()>0)  //线路连接的接地刀闸，只有一个
		{
			int Status=10; //开关的最高状态 10是随便设置的，不可能超过10
			for (Iterator iter = allpath.keySet().iterator(); iter.hasNext();) {
				 tempDev = (PowerDevice) iter.next();
	           	 devStatus=tempDev.getDeviceStatus();
	           	 if(devStatus==null||"".equals(devStatus))
	           		 continue;
                
	           	 // 开关处于热备用或者运行，一定要在通路时才有效
	           	 if("01".indexOf(devStatus)>=0){
		           		 
		           		 List onePath=(ArrayList)allpath.get(tempDev);
		           		 PowerDevice knifeDev;
		           		 String knifeStatus="";
		           		 for(int i=1;i<onePath.size()-1;i++){
		           		      knifeDev=(PowerDevice)onePath.get(i);
		           		      knifeStatus=knifeDev.getDeviceStatus();
		           		      if("1".equals(knifeStatus))
		           		    	  break;
		           		 }
		           		 if("0".equals(knifeStatus)||"".equals(knifeStatus)) //“”时表示中间没有刀闸
		           		 {
		           			  if("0".equals(devStatus))
		           			  {
		           				pd.setDeviceStatus("0");
		           				DevicePropertyDB.UpdateDeviceStatus(pd, "0");
		        			 	return "0";
		           			  }
		           		 }else{
		           			 continue;
		           		 }
	           	 }
	           	 
	           	 if(Integer.parseInt(devStatus)<=Status){
	           		Status=Integer.parseInt(devStatus);
	           	 }
            }
            if(Status!=10)
            {
            	if(Status==2&&"3".equals(pd.getDeviceStatus())){
            		//冷备用的情况下如果操作票系统中设备是检修，不更新 以为母线暂时没有接地刀闸，无法判断检修或者冷备用
            		return "1";
            	}
	            pd.setDeviceStatus(String.valueOf(Status));
	            DevicePropertyDB.UpdateDeviceStatus(pd, String.valueOf(Status));
			 	return "0";
            }
		}
		pd.setDeviceStatus("0");
		DevicePropertyDB.UpdateDeviceStatus(pd, "0");
		return "0";
	}

}

package czprule.algorithm.devicestatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.algorithm.deviceruntype.JudgeMainTransKnife;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class MainTransStatus implements DeviceTypeJudgeInf{
	/**
	 * 主变状态判定
	 * @return 0：判断成功、1：判断失败 -1没有判断
	 */
	public String doJudge(PowerDevice pd) {
		if(pd==null)
			return "-1";
		
		
		PowerDevice groudKnife=null; //接地刀闸
		PowerDevice transKnife=null;  //主变刀闸
		List transSwitchs=new ArrayList(); //主变开关
		

		CommonSearch cs=new CommonSearch();
		Map<String,Object> inMap=new HashMap<String,Object>();
		Map<String,Object> outMap=new HashMap<String,Object>();
		PowerDevice tempDev=null;
		List results=null;
		
        // 0、主变连接的接地刀闸   
		inMap.put("oprSrcDevice", pd);
		inMap.put("isSearchDirectDevice", true);
		inMap.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
		cs.execute(inMap, outMap);
		inMap.clear();
	    results=(ArrayList)outMap.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //主变连接的接地刀闸，只有一个
			groudKnife=(PowerDevice)results.get(0);
		  

		
		// 1、 ----------主变连接的主变刀闸--------
		inMap.put("oprSrcDevice", pd);
		inMap.put("isSearchDirectDevice", true);
		inMap.put("tagDevType", SystemConstants.SwitchSeparate);
		cs.execute(inMap, outMap);
		inMap.clear();
	    results=(ArrayList)outMap.get("linkedDeviceList");
		for (int j = 0; j < results.size(); j++) {
			 tempDev = (PowerDevice)results.get(j);
			 JudgeMainTransKnife jmk=new JudgeMainTransKnife();
			 String returnStr=jmk.doJudge(tempDev);
			 if(returnStr.equals("0")) //线路连接的线路刀闸 一般只有一个
			 {
				 transKnife=tempDev;
				 break;
			 }
		}
		
		
		// 2、---------主变连接的主变开关------------------
		
		inMap.clear();
		inMap.put("oprSrcDevice", pd);
		inMap.put("tagDevType", SystemConstants.Switch);
		cs.execute(inMap, outMap);//搜索以开关为终端设备类型的通路
		inMap.clear();
		results=(ArrayList)outMap.get("linkedDeviceList");
		if(results!=null&&results.size()>0)
		     transSwitchs=results;			
		
		String devStatus="";
		
		//主变检修状态
		if(groudKnife!=null){
			devStatus=groudKnife.getDeviceStatus();
			if("0".equals(devStatus)){
				pd.setDeviceStatus("3");
				DevicePropertyDB.UpdateDeviceStatus(pd, "3");
				return "0";
			}
		}
		
		//有主变刀闸情况
		if(transKnife!=null){
				devStatus=transKnife.getDeviceStatus();
				if("1".equals(devStatus)){
					if(!"3".equals(pd.getDeviceStatus())){
	            		//冷备用的情况下如果操作票系统中设备是检修，不更新。主变暂时没有接地刀闸，无法判断检修或者冷备用
						return "1";
	            	}
					//主变刀闸断开   主变冷备用状态
					pd.setDeviceStatus("2");
					DevicePropertyDB.UpdateDeviceStatus(pd, "2");
					return "0";
				}else{
					//主变刀闸合上 热备用和运行的区别
					String Status=""; //开关的最高状态
                    for(int i=0;i<transSwitchs.size();i++){
                    	 tempDev=(PowerDevice)transSwitchs.get(i);
                    	 devStatus=tempDev.getDeviceStatus();
                    	 if("0".equals(devStatus)){
                    		 Status="0";
                    		 break;
                    	 }
                     }
                     if("0".equals(Status)){
               		     pd.setDeviceStatus("0");
               		     DevicePropertyDB.UpdateDeviceStatus(pd, "0");
    				 	 return "0";
	               	 }else{
	               		 pd.setDeviceStatus("1");
	               		 DevicePropertyDB.UpdateDeviceStatus(pd, "1");
	    				 return "0";
	               	 }
				}
		}else{
		    //无主变刀闸的情况
			int Status=10; //开关的最高状态
            for(int i=0;i<transSwitchs.size();i++){
	           	 tempDev=(PowerDevice)transSwitchs.get(i);
	           	 devStatus=tempDev.getDeviceStatus();
	           	 if(devStatus==null||"".equals(devStatus))
	           		 continue;
	           	 if(Integer.parseInt(devStatus)<=Status){
	           		Status=Integer.parseInt(devStatus);
	           	 }
            }
            if(Status!=10)
            {
            	if(Status==2&&"3".equals(pd.getDeviceStatus())){
            		//冷备用的情况下如果操作票系统中设备是检修，不更新 因为母线暂时没有接地刀闸，无法判断检修或者冷备用
            		return "0";
            	}
	            pd.setDeviceStatus(String.valueOf(Status));
	            DevicePropertyDB.UpdateDeviceStatus(pd, String.valueOf(Status));
			 	return "0";
            }
		}
		
		pd.setDeviceStatus("0");
		DevicePropertyDB.UpdateDeviceStatus(pd, "0");
	 	return "0";
	}

}

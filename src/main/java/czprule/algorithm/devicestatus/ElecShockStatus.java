/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：操作票专家系统
 * 功能说明 : 电抗器状态判定
 * 作    者 : 张余平
 * 开发日期 : 2010-10-26
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/
package czprule.algorithm.devicestatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;


/**
 * 电抗器状态获取
 * <AUTHOR>
 *
 */
public class ElecShockStatus implements DeviceTypeJudgeInf{
	/**
	 * 电抗器状态判定
	 * @return 0：判断成功、1：判断失败 -1没有判断
	 */
	public String doJudge(PowerDevice pd) {
		if(pd==null)
			return "-1";
		
		CommonSearch cs=new CommonSearch();
		Map<String,Object> inPara=new HashMap<String,Object>();
		Map<String,Object> outPara=new HashMap<String,Object>();
		List results=null;
		PowerDevice temDev=null;
		String devStatus="";
		
		//一、搜索直接连接的线路
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine);// 目标设备线路
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		inPara.clear();
	    results=(ArrayList)outPara.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //电抗器连接的线路，只有一个
		{
			 temDev=(PowerDevice)results.get(0);
			 devStatus=temDev.getDeviceStatus();
			 pd.setDeviceStatus(devStatus);
			 DevicePropertyDB.UpdateDeviceStatus(pd, devStatus);
			 return "0";
		}
		
		
		//二、搜索直接连接的开关
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.Switch);// 目标设备开关
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		inPara.clear();
	    results=(ArrayList)outPara.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //电抗器连接的开关，只有一个
		{
			 temDev=(PowerDevice)results.get(0);
			 devStatus=temDev.getDeviceStatus();
			 pd.setDeviceStatus(devStatus);
			 DevicePropertyDB.UpdateDeviceStatus(pd, devStatus);
			 return "0";
		}
		
		//三、搜索直接连接的刀闸,接地刀闸，根据这些设备的状态判定电抗状态
		String knifeStatus="";
		String groudStatus="";
		String lineStatus="";
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate+","+SystemConstants.SwitchFlowGroundLine);// 目标设备开关
		inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		inPara.clear();
	    results=(ArrayList)outPara.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //电抗器连接的刀闸，只有一个
		{
			 for(int i=0;i<results.size();i++){
				 temDev=(PowerDevice)results.get(i);
				 String devID=temDev.getPowerDeviceID();
				 devStatus=temDev.getDeviceStatus();
				 if(SystemConstants.SwitchSeparate.equals(temDev.getDeviceType())){
					 knifeStatus=devStatus;
				 }else{
					 groudStatus=devStatus;
				 }
			 } 
		}
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.MotherLine+","+SystemConstants.PowerTransformer);// 目标设备
		inPara.put("excDevType", SystemConstants.Switch);
		cs.execute(inPara, outPara);
		inPara.clear();
	    results=(ArrayList)outPara.get("linkedDeviceList");
	    if(results!=null&&results.size()>0)  //电抗器连接的线路，母线，主变只有一个
		{
			 temDev=(PowerDevice)results.get(0);
			 lineStatus=temDev.getDeviceStatus();
		}
	    
	    //检修
	    if("0".equals(groudStatus)){
	    	 pd.setDeviceStatus("3");
	    	 DevicePropertyDB.UpdateDeviceStatus(pd, "3");
			 return "0";
	    }
		//冷备用
	    if("1".equals(knifeStatus)){
	    	 pd.setDeviceStatus("2");
	    	 DevicePropertyDB.UpdateDeviceStatus(pd, "2");
			 return "0";
	    }
	    //热备用、运行
	    if(!"".equals(lineStatus)){
	    	pd.setDeviceStatus(lineStatus);
	    	DevicePropertyDB.UpdateDeviceStatus(pd, lineStatus);
			return "0";
	    }
	    
	    pd.setDeviceStatus("0");
	    DevicePropertyDB.UpdateDeviceStatus(pd, "0");
		return "0";
	}

}

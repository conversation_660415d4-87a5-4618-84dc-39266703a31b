package czprule.algorithm.devicestatus;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.algorithm.deviceruntype.JudgeLineKnife;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class LineStatus implements DeviceTypeJudgeInf{
	/**
	 * 线路状态判定
	 * @return 0：判断成功、1：判断失败
	 */
	public String doJudge(PowerDevice pd) {
		if(pd==null)
			return "-1";
		PowerDevice groudKnife=null; //接地刀闸
		PowerDevice lineKnife=null;  //线路刀闸
//		PowerDevice onlyKnife=null;  //代替开关的刀闸
//		PowerDevice dkDev=null;
		List lineSwitchs=new ArrayList(); //线路开关
		

		CommonSearch cs=new CommonSearch();
		Map<String,Object> inMap=new HashMap<String,Object>();
		Map<String,Object> outMap=new HashMap<String,Object>();
		PowerDevice tempDev=null;
		List results=null;
		
        // 0、线路连接的接地刀闸   
		inMap.put("oprSrcDevice", pd);
		inMap.put("isSearchDirectDevice", true);
		inMap.put("tagDevType", SystemConstants.SwitchFlowGroundLine);
		cs.execute(inMap, outMap);
		inMap.clear();
	    results=(ArrayList)outMap.get("linkedDeviceList");
		if(results!=null&&results.size()>0)  //线路连接的接地刀闸，只有一个
			groudKnife=(PowerDevice)results.get(0);
		  

		
		// 1、 ----------线路连接的线路刀闸--------
		inMap.put("oprSrcDevice", pd);
		inMap.put("isSearchDirectDevice", true);
		inMap.put("tagDevType", SystemConstants.SwitchSeparate);
		cs.execute(inMap, outMap);
		inMap.clear();
	    results=(ArrayList)outMap.get("linkedDeviceList");
		for (int j = 0; j < results.size(); j++) {
			 tempDev = (PowerDevice)results.get(j);
			 JudgeLineKnife jlk=new JudgeLineKnife();
			 String returnStr=jlk.doJudge(tempDev);
			 if(returnStr.equals("0")) //线路连接的线路刀闸 一般只有一个
			 {
				 lineKnife=tempDev;
				 break;
			 }
		}
		
		
		// 2、---------线路连接的线路开关------------------
		
		inMap.clear();
		inMap.put("oprSrcDevice", pd);
		inMap.put("tagDevType", SystemConstants.Switch);
		inMap.put("excDevType", SystemConstants.PowerTransformer);
		cs.execute(inMap, outMap);//搜索以开关为终端设备类型的通路
		inMap.clear();
		results=(ArrayList)outMap.get("linkedDeviceList");
		if(results!=null&&results.size()>0)
		     lineSwitchs=results;
			
		
//		// 3、------- 找线路只存在的刀闸 即能取代线路开关的刀闸-------
//		
//		inMap.put("oprSrcDevice", pd);
//		inMap.put("tagDevType", SystemConstants.MotherLine+","+SystemConstants.PowerTransformer);
//		inMap.put("excDevType", SystemConstants.Switch);
//		cs.execute(inMap, outMap);//搜索以开关为终端设备类型的通路
//		Map allPaths=(Map)outMap.get("pathList");
//		List onePath=new ArrayList();
//		
//		for (Iterator iter = allPaths.values().iterator(); iter.hasNext();) {
//			onePath = (ArrayList)iter.next();
//			for(int r=1;r<onePath.size();r++) {
//				tempDev = (PowerDevice)onePath.get(r);
//				if(tempDev.getDeviceType().equals(SystemConstants.SwitchSeparate)){
//					onlyKnife=tempDev;   //这里存在旁路刀闸的情况，到时候要注意排除！
//					break;
//				}
//			}
//		}
//		
//		
//		
//		// 4、 --------- 找线路上的高压电抗器 --------
//		inMap.put("oprSrcDevice", pd);
//		inMap.put("tagDevType", SystemConstants.ElecShock);
//		inMap.put("excDevType", SystemConstants.PowerTransformer+","+SystemConstants.Switch);
//		cs.execute(inMap, outMap);
//		inMap.clear();
//		results=(ArrayList)outMap.get("linkedDeviceList");
//		if(results!=null&&results.size()>0)
//			dkDev=(PowerDevice)results.get(0);
		
		
		String devStatus="";
		
		//线路检修状态
		if(groudKnife!=null){
			devStatus=groudKnife.getDeviceStatus();
			if("0".equals(devStatus)){
				pd.setDeviceStatus("3");
				DevicePropertyDB.UpdateDeviceStatus(pd, "3");
				return "0";
			}
		}
		
		//有线路刀闸情况
		if(lineKnife!=null){
				devStatus=lineKnife.getDeviceStatus();
				if("1".equals(devStatus)){
					//线路刀闸断开   线路冷备用状态
					pd.setDeviceStatus("2");
					DevicePropertyDB.UpdateDeviceStatus(pd, "2");
					return "0";
				}else{
					//线路刀闸合上 热备用和运行的区别
					String Status=""; //开关的最高状态
                    for(int i=0;i<lineSwitchs.size();i++){
                    	 tempDev=(PowerDevice)lineSwitchs.get(i);
                    	 devStatus=tempDev.getDeviceStatus();
                    	 if("0".equals(devStatus)){
                    		 Status="0";
                    		 break;
                    	 }
                     }
                     if("0".equals(Status)){
               		     pd.setDeviceStatus("0");
               		     DevicePropertyDB.UpdateDeviceStatus(pd, "0");
    				 	 return "0";
	               	 }else{
	               		 pd.setDeviceStatus("1");
	               		 DevicePropertyDB.UpdateDeviceStatus(pd, "1");
	    				 return "0";
	               	 }
				}
		}else{
		    //无线路刀闸的情况
			int Status=10; //开关的最高状态
            for(int i=0;i<lineSwitchs.size();i++){
	           	 tempDev=(PowerDevice)lineSwitchs.get(i);
	           	 devStatus=tempDev.getDeviceStatus();
	           	 if(devStatus==null||"".equals(devStatus))
	           		 continue;
	           	 if(Integer.parseInt(devStatus)<=Status){
	           		Status=Integer.parseInt(devStatus);
	           	 }
            }
            if(Status!=10)
            {
	            pd.setDeviceStatus(String.valueOf(Status));
	            DevicePropertyDB.UpdateDeviceStatus(pd, String.valueOf(Status));
			 	return "0";
            }
		}
		
		pd.setDeviceStatus("0");
		DevicePropertyDB.UpdateDeviceStatus(pd, "0");
		return "0";
		
	}

}

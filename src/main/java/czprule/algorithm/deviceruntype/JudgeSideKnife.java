package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeSideKnife implements DeviceTypeJudgeInf{
	
	/**
	 * 用   途 : 判定某个刀闸是否为旁路刀闸
	 * 判定描述 ：刀闸一端直接连接母线，另一端连接线路和线路开关
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @返回值：输出参数 0代表是，-1代表不是
	 **/
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}	
		
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
//			return "-1";
//		}	
//		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifePL))  { 
//			 return "0";
//		}
		
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();
		
		//判断连接设备
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);// 目标设备为线路或者母线
	    inPara.put("isSearchDirectDevice", true);
	    
		cs.execute(inPara, outPara);
		List<PowerDevice> allDevice = (ArrayList) outPara.get("linkedDeviceList");		
		for(PowerDevice dev : allDevice) {
			if(dev.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)
					&&RuleExeUtil.getDeviceDirectList(pd, "").size()>1){//旁路刀闸的直接连接设备要大于一（除了旁路母线还要连接其他设备），因为有的旁路PT刀闸由于没PT模型，错误识别成了旁路刀闸
				return "0";
			}
			
		}
		
		return "-1";
  }
}

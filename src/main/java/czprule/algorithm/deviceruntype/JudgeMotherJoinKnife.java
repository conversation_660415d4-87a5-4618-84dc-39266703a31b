package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CommonSearch;

public class JudgeMotherJoinKnife implements DeviceTypeJudgeInf{
/**
 * 用   途 : 判定某个刀闸是否为母联刀闸
 * 判定描述 ：刀闸两端连接的均为母线，并且刀闸没有连接开关
 * @参数1(Map)：输入参数 设备（powerDevice）
 * @返回值：输出参数 0代表是，-1代表不是
 **/
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		// TODO Auto-generated method stub
		
		if (pd==null) {
			return "-1";
		}	
		
		 Map inPara = new HashMap();
	     Map outPara = new HashMap();
	     CommonSearch cs = new CommonSearch();
	     
	     //判断刀闸是否连接开关
	     inPara.put("oprSrcDevice", pd);
	     inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
	     cs.execute(inPara, outPara);
	     List switchs=(ArrayList) outPara.get("linkedDeviceList");
	     if(switchs.size()>0)
	    	 return "-1";
	     
	     //搜索刀闸两端的母线
	     inPara.put("oprSrcDevice", pd);
	     inPara.put("tagDevType", SystemConstants.MotherLine);
	     inPara.put("excDevType", SystemConstants.PowerTransformer);
	     inPara.put("validPort", 1);
	     cs.execute(inPara, outPara);
	     List oneMotherLine = (ArrayList) outPara.get("linkedDeviceList");
	     
	     inPara.put("validPort", 2);
	     cs.execute(inPara, outPara);
	     List twoMotherLine = (ArrayList) outPara.get("linkedDeviceList");
	     
	     if(oneMotherLine.size()>0&&twoMotherLine.size()>0){
	    	 List<PowerDevice> lineList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);

	    	 if(lineList.size() > 0){
	    		 return "-1";
	    	 }

	    	 return "0";
	     }else{
	    	 return "-1";
	     }
	}
}

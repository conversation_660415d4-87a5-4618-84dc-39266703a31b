package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeMotherKnife implements DeviceTypeJudgeInf{
	/**
	 * 用 途:判定某个刀闸是否为母线侧刀闸 
	 * 判定描述：刀闸一端直接连母线、一端直接连开关
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @返回值：输出参数 0代表是，1代表不是
	 */
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		// TODO Auto-generated method stub
		
		if (pd==null) {
			return "-1";
		}	
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
//			return "-1";
//		}	
//		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeMX))  { 
//			 return "0";
//		}
		

		Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();	
		
		
		//如果刀闸直接连接的线路、母线，且没直接连接开关，则不是母线刀闸
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.Switch+","+SystemConstants.MotherLine);// 目标设备
	    inPara.put("isSearchDirectDevice", true);
		cs.execute(inPara, outPara);
		List<PowerDevice> linkDev = (ArrayList) outPara.get("linkedDeviceList");		
		inPara.clear();
		boolean linkSW = false;
		boolean linkXL =false;
		boolean linkMX =false;
		for(PowerDevice dev:linkDev){
			if(dev.getDeviceType().equals(SystemConstants.InOutLine)){
				linkXL=true;
			}else if(dev.getDeviceType().equals(SystemConstants.Switch)){
				linkSW=true;
			}else if(dev.getDeviceType().equals(SystemConstants.MotherLine)){
				linkMX=true;
			}
		}
		if(linkXL&&linkMX&& !linkSW){
			return "-1";
		}
		
		
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchSeparate+","+SystemConstants.Switch+","+SystemConstants.MotherLine);// 目标设备
	    inPara.put("isSearchDirectDevice", true);
	    inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		List<PowerDevice> allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");		
	    inPara.put("validPort", 2);
		cs.execute(inPara, outPara);
		inPara.clear();
		List<PowerDevice> allDeviceTwo = (ArrayList) outPara.get("linkedDeviceList");
		if(allDeviceOne.size()==0||allDeviceTwo.size()==0){
			if(CBSystemConstants.pjzMap.containsKey(pd.getPowerStationID())){
				//判断连接设备
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType",SystemConstants.Switch+","+SystemConstants.MotherLine);// 目标设备开关
			    inPara.put("isSearchDirectDevice", true);
			    inPara.put("validPort", 0);
				cs.execute(inPara, outPara);
				List allDeviceZero = (ArrayList) outPara.get("linkedDeviceList");		
				if(allDeviceZero.size()>1){
					return "0";
				}
			}
			return "-1";
		}
		if(allDeviceOne.size()>=2&&allDeviceTwo.size()>=2){
			return "-1";
		}
		if(allDeviceOne.size()>=1 && allDeviceOne.get(0).getDeviceType().equals(SystemConstants.MotherLine)) {
			for(PowerDevice ml : allDeviceOne) {
				if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					return "-1";
			}
			return "0";
		}
		if(allDeviceTwo.size()>=1 && allDeviceTwo.get(0).getDeviceType().equals(SystemConstants.MotherLine)) {
			for(PowerDevice ml : allDeviceTwo) {
				if(ml.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					return "-1";
			}
			return "0";
		}
		
		if(CBSystemConstants.pjzMap.containsKey(pd.getPowerStationID())){
			//判断连接设备
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType",SystemConstants.Switch+","+SystemConstants.MotherLine);// 目标设备开关
		    inPara.put("isSearchDirectDevice", true);
		    inPara.put("validPort", 0);
			cs.execute(inPara, outPara);
			List allDeviceZero = (ArrayList) outPara.get("linkedDeviceList");		
			if(allDeviceZero.size()>1){
				return "0";
			}
		}
		
		return "-1";
	}
}

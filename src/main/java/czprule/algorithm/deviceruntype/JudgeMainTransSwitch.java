package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.baserule.JudgeVolt;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CommonSearch;

public class JudgeMainTransSwitch implements DeviceTypeJudgeInf{

	/**
     * 用  途:主变（电源侧负荷侧）开关判定算法
     * 算法描述：开关直接连接主变，再根据设备电压等级与所属变电站电压等级的比较来判断为电源侧或负荷侧主变开关
     * @参数1(Map)：输入参数 设备（oprSrcDevice）
     * @返回值：输出参数 0代表为电源侧主变开关，1代表负荷侧主变开关  -1没有意义。
     */
   public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}	

		Map<String,Object> inPara = new HashMap<String, Object>();
        Map<String,Object> outPara = new HashMap<String,Object>();
        CommonSearch cs = new CommonSearch();
        
        inPara.put("oprSrcDevice", pd);
        inPara.put("tagDevType", SystemConstants.PowerTransformer);  //目标设备主变
        inPara.put("excDevType", SystemConstants.Switch);
        cs.execute(inPara, outPara);
        inPara.clear();
        
        List allDevs = (List) outPara.get("linkedDeviceList");
        if(allDevs.size() != 1)
        	return "-1";
        
        List<PowerDevice> tfList = new ArrayList<PowerDevice>();
        List<PowerDevice> lnList = new ArrayList<PowerDevice>();
        List<PowerDevice> devList = new ArrayList<PowerDevice>();
        devList.add(pd);
        devList.addAll(RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate));
        for(PowerDevice dev : devList) {
        	 tfList.addAll(RuleExeUtil.getDeviceDirectList(dev, SystemConstants.PowerTransformer));
        	 lnList.addAll(RuleExeUtil.getDeviceDirectList(dev, SystemConstants.InOutLine));
        }
        if(lnList.size() > 0 && tfList.size() == 0)
        	return "-1";
        
        inPara.put("oprSrcDevice", pd);
        JudgeVolt jv = new JudgeVolt();
        String returnValue=jv.doJudge(pd);
            if ("1".equals(returnValue)) {
                //开关电压等级和变电站相等
               return "0";
            }else {
               return "1";
            }
        
	}

}

package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;


public class JudgePowerSideKnife implements DeviceTypeJudgeInf{
	
	/**
	 * 用途：判定某个刀闸是否为线路侧刀闸
	 * 判定描述：刀闸一端直接连线路、一端直接连开关
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @return 0表示是  -1表示否
	 *
	 */
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		// TODO Auto-generated method stub
		if (pd==null) {
			return "-1";
		}	
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
//			return "-1";
//		}	
//		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeDY))  { 
//			 return "0";
//		}
		
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();	

		//判断连接设备
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType",SystemConstants.Switch);// 目标设备开关
	    inPara.put("isSearchDirectDevice", true);
	    inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		List allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");		
		if(allDeviceOne.size()>0){
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType",SystemConstants.InOutLine);// 目标设备开关
		    inPara.put("validPort", 2);
			cs.execute(inPara, outPara);
			allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");
			if(allDeviceOne.size()>0)
				return "0";
			else
				return "-1";
		}
		inPara.clear();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType",SystemConstants.Switch);// 目标设备开关
	    inPara.put("isSearchDirectDevice", true);
	    inPara.put("validPort", 2);
		cs.execute(inPara, outPara);
		List allDeviceTwo = (ArrayList) outPara.get("linkedDeviceList");
		if(allDeviceTwo.size()>0){
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType",SystemConstants.InOutLine);// 目标设备开关
		    inPara.put("validPort", 1);
			cs.execute(inPara, outPara);
			allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");
			if(allDeviceOne.size()>0)
				return "0";
			else
				return "-1";
		}
		
		if(CBSystemConstants.pjzMap.containsKey(pd.getPowerStationID())){
			//判断连接设备
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType",SystemConstants.Switch);// 目标设备开关
		    inPara.put("isSearchDirectDevice", true);
		    inPara.put("validPort", 0);
			cs.execute(inPara, outPara);
			List allDeviceZero = (ArrayList) outPara.get("linkedDeviceList");		
			if(allDeviceZero.size()>0){
				inPara.clear();
				inPara.put("oprSrcDevice", pd);
				inPara.put("tagDevType",SystemConstants.InOutLine);// 目标设备开关
			    inPara.put("validPort", 0);
				cs.execute(inPara, outPara);
				allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");
				if(allDeviceOne.size()>0)
					return "0";
				else
					return "-1";
			}
		}
		return "-1";
	}

}


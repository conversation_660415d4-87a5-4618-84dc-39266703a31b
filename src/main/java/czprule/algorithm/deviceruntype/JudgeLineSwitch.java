package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class JudgeLineSwitch implements DeviceTypeJudgeInf{

	/**
	 * 用 途: 线路开关判定算法
	 * 算法描述：判定开关存在一端连接为线路
	 * @参数1(Map)：输入参数 设备（pd）
	 * @return：输出参数 0代表为线路开关，-1代表结果没有意义
	 */
	 public String doJudge(PowerDevice pd) {
			
			if (pd==null) {
				return "-1";
			}	
			Map<String,Object> inPara = new HashMap<String, Object>();
	        Map<String,Object> outPara = new HashMap<String,Object>();
	        CommonSearch cs = new CommonSearch();
		
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.InOutLine); //目标设备进出线
			inPara.put("excDevType", SystemConstants.PowerTransformer + ","+ SystemConstants.Switch);
			cs.execute(inPara, outPara);
			inPara.clear();
            List allDevs = (ArrayList) outPara.get("linkedDeviceList");
	        if(allDevs.size()>0){
	        	return "0";
	        }else{
	        	return "-1";
	        }
	}
}

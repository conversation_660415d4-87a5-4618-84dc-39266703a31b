package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;


public class JudgeSideMotherLine implements DeviceTypeJudgeInf{
	/**
	 * 用 途:判定某个母线是否为旁路母线
	 * 判定描述：母线直接连接的刀闸为旁路刀闸
	 * @参数：输入参数 设备（powerDevice）
	 * @返回值：输出参数 0代表是，-1代表无意义结果
	 */
	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}
		if(pd.getPowerDeviceName().contains("旁母")||pd.getPowerDeviceName().contains("旁路")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSideMother);
			pd.setDeviceSetType(CBSystemConstants.RunTypeSideMother);
			return "0";
		}
		List<PowerDevice> knifeList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.SwitchSeparate); //母线直接连接的刀闸
		for(PowerDevice knife : knifeList){
			List<PowerDevice> devList = RuleExeUtil.getDeviceDirectList(knife, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer);
			
			for(PowerDevice devTemp : devList){
				if(devTemp.getPowerVoltGrade() == pd.getPowerVoltGrade()) {
					List<PowerDevice> mlList = RuleExeUtil.getDeviceList(devTemp, knife, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", "", false, true, true, true, true);
					if(mlList.size() > 0 && !mlList.contains(pd)) {
						pd.setDeviceRunType(CBSystemConstants.RunTypeSideMother);
						pd.setDeviceSetType(CBSystemConstants.RunTypeSideMother);
						return "0";
					}
				}
			}
		}
		pd.setDeviceRunType("");
		return  "-1";
	}
}

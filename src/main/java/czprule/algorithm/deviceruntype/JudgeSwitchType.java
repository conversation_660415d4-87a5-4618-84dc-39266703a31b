package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public class JudgeSwitchType implements DeviceTypeJudgeInf{

	/**
	 * 用 途:判断开关所属类型
	 * @参数1(Map)：输入参数 设备（pd）
	 * @return：输出参数 开关所属类型： 0 代表线路开关、1 代表电源侧主变开关、2代表负荷侧主变开关
	 *             、3代表母联兼旁路开关、4 代表旁母开关、5 代表母联开关，6代表其它开关.
	 */
	 public String doJudge(PowerDevice pd) {
			
		if (pd==null) {
			return "-1";
		}	
		DevicePropertyDB idrt=new DevicePropertyDB();//状态插入类
		String returnValue="";
		DeviceTypeJudgeInf dtji=null;
		
		if(pd.getPowerDeviceName().contains("分段")||pd.getPowerDeviceName().contains("内桥")||pd.getPowerDeviceName().contains("母联")){
			dtji = new JudgeMotherOrSideJoinSwitch();
			returnValue=dtji.doJudge(pd);
			switch (Integer.parseInt(returnValue)) {
			case 0:
				pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchMLPL);
				return "3";
			case 1:
				pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchPL);
				return "4";
			case 2:
				pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchML);
				return "5";
			default:
				break;
			}
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchML);
			return "5";
		}
		
		
		// 主变（电源侧与负荷侧）开关判定
		dtji = new JudgeMainTransSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchDYC);
			return "1";
		}else{
			if(returnValue.equals("1")){
				pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchFHC);
				return "2";
			}
		}
		
		// 线路开关判定
		dtji = new JudgeLineSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchXL);
			return "0";
		}
		    
		//母联开关和旁路开关判定
		dtji = new JudgeMotherOrSideJoinSwitch();
		returnValue=dtji.doJudge(pd);
		switch (Integer.parseInt(returnValue)) {
		case 0:
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchMLPL);
			return "3";
		case 1:
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchPL);
			return "4";
		case 2:
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchML);
			return "5";
		default:
			break;
		}
		
		

		dtji = new JudgeMoreDRSwich();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchDR);
			return "7";
		}
		
		dtji = new JudgeMoreDKSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchDK);
			return "8";
		}
		
		dtji = new JudgeMoreJDBSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchJDB);
			return "9";
		}
		
		dtji = new JudgeMoreZYBSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchZYB);
			return "10";
		}
		
		dtji = new JudgeMoreDJSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchDJ);
			return "11";
		}
		
		dtji = new JudgeMoreXHXQSwitch();
		returnValue=dtji.doJudge(pd);
		if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchXHXQ);
			return "12";
		}
		
		//其他开发判定
        pd.setDeviceRunType(CBSystemConstants.RunTypeSwitchQT);
        return "6";
	}
}

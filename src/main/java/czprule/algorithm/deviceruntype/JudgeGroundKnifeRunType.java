package czprule.algorithm.deviceruntype;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleUtil;
import czprule.system.CBSystemConstants;

public class JudgeGroundKnifeRunType implements DeviceTypeJudgeInf {
	/**
	 * 返回值-1 不是接地刀闸  0已存在安装类型 1 中性点地刀 2普通接地刀
	 * */
	public String doJudge(PowerDevice pd) {
		if (pd==null) {
			return "-1";
		}	
		//若设备类型不为空，即以前判断过，则不判断
		//需屏蔽这段代码，任何时候都需要重新判断
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchFlowGroundLine)) {
//			return "-1";
//		}	
	
		//假如已存在安装类型，则不继续判断
//		if (!pd.getDeviceRunType().trim().equals("")) {
//			return "0";
//		}
		if(pd.getPowerDeviceName().contains("中性点")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeGroundZXDDD);
			return "1"; 
		}
		boolean rs=RuleUtil.isZXDDD(pd);
		if(rs){
			pd.setDeviceRunType(CBSystemConstants.RunTypeGroundZXDDD);
			return "1"; 
		}else{
			pd.setDeviceRunType(CBSystemConstants.RunTypeGroundKnife);
			return "2";
		}
	}
}

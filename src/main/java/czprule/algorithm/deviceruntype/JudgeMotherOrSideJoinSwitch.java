package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CommonSearch;

public class JudgeMotherOrSideJoinSwitch  implements DeviceTypeJudgeInf{

	/**
     * 用 途: 母联开关、旁路开关、母联兼旁路开关判定算法
     * 算法描述：开关两端直接或通过刀闸连接母线,且开关两头连接的母线不能为同一根母线为母联开关，
     *          如果开关连接旁路母线为旁路开关，如果一段既连接了旁路母线又连接了其他母线为旁路兼母联开关
     * @param： 输入参数 开关设备（oprSrcDevice）
     * @return：输出参数 0代表为旁路兼母联开关，1代表旁路开关 ，2代表母联开关，-1代表无意义结果
     */
	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}
		
		CommonSearch cs = new CommonSearch();
		Map<String,Object> inPara = new HashMap<String, Object>();
		Map<String,Object> outPara = new HashMap<String,Object>();
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.PowerTransformer);
		cs.execute(inPara, outPara);
		inPara.clear();
		List allDevs = (List) outPara.get("linkedDeviceList");
		if(allDevs.size()>0){
			return "-1";
		}
		
		JudgeSideMotherLine JSMotherLine=new JudgeSideMotherLine();
        // -------搜索开关一端相连设备-----
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);
		inPara.put("excDevType", SystemConstants.Switch+","+SystemConstants.Term);
		inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		List<PowerDevice> OneMontherLines = (List) outPara.get("linkedDeviceList");
		if(OneMontherLines.size()==0&&!pd.getPowerDeviceName().contains("母联")){
			return "-1";
		}else if(OneMontherLines.size()==0&&pd.getPowerDeviceName().contains("母联")){
			return "2";
		}
		else if(OneMontherLines.size() == 1 && JSMotherLine.doJudge(OneMontherLines.get(0)).equals("0")) {
			List<PowerDevice> path = RuleExeUtil.getPathByDevice(pd, OneMontherLines.get(0), SystemConstants.PowerTransformer, "", true, true);
			if(path.size() >= 4)
				return "-1";
		}
		inPara.put("validPort", 2);
		cs.execute(inPara, outPara);
		List<PowerDevice> TwoMontherLines = (List) outPara.get("linkedDeviceList");
		if(TwoMontherLines.size()==0){
			return "-1";
		}
		else if(TwoMontherLines.size() == 1 && JSMotherLine.doJudge(TwoMontherLines.get(0)).equals("0")) {
			List<PowerDevice> path = RuleExeUtil.getPathByDevice(pd, TwoMontherLines.get(0), SystemConstants.PowerTransformer, "", true, true);
			if(path.size() >= 4)
				return "-1";
		}
		
		PowerDevice tempDev=null; //搜索设备对象
		
		int mlCount = 0;
		int pmCount = 0;
		for (int i = 0; i < OneMontherLines.size(); i++) {
			tempDev=(PowerDevice)OneMontherLines.get(i);
			if(JSMotherLine.doJudge(tempDev).equals("0")) {
				pmCount++;
				//进一步搜索母线（上海金山热电厂有这种接线）
				List<PowerDevice> linkMLList = RuleExeUtil.getDeviceList(tempDev, pd, SystemConstants.MotherLine, SystemConstants.Switch+""+SystemConstants.PowerTransformer, "", "", false, true, true, true, true);
				if(linkMLList.size() > 0)
					mlCount++;
			}
			else
				mlCount++;
		}
		if(pmCount > 0) { //本侧有旁路母线
			 if(mlCount > 0)
				   return "0"; //本侧有普通母线
			 else
				 return "1"; //本侧无普通母线
		}
		
		mlCount = 0;
		pmCount = 0;
		for (int i = 0; i < TwoMontherLines.size(); i++) {
			tempDev=(PowerDevice)TwoMontherLines.get(i);
			if(JSMotherLine.doJudge(tempDev).equals("0")) {
				pmCount++;
				//进一步搜索母线
				List<PowerDevice> linkMLList = RuleExeUtil.getDeviceList(tempDev, pd, SystemConstants.MotherLine, SystemConstants.Switch+""+SystemConstants.PowerTransformer, "", "", false, true, true, true, true);
				if(linkMLList.size() > 0)
					mlCount++;
			}
			else
				mlCount++;
		}
		if(pmCount > 0) {
			 if(mlCount > 0)
				   return "0";
			 else
				 return "1";
		}
		return "2";
	}
}

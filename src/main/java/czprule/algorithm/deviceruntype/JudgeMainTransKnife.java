package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-29
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class JudgeMainTransKnife implements DeviceTypeJudgeInf {
	/**
	 * 用          途: 判定某个刀闸是否为主变刀闸
	 * 算法描述：刀闸一端直接连主变、另一端通过刀闸连开关，一般在3/2接线方式中存在此情况。
	 * @param：PowerDevice设备对象
	 * @return：String 0代表是，-1代表不是
	 */
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		// TODO Auto-generated method stub
		
		if (pd==null) {
			return "-1";
		}	
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
//			return "-1";
//		}	
//		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeZB))  { 
//			 return "0";
//		}
		 
	    Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.PowerTransformer);// 目标设备为主变
		inPara.put("isSearchDirectDevice", "true");// 直接连接
		inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		inPara.clear();
		List allLineOne = (ArrayList) outPara.get("linkedDeviceList");
		if(allLineOne.size()>0){
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
			inPara.put("excDevType", SystemConstants.PowerTransformer);// 排除主变
			inPara.put("validPort", 2);
			cs.execute(inPara, outPara);
			inPara.clear();
			HashMap<PowerDevice, ArrayList<PowerDevice>> switchPaths = (HashMap) outPara.get("pathList");// 最短通路
			if (switchPaths.size()==0) {
				 return "-1";
			}else{
				for (Iterator iterator = switchPaths.values().iterator(); iterator
						.hasNext();) {
					List<PowerDevice> path = (ArrayList) iterator.next();
					if(path.size()==2){
						return "-1";
					}
				}		
			}
		}else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.PowerTransformer);// 目标设备为主变
			inPara.put("isSearchDirectDevice", "true");// 直接连接
			inPara.put("validPort", 2);
			cs.execute(inPara, outPara);
			inPara.clear();
			List allLineTwo = (ArrayList) outPara.get("linkedDeviceList");
			if(allLineTwo.size()==0){
				return "-1";
			}
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
			inPara.put("excDevType", SystemConstants.PowerTransformer);// 排除主变
			inPara.put("validPort", 1);
			cs.execute(inPara, outPara);
			inPara.clear();
			HashMap<PowerDevice, ArrayList<PowerDevice>> switchPaths = (HashMap) outPara.get("pathList");// 最短通路
			if (switchPaths.size()==0) {
				 return "-1";
			}else{
				for (Iterator iterator = switchPaths.values().iterator(); iterator
						.hasNext();) {
					List<PowerDevice> path = (ArrayList) iterator.next();
					if(path.size()==2){
						return "-1";
					}
				}		
			}
		}
		return "0";
	}
}

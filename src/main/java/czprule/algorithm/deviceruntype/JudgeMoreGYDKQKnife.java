package czprule.algorithm.deviceruntype;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 
 * 作    者: 郑柯
 * 开发日期: 2014年11月12日 下午1:54:47 
 */
public class JudgeMoreGYDKQKnife implements DeviceTypeJudgeInf{
	/**
	 * 用途：高压电抗器刀闸判断算法
	 * 
	 */
	public String doJudge(PowerDevice pd) {
		if (pd==null) {
			return "-1";
		}	
		CommonSearch cs = new CommonSearch();
		Map<String,Object> inPara = new HashMap<String, Object>();
		Map<String,Object> outPara = new HashMap<String,Object>();
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.ElecShock);
		cs.execute(inPara, outPara);
		List allDevs = (List) outPara.get("linkedDeviceList");
		if(allDevs.size()==0){
			return "-1";
		}else{
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.InOutLine);
			cs.execute(inPara, outPara);
			allDevs = (List) outPara.get("linkedDeviceList");
			if(allDevs.size()==0)
				return "-1";
			else
				return "0";
		}
	}

}

package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-29
 **/

import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class JudgeKnifeType implements DeviceTypeJudgeInf{
	/**
	 * 用 途:判断刀闸所属类型
	 * @参数1(Map)：输入参数 设备（PowerDevice）
	 * @返回值：输出参数开关所属类型： -1 代表没有结果或不做判断
	 * 0 代表母线侧刀闸、 1代表电源侧刀闸 、2 代表旁路刀闸  、3 代表线路刀闸(3/2)、4 代表主变刀闸(3/2)、5 代表母联刀闸、
	 * 6代表线路刀闸（代替线路开关）、7 代表主变刀闸（代替主变开关）、8代表其他刀闸，
	 */
	public String doJudge(PowerDevice pd){
		
		if(pd==null)
			return "-1";
		//若设备类型不为空，即以前判断过，则不判断
		//需屏蔽这段代码，任何时候都需要重新判断
//		if(!pd.getDeviceRunType().trim().equals(""))   
//			return "-1";
		
//		if(!pd.getDeviceRunType().trim().equals(CBSystemConstants.RunTypeKnifeQT))   
//			return "-1";
		
//		int aa=1;
//		if(pd.getPowerDeviceName().indexOf("9813")>=0||pd.getPowerDeviceName().indexOf("3836")>=0){
//			int asd =1;
//		}
		
		String returnValue="";
		DevicePropertyDB idrt=new DevicePropertyDB();//状态插入类
		DeviceTypeJudgeInf dtji=null;
		
		//母联刀闸判定
	    dtji=new JudgeMotherJoinKnife();
	     returnValue=dtji.doJudge(pd);
	     if(returnValue.equals("0")){
				pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeML);
				return "5";
		 }
	   
	    //PT刀闸判定
	    dtji=new JudgeMorePTKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifePT);
			return "9";
	    }
		    
	   //旁路刀闸判断
	    dtji=new JudgeSideKnife();	
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifePL);
			return "2";
	    }
		    
		//母线侧刀闸判定
		dtji=new JudgeMotherKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeMX);
			return "0";
	    } 
	    
	    //线路侧刀闸判断
	    dtji=new JudgePowerSideKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeDY);
			return "1";
	    } 	
	    
	   //主变侧刀闸判断（20190704也设置为母线对侧）
	    dtji=new JudgePowerSideZBKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeDY);
			return "1";
	    } 	
	    
	    
	    //线路刀闸判定
	    dtji=new JudgeLineKnife();
		returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeXL);
			return "3";
	    }

	    //主变刀闸判定
	    dtji=new JudgeMainTransKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeZB);
			return "4";
	    }

	     
	     
		//线路刀闸（代替线路开关）判定
	    dtji=new JudgeLineKnifeForSwitch();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeXLS);
			return "6";
	    } 
	    
	    //主变刀闸（代替主变开关）判定
	    dtji=new JudgeMainTransKnifeForSwitch();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeZBS);
			return "7";
	    }
	    

	    
	    //避雷器刀闸判定
	    dtji=new JudgeMoreBLQKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeBLQ);
			return "10";
	    }
	    
	    //高压电抗器刀闸判定
	    dtji=new JudgeMoreGYDKQKnife();
	    returnValue=dtji.doJudge(pd);
	    if(returnValue.equals("0")){
			pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeGYDKQ);
			return "10";
	    }
	   
	    if(pd.getPowerDeviceName().contains("PT")){
	    	  pd.setDeviceRunType(CBSystemConstants.RunTypeKnifePT);
	  	    return "9";
	    }
	    
	    List<PowerDevice> swList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.Switch);
	    if(swList.size()>0){
	    	pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeDY);
			return "1";
	    }
	    //其他刀闸判定
	    pd.setDeviceRunType(CBSystemConstants.RunTypeKnifeQT);
	    return "8";
	}
}

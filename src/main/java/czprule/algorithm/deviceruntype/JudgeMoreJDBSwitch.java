package czprule.algorithm.deviceruntype;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class Judge<PERSON>oreJDBSwitch implements DeviceTypeJudgeInf{
	/**
	 * 用途：接地变开关判断算法
	 * 
	 */
	public String doJudge(PowerDevice pd) {
		if (pd==null) {
			return "-1";
		}	
		CommonSearch cs = new CommonSearch();
		Map<String,Object> inPara = new HashMap<String, Object>();
		Map<String,Object> outPara = new HashMap<String,Object>();
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.EarthingTransformer);
		cs.execute(inPara, outPara);
		List allDevs = (List) outPara.get("linkedDeviceList");
		if(allDevs.size()==0){
			return "-1";
		}else{
			return "0";
		}
	}

}

package czprule.algorithm.deviceruntype;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 刀闸类型判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-30
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CommonSearch;

public class JudgeLineKnifeForSwitch implements DeviceTypeJudgeInf{
	/**
	 * 用途：判定某个刀闸是否为线路刀闸 （代替线路开关）
	 * 判定描述：刀闸一端直接连线路、一端直接连接母线或者主变,不能连接开关。
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @返回值：输出参数 0代表是，-1代表不是
	 *
	 */
	@SuppressWarnings("unchecked")
	public String doJudge(PowerDevice pd) {
		// TODO Auto-generated method stub
		
		if (pd==null) {
			return "-1";
		}	
//		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
//			return "-1";
//		}	
//		if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeKnifeXLS))  { 
//			 return "0";
//		}
		
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();	
		 
		//判断刀闸是否连接开关
	     inPara.put("oprSrcDevice", pd);
	     inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
	     cs.execute(inPara, outPara);
	     List switchs=(ArrayList) outPara.get("linkedDeviceList");
	     if(switchs.size()>0)
	    	 return "-1";
	     
	     
		// 判定相关设备
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine+","+SystemConstants.MotherLine+","+SystemConstants.PowerTransformer);
		inPara.put("isSearchDirectDevice", true);
		inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		List allDeviceOne = (ArrayList) outPara.get("linkedDeviceList");
		inPara.put("validPort", 2);
		cs.execute(inPara, outPara);
		inPara.clear();
		List allDeviceTwo = (ArrayList) outPara.get("linkedDeviceList");
		if(allDeviceOne.size()==0||allDeviceTwo.size()==0){
			return "-1";
		}
		PowerDevice pDeviceOne=(PowerDevice)allDeviceOne.get(0);  //刀闸一端直接连接的线路或者母线只有一个
		PowerDevice pDeviceTwo=(PowerDevice)allDeviceTwo.get(0);
		
		if (pDeviceOne.getDeviceType().equals(SystemConstants.InOutLine)
				&&(pDeviceTwo.getDeviceType().equals(SystemConstants.MotherLine)||pDeviceTwo.getDeviceType().equals(SystemConstants.PowerTransformer))){
			return "0";
		}
		if (pDeviceTwo.getDeviceType().equals(SystemConstants.InOutLine)
				&&(pDeviceOne.getDeviceType().equals(SystemConstants.MotherLine)||pDeviceOne.getDeviceType().equals(SystemConstants.PowerTransformer))){
			return "0";
		}
		return "-1";
		}

	}


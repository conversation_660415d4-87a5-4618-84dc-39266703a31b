package czprule.algorithm.baserule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.app.service.OPEService;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.ElecIsland;
import czprule.system.CBSystemConstants;
import czprule.system.CreatePowerStationToplogy;
import czprule.system.DBManager;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 电气岛算法
 * 作    者: 郑柯
 * 开发日期: 2013年9月26日 下午7:15:33 
 */
public class ElecIslandAlgorithm  {

	private static HashMap<String,ArrayList<String>> lineStationMap = new HashMap<String,ArrayList<String>>();
	private static HashMap<String,ArrayList<String>> stationLineMap = new HashMap<String,ArrayList<String>>();
	private static ArrayList<ElecIsland> elecIslandList = new ArrayList<ElecIsland>();
	
	public static void main(String[] args)
	{
		
		CreatePowerStationToplogy.buildStation();
		CreatePowerStationToplogy.buildLine();
		splitElecIsland();
		
		for (ElecIsland elecIsland: elecIslandList) {
			for (String station: elecIsland.getStationList()) {
				System.out.print(CBSystemConstants.getMapPowerStation().get(station).getPowerDeviceName()+", ");
			}
		}
	}
	
	public static void splitElecIsland() {
		elecIslandList.clear();
		ArrayList<String> lineList = new ArrayList<String>();
		//获取厂站,线路,连接关系
//		List list = DBManager.queryForList("select t.line_id,t.station_id from "+CBSystemConstants.opcardUser+"t_e_substationtopology t");
		//edit 2014.6.26
		List list = DBManager.queryForList(OPEService.getService().ElecIslandAlgorithmSql());
		for(Iterator iter = list.iterator();iter.hasNext();) {
			Map map = (Map) iter.next();
			String LINE_ID = String.valueOf(map.get("LINE_ID"));
			String STATION_ID = String.valueOf(map.get("STATION_ID"));
			if(lineStationMap.get(LINE_ID) == null)
				lineStationMap.put(LINE_ID, new ArrayList());
			lineStationMap.get(LINE_ID).add(STATION_ID);
			if(stationLineMap.get(STATION_ID) == null)
				stationLineMap.put(STATION_ID, new ArrayList());
			stationLineMap.get(STATION_ID).add(LINE_ID);
		}

		for (String stationID : CBSystemConstants.getMapPowerStation()
				.keySet()) {
			boolean isExist = false;
			for (ElecIsland elecIsland: elecIslandList) {
				if(elecIsland.getStationList().contains(stationID)) {
					isExist = true;
					break;
				}
			}
			if(isExist)
				continue;
			ArrayList<String> stationList = new ArrayList<String>();
			stationList.add(stationID);
			ElecIsland elecIsland = new ElecIsland(java.util.UUID.randomUUID().toString(), stationList);
			elecIslandList.add(elecIsland);
			split(stationList,lineList,stationID);
		}
		for (ElecIsland elecIsland: elecIslandList) {
			for (String station: elecIsland.getStationList()) {
				if(CBSystemConstants.getMapPowerStation().get(station).getDeviceType().equals(SystemConstants.PowerFactory)) {
					elecIsland.setIslandType("1");
					break;
				}
			}
		}
	}
	
	private static void split(List stationList, List lineList,String stationID) {
		
		ArrayList<String> liList = stationLineMap.get(stationID);
		if(liList == null)
			return;
		for (String lineID : liList) {
			if(lineList.equals(lineID))
				continue;
			lineList.add(lineID);
			
			if(!CBSystemConstants.getPowerLine(lineID).getDeviceStatus().equals("0")) //线路不在运行
				continue;
			ArrayList<String> sidList = lineStationMap.get(lineID);
			for (String sid : sidList) {
				if(sid.equals(stationID))
					continue;
				else if(stationList.contains(sid))
					continue;
				else {
					stationList.add(sid);
					split(stationList, lineList,sid);
				}
			}
		}
	}
	/**
	 * 判断厂站是否属于同一电气岛
	 */
	public static boolean isInSameElecIsland(ArrayList<String> stationIDList) {
		if(elecIslandList.size() == 0)
			splitElecIsland();
		for (ElecIsland elecIsland: elecIslandList) {
			if(elecIsland.getStationList().containsAll(stationIDList))
				return true;
		}
		return false;
	}
	
	/**
	 * 获取与主网分列的变电站
	 */
	public static ArrayList<String> getSplitStation(ArrayList<String> stationIDList) {
		if(elecIslandList.size() == 0)
			splitElecIsland();
		ArrayList<String> splitList = new ArrayList<String>();
		for (String stationID: stationIDList) {
			for (ElecIsland elecIsland: elecIslandList) {
				if(elecIsland.getStationList().contains(stationID)) {
					if(elecIsland.getIslandType().equals("0"))
						splitList.add(stationID);
					break;
				}
			}
		}
		return splitList;
	}
	
}

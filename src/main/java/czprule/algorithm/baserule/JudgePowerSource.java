package czprule.algorithm.baserule;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;

/** 
 * 版权声明: 泰豪软件股份有限公司版权所有
 * 功能说明: 从给定设备集合判断哪些是电源侧设备
 * 作    者: 郑柯
 * 开发日期: 2013年10月21日 下午8:31:14 
 */
public class JudgePowerSource {

	public List<PowerDevice> execute(List<PowerDevice> list) {
		
		List<PowerDevice> results = new ArrayList<PowerDevice>();
		double maxVol = 0;
		for(PowerDevice pd : list) {
			if(pd.getPowerVoltGrade() > maxVol)
				maxVol = pd.getPowerVoltGrade();
		}
		for(Iterator it=list.iterator();it.hasNext();) {
			PowerDevice pd = (PowerDevice)it.next();
			if(pd.getPowerVoltGrade() < maxVol)
				it.remove();
		}
		for(PowerDevice pd : list) {
			if(pd.getDeviceType().equals(SystemConstants.InOutLine))
				results.add(pd);
		}
		if(results.size() != 0)
			return results;
		for(PowerDevice pd : list) {
			if(pd.getDeviceType().equals(SystemConstants.MotherLine))
				results.add(pd);
		}
		if(results.size() != 0)
			return results;
		for(PowerDevice pd : list) {
			if(pd.getDeviceType().equals(SystemConstants.PowerTransformer))
				results.add(pd);
		}
		if(results.size() != 0)
			return results;
		return results;
	}
}

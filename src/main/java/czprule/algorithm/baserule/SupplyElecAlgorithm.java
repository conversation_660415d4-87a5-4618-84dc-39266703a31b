package czprule.algorithm.baserule;

/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项 目 组 ：西北电力图形化智能操作票系统
 * 功能说明 : 送电范围判断
 * 作    者 : 张余平
 * 开发日期 : 2010-10-04
 * 修改日期 ：
 * 修改说明 ：
 * 修 改 人 ：
 **/

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JFrame;
import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CommonSearch;

/**
 * 功能说明 : 送电范围计算算法 功能描述 : 以一个刀闸为起始目标，目标设备为接地线和接地刀闸， 中途遇刀闸、主变、线路停止，通路中所有设备为送电范围
 * 
 * @参数1pd：输入参数 设备（powerDevice）
 * @返回: 设备集合results
 * <AUTHOR>
 * 
 */
public class SupplyElecAlgorithm {

	public List<PowerDevice> execute(PowerDevice pd) {
		// TODO Auto-generated method stub

		List<PowerDevice> results = new ArrayList<PowerDevice>();

		// 如果不是刀闸，直接返回
		if (!pd.getDeviceType().equals(SystemConstants.SwitchSeparate)) {
			JFrame mf = SystemConstants.getMainFrame();
			String title = SystemConstants.SYSTEM_TITLE;
			JOptionPane.showMessageDialog(SystemConstants.getMainFrame(),
					"刀闸送电范围搜索算法输入设备对象[" + pd.getPowerDeviceName() + "]非刀闸！",
					SystemConstants.SYSTEM_TITLE,
					javax.swing.JOptionPane.INFORMATION_MESSAGE);
			return results;
		}
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		CommonSearch cs = new CommonSearch();
		// 目标设备为接地刀闸和接地线，排除普通刀闸、主线路、主变
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.SwitchFlowGroundLine);// 目标设备为接地刀闸和接地线
		inPara.put("excDevType", SystemConstants.SwitchSeparate + ","
				+ SystemConstants.InOutLine + ","
				+ SystemConstants.PowerTransformer);
		cs.execute(inPara, outPara);
		List devs = (ArrayList) outPara.get("linkedDeviceList");
		
		//搜索线路对侧厂站的地刀
		List<PowerDevice> lineList = RuleExeUtil.getDeviceDirectList(pd, SystemConstants.InOutLine);
		if(lineList.size() > 0) {
			PowerDevice line = lineList.get(0);
			List<PowerDevice> lineOtherList = RuleExeUtil.getLineOtherSideList(line);
			for(PowerDevice dev : lineOtherList) {
				List<PowerDevice> gdList = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchFlowGroundLine);
				if(gdList.size() > 0) {
					devs.addAll(gdList);
				}
			}
		}
		
		if (devs.size() > 0)
			results = devs;
		return results;

		// 下面代码保留，方便查询送电范围内所有设备

		/*
		 * HashMap<PowerDevice, ArrayList<PowerDevice>> hashMap = (HashMap)
		 * outPara.get("pathList");// 此集合中包含所有通路 for (int i = 0; i <
		 * allLine.size(); i++) { PowerDevice pDevice = new PowerDevice();
		 * pDevice = (PowerDevice) allLine.get(i);
		 * ArrayList<PowerDevice>list = hashMap.get(allLine.get(i)); for(int j =
		 * 1; j < list.size(); j++) { PowerDevice pDeviceTo = new PowerDevice();
		 * pDeviceTo =list.get(j);
		 * //System.out.print(pDeviceTo.getPowerDeviceName()+"-");
		 * allDevice.add(pDeviceTo); }
		 * 
		 * }
		 */
	}
}

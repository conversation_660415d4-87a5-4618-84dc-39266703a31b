package czprule.algorithm.baserule;

/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 设备与变电站电压等级判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;

public class JudgeVolt implements DeviceTypeJudgeInf {
	/**
	 * 用 途:判定设备电压等级与所属变电站电压等级的比较关系
	 * 
	 * @参数1(Map)：输入参数 设备（pd）
	 * @返回值：输出参数 returnValue =0 表示小于变电站电压等级 ，1表示设备电压等于变电站，-1表示没有意义。
	 */
	public String doJudge(PowerDevice pd) {

		if (pd == null) {
			return "-1";
		}

		String powerStationID = pd.getPowerStationID();
		PowerDevice powerStation = CBSystemConstants
				.getPowerStation(powerStationID);
		if (powerStation == null)
			return "-1";

		if (pd.getPowerVoltGrade() < powerStation.getPowerVoltGrade()) {
			return "0";
		} else {
			return "1";
		}
	}
}

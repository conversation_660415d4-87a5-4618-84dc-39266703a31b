package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeSwitchRunModeDouble implements DeviceTypeJudgeInf{
	/**
	 * 用 途:开关双母母接线方式判定 
	 * 判定描述：且它的一端与两条母线相连
	 * @参数1(Map)：输入参数 设备（pd）
	 * @return : 0代表是，1代表不是  -1代表没有意义
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public String doJudge(PowerDevice pd) {
		
			if (pd==null) {
				return "-1";
			}			
			
			Map inPara = new HashMap();
			Map outPara = new HashMap();
			CommonSearch cs = new CommonSearch();
		    List<PowerDevice> mlLists = new ArrayList<PowerDevice>();
		
			inPara.put("oprSrcDevice", pd);
			inPara.put("tagDevType", SystemConstants.MotherLine);// 目标设备为母线
			inPara.put("excDevType", SystemConstants.PowerTransformer+","+SystemConstants.Switch);// 排除设备
			inPara.put("validPort", 1);
			cs.execute(inPara, outPara);
			List<PowerDevice> allMotherOne = (ArrayList) outPara.get("linkedDeviceList");
			for(PowerDevice mlTemp : allMotherOne){
				if(!mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					mlLists.add(mlTemp);
			}
			if (mlLists.size()==2) {
				return "0";
			}
			inPara.put("validPort", 2);
			cs.execute(inPara, outPara);
			inPara.clear();
			List<PowerDevice> allMotherTwo = (ArrayList) outPara.get("linkedDeviceList");
			for(PowerDevice mlTemp : allMotherTwo){
				if(!mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
					mlLists.add(mlTemp);
			}
			if (allMotherTwo.size()==2) {
				return "0";
			}
			return "1";
	}

}

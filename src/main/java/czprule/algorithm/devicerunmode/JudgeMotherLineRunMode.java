package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 母线接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-01-04
 **/
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class JudgeMotherLineRunMode  implements DeviceTypeJudgeInf{

	/**
	 * 用          途：母线接线方式判定      
	 * 算法描述：搜索母线接线的线路开关，线路开关的接线方式就是母线的接线方式，广州珠海部分变电站主变开关连接两条母线，而两条母线是单母接线方式
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @reutrn：1代表单母接线方式，22代表双母接线方式，3代表3/接线方式，-1表示结果没有意义
	 */

	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}	
		
		List<PowerDevice> devList = RuleExeUtil.getDeviceList(pd, SystemConstants.InOutLine+","+SystemConstants.PowerTransformer, SystemConstants.PowerTransformer, true, true, true);
		for(Iterator it = devList.iterator();it.hasNext();) {
			PowerDevice devTemp = (PowerDevice)it.next();
			if(devTemp.getPowerVoltGrade() != pd.getPowerVoltGrade())
				it.remove();
		}
		for(PowerDevice devTemp : devList){
			List<PowerDevice> switchList = RuleExeUtil.getDeviceList(devTemp, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
			for(Iterator it = switchList.iterator();it.hasNext();) {
				PowerDevice swTemp = (PowerDevice)it.next();
				if(swTemp.getPowerVoltGrade() != pd.getPowerVoltGrade()){
					it.remove();
				}else if(swTemp.getPowerDeviceName().contains("电抗")){
					it.remove();
				}
			}
			if(switchList.size() == 2) {
				
				PowerDevice switch1 = switchList.get(0);
				PowerDevice switch2 = switchList.get(1);
				if(switch1.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)
						&&switch1.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchML)){
					
				}else{
					List<PowerDevice> mlList1 = RuleExeUtil.getDeviceList(switch1, switch2, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", "", false, true, true, true);
					List<PowerDevice> mlList2 = RuleExeUtil.getDeviceList(switch2, switch1, SystemConstants.MotherLine, SystemConstants.PowerTransformer, "", "", false, true, true, true);
					if(mlList1.size() > 0 && mlList2.size() > 0) {
						if(mlList1.contains(pd) || mlList2.contains(pd)){
							pd.setDeviceRunModel(CBSystemConstants.RunModelThreeTwo);
							return pd.getDeviceRunModel();
						}
					}
				}
				
			}
		}
		
		List<PowerDevice> switchList = RuleExeUtil.getDeviceList(pd, SystemConstants.Switch, SystemConstants.PowerTransformer, true, true, true);
		for(PowerDevice switchTemp : switchList){
			//不判断主变开关（武汉存在一个主变连接两根单母母线,不排除其他开关是为了防止开关类型识别错误时无法识别双母接线）
			if(switchTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)
					||switchTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchFHC)){
				continue;
			}
			for(int i = 1; i <= 2; i++) {
				List<PowerDevice> switchLinkedKnifeListPort = RuleExeUtil.getDeviceDirectByPortList(switchTemp, SystemConstants.SwitchSeparate, String.valueOf(i));
				if(switchLinkedKnifeListPort.size() == 2){
					//如果母线连接的线路、其他开关中存在一个开关一侧连接了两个刀闸，并且两个刀闸中一个连接的是当前母线，另外一个连接的是另一条母线，则为双母接线
					PowerDevice knife1 = switchLinkedKnifeListPort.get(0);
					PowerDevice knife2 = switchLinkedKnifeListPort.get(1);
					List<PowerDevice> knife1LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife1, SystemConstants.MotherLine);
					List<PowerDevice> knife2LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife2, SystemConstants.MotherLine);
					if(knife1LinkedMotherLineList.size() > 0 && knife2LinkedMotherLineList.size() > 0) {
						if(knife1LinkedMotherLineList.contains(pd) || knife2LinkedMotherLineList.contains(pd)){
							pd.setDeviceRunModel(CBSystemConstants.RunModelDoubleMotherLine);
							return pd.getDeviceRunModel();
						}
					}
				}else if(CBSystemConstants.pjzMap.containsKey(pd.getPowerStationID())){
					List<PowerDevice> switchLinkedKnifeListPortZero = RuleExeUtil.getDeviceDirectByPortList(switchTemp, SystemConstants.SwitchSeparate, "0");
					if(switchLinkedKnifeListPortZero.size() == 3){
						List<PowerDevice> switchLinkedKnifeListPortOne = RuleExeUtil.getDeviceDirectByPortList(switchTemp, SystemConstants.SwitchSeparate, "1");
						List<PowerDevice> switchLinkedKnifeListPortTwo = RuleExeUtil.getDeviceDirectByPortList(switchTemp, SystemConstants.SwitchSeparate, "2");
						
						if(switchLinkedKnifeListPortOne.size()==0&&switchLinkedKnifeListPortTwo.size()>0){
							for(Iterator<PowerDevice> itor = switchLinkedKnifeListPortZero.iterator();itor.hasNext();){
								PowerDevice dev = itor.next();
								for(PowerDevice dev2:switchLinkedKnifeListPortTwo){
									if(dev2.getPowerDeviceID().equals(dev.getPowerDeviceID())){
										itor.remove();
									}
								}
							}
							
							if(switchLinkedKnifeListPortZero.size() == 2){
								//如果母线连接的线路、其他开关中存在一个开关一侧连接了两个刀闸，并且两个刀闸中一个连接的是当前母线，另外一个连接的是另一条母线，则为双母接线
								PowerDevice knife1 = switchLinkedKnifeListPortZero.get(0);
								PowerDevice knife2 = switchLinkedKnifeListPortZero.get(1);
								List<PowerDevice> knife1LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife1, SystemConstants.MotherLine);
								List<PowerDevice> knife2LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife2, SystemConstants.MotherLine);
								if(knife1LinkedMotherLineList.size() > 0 && knife2LinkedMotherLineList.size() > 0) {
									if(knife1LinkedMotherLineList.contains(pd) || knife2LinkedMotherLineList.contains(pd)){
										pd.setDeviceRunModel(CBSystemConstants.RunModelDoubleMotherLine);
										return pd.getDeviceRunModel();
									}
								}
							}
						}else if(switchLinkedKnifeListPortTwo.size()==0&&switchLinkedKnifeListPortOne.size()>0){
							for(Iterator<PowerDevice> itor = switchLinkedKnifeListPortZero.iterator();itor.hasNext();){
								PowerDevice dev = itor.next();
								for(PowerDevice dev2:switchLinkedKnifeListPortOne){
									if(dev2.getPowerDeviceID().equals(dev.getPowerDeviceID())){
										itor.remove();
									}
								}
							}
							
							if(switchLinkedKnifeListPortZero.size() == 2){
								//如果母线连接的线路、其他开关中存在一个开关一侧连接了两个刀闸，并且两个刀闸中一个连接的是当前母线，另外一个连接的是另一条母线，则为双母接线
								PowerDevice knife1 = switchLinkedKnifeListPortZero.get(0);
								PowerDevice knife2 = switchLinkedKnifeListPortZero.get(1);
								List<PowerDevice> knife1LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife1, SystemConstants.MotherLine);
								List<PowerDevice> knife2LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife2, SystemConstants.MotherLine);
								if(knife1LinkedMotherLineList.size() > 0 && knife2LinkedMotherLineList.size() > 0) {
									if(knife1LinkedMotherLineList.contains(pd) || knife2LinkedMotherLineList.contains(pd)){
										pd.setDeviceRunModel(CBSystemConstants.RunModelDoubleMotherLine);
										return pd.getDeviceRunModel();
									}
								}
							}
						}else if(switchLinkedKnifeListPortTwo.size()==0&&switchLinkedKnifeListPortOne.size()==0){
							List<PowerDevice> tagList = new ArrayList<PowerDevice>();
							for(Iterator<PowerDevice> itor = switchLinkedKnifeListPortZero.iterator();itor.hasNext();){
								PowerDevice dev = itor.next();
								List<PowerDevice> list = RuleExeUtil.getDeviceDirectList(dev, SystemConstants.SwitchSeparate);
								
								if(list.size()>0){
									switchLinkedKnifeListPortZero.contains(list.get(0));
									tagList.add(dev);
									tagList.add(list.get(0));
									break;
								}
							}
							
							if(tagList.size() == 2){
								//如果母线连接的线路、其他开关中存在一个开关一侧连接了两个刀闸，并且两个刀闸中一个连接的是当前母线，另外一个连接的是另一条母线，则为双母接线
								PowerDevice knife1 = tagList.get(0);
								PowerDevice knife2 = tagList.get(1);
								List<PowerDevice> knife1LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife1, SystemConstants.MotherLine);
								List<PowerDevice> knife2LinkedMotherLineList = RuleExeUtil.getDeviceDirectList(knife2, SystemConstants.MotherLine);
								if(knife1LinkedMotherLineList.size() > 0 && knife2LinkedMotherLineList.size() > 0) {
									if(knife1LinkedMotherLineList.contains(pd) || knife2LinkedMotherLineList.contains(pd)){
										pd.setDeviceRunModel(CBSystemConstants.RunModelDoubleMotherLine);
										return pd.getDeviceRunModel();
									}
								}
							}
						}
					}
				}
			}
		}
		pd.setDeviceRunModel(CBSystemConstants.RunModelOneMotherLine);
		return pd.getDeviceRunModel();
	}
}

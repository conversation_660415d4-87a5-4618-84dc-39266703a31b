package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-01-04
 **/

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeSwitchRunModeThreeTwo implements DeviceTypeJudgeInf{
	/**
	 * 用 途:开关3/2接线方式判定 
	 * 判定描述：第一种情况 一端连接母线（可以有刀闸），一端通过另一个开关连接母线（可以有刀闸）
	 * 		   第二种情况 两端都通过开关连接母线（可以有刀闸）
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @参数2(Map): 输出参数 0代表是，1代表不是
	 * @返回值：无
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public String doJudge(PowerDevice pd) {
		
		
		if (pd==null) {
			return "-1";
		}	
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		int switchNum = 0;
		PowerDevice motherLine=null;
		CommonSearch cs = new CommonSearch();
		List<PowerDevice> mlLists = new ArrayList<PowerDevice>();
		
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);// 目标设备为母线
		inPara.put("excDevType", SystemConstants.PowerTransformer);// 排除主变
		inPara.put("validPort", 1);
		cs.execute(inPara, outPara);
		List<PowerDevice> oneMotherLines = (ArrayList) outPara.get("linkedDeviceList");
		HashMap<PowerDevice, ArrayList<PowerDevice>> oneMotherLinePath = (HashMap) outPara.get("pathList");
		for(PowerDevice mlTemp : oneMotherLines){
			if(!mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
				mlLists.add(mlTemp);
		}
		if(mlLists.size()==0||mlLists.size()>1)
			return "1";
		motherLine=(PowerDevice)mlLists.get(0);
		ArrayList<PowerDevice> onepath = oneMotherLinePath.get(motherLine);
		PowerDevice tempDev=null;
		for (int i = 1; i < onepath.size(); i++) {
			tempDev = onepath.get(i);
			if (tempDev.getDeviceType().equals(SystemConstants.Switch)) {
				switchNum++;
			}
		}
		inPara.put("validPort", 2);
		cs.execute(inPara, outPara);
		inPara.clear();
		mlLists.clear();
		List<PowerDevice> twoMotherLines = (ArrayList) outPara.get("linkedDeviceList");
		HashMap<PowerDevice, ArrayList<PowerDevice>> twoMotherLinePath = (HashMap) outPara.get("pathList");
		for(PowerDevice mlTemp : twoMotherLines){
			if(!mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
				mlLists.add(mlTemp);
		}
		
		if(mlLists.size()==0||mlLists.size()>1)
			return "1";
		if(mlLists.contains(motherLine)) //区别三角接线方式中开关两端连接的母线如果是同一条
			return "1";
		ArrayList<PowerDevice> twoPath = twoMotherLinePath.get(mlLists.get(0));//开关一端连接母线
		for (int i = 1; i < twoPath.size(); i++) {
			tempDev = twoPath.get(i);
			if (tempDev.getDeviceType().equals(SystemConstants.Switch)) {
				switchNum++;
			}
		}
		if(switchNum == 2)
		    return "0";
		else
			return "1";
	}
	
}

package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-01-04
 **/
import java.util.List;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;

public class JudgeSwitchRunMode implements DeviceTypeJudgeInf{
	/**
	 * 用 途:开关接线方式判定 判定描述：接线方式有单母，双母，3/2  其它
	 * @param：输入参数 设备（pd）
	 * @return: 1代表单母接线方式，2代表双母接线方式，3代表3/2接线方式，4代表其他方式
	 */
	public String doJudge(PowerDevice pd) {
			
		if (pd==null) {
			return "-1";
		}	
		//若设备类型不为空，即以前判断过，则不判断
	   //需屏蔽这段代码，任何时候都需要重新判断
//		if (!pd.getDeviceType().equals(SystemConstants.Switch)) {
//			return "-1";
//		}	
	
        DevicePropertyDB idrt = new DevicePropertyDB();// 状态插入类
        String returnValue = "";
        DeviceTypeJudgeInf dtji = null;
//		
//		//3/2接线方式
//		dtji=new JudgeSwitchRunModeThreeTwo();
//		returnValue=dtji.doJudge(pd);
//		if(returnValue.equals("0")){
//			pd.setDeviceRunModel(CBSystemConstants.RunModelThreeTwo);
//			idrt.deviceModelInsert(pd);
//			return "3";
//		}
//		
//		//判断单母接线方式
//		dtji=new JudgeSwitchRunModeOne();  
//		returnValue=dtji.doJudge(pd);
//		if(returnValue.equals("0")){
//			pd.setDeviceRunModel(CBSystemConstants.RunModelOneMotherLine);
//			idrt.deviceModelInsert(pd);
//			return "1";
//		}
//		

        if (pd.getDeviceRunType().equals(CBSystemConstants.RunTypeSwitchDYC)) {
            // 双母接线方式
            dtji = new JudgeSwitchRunModeDouble();
            returnValue = dtji.doJudge(pd);
            if (returnValue.equals("0")) {
                pd.setDeviceRunModel(CBSystemConstants.RunModelDoubleMotherLine);
                idrt.deviceModelInsert(pd);
                return "2";
            }
        }
		
		//搜素与开关直接连接的母线(排除旁路母线)
		List<PowerDevice> otherMLList = RuleExeUtil.getDeviceList(pd, SystemConstants.MotherLine, SystemConstants.PowerTransformer+","+CBSystemConstants.RunTypeSideMother, true, false, true);
		if(otherMLList.size() == 0)
			return "4";
		else {
			String result = null;
			for(PowerDevice mlTemp : otherMLList){
				if(!mlTemp.getDeviceRunModel().trim().equals("") && !mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother)){
					//3/4或者3/2直接返回
					if(CBSystemConstants.RunModelThreeTwo.equals(mlTemp.getDeviceRunModel())  || CBSystemConstants.RunModelFourThree.equals(mlTemp.getDeviceRunModel())) {
						pd.setDeviceRunModel(mlTemp.getDeviceRunModel());
						return pd.getDeviceRunModel();
					}
					//单母和双母暂时保存
					if(result == null) {
						result = mlTemp.getDeviceRunModel();
					}
				}
			}
			pd.setDeviceRunModel(result);
			return result;
		}
	}
}

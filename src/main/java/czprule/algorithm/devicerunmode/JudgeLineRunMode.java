package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 线路接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-01-04
 **/

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.algorithm.dao.DevicePropertyDB;
import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeLineRunMode  implements DeviceTypeJudgeInf{

	/**
	 * 用          途：线路接线方式判定      
	 * 算法描述：搜索线路接线的开关，开关的接线方式就是线路的接线方式
	 * @参数1(Map)：输入参数 设备（powerDevice）
	 * @reutrn：1代表单母接线方式，2代表双母接线方式，3代表3/2接线方式，-1表示结果没有意义
	 */

	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}
		
		String runModel = "";
		if(RuleExeUtil.judgeLineType(pd).equals("1"))
			runModel = CBSystemConstants.RunModelCableLine;
		else if(RuleExeUtil.isDoubleSource(pd))
			runModel = CBSystemConstants.RunModelDoubleLine;
		else 
			runModel = CBSystemConstants.RunModelOneLine;
		pd.setDeviceRunModel(runModel);
		return runModel;
		
//		String runModel = "";
//		if(RuleExeUtil.judgeLineType(pd).equals("1"))
//			runModel = CBSystemConstants.RunModelCableLine;
//		else if(RuleExeUtil.isDoubleSource(pd))
//			runModel = CBSystemConstants.RunModelDoubleLine;
//		else 
//			runModel = CBSystemConstants.RunModelOneLine;
//		
//		pd.setDeviceRunModel(runModel);
//		DevicePropertyDB insert = new DevicePropertyDB();
//		insert.deviceModelInsert(pd);
//		return runModel;
		
		
		//若设备类型不为空，即以前判断过，则不判断
		//需屏蔽这段代码，任何时候都需要重新判断
//		if (!pd.getDeviceType().equals(SystemConstants.InOutLine)) {
//			return "-1";
//		}	
	
		// 假如已存在接线方式，则不继续判断
//		if (!pd.getDeviceRunModel().trim().equals("")) {
//			return pd.getDeviceRunModel();
//		}

		// 搜索开关
//		Map inPara = new HashMap();
//		Map outPara = new HashMap();
//		CommonSearch cs = new CommonSearch();
//		inPara.put("oprSrcDevice", pd);
//		inPara.put("tagDevType", SystemConstants.Switch);// 目标设备为开关
//		inPara.put("excDevType", SystemConstants.PowerTransformer);// 排除主变
//		cs.execute(inPara, outPara);
//		List switchs = (ArrayList) outPara.get("linkedDeviceList");
//		if(switchs.size()==0)  
//			return "1";     //目前线路没有连接开关一般只从在单母接线中，用刀闸代替了开关
//		
//		PowerDevice tempDev=null;
//		for (int i = 0; i < switchs.size(); i++) {
//			tempDev=(PowerDevice)switchs.get(i);
//			if(!tempDev.getDeviceRunModel().trim().equals("")){
//				pd.setDeviceRunModel(tempDev.getDeviceRunModel());
//				DevicePropertyDB insert = new DevicePropertyDB();
//				insert.deviceModelInsert(pd);
//				return pd.getDeviceRunModel();
//			}
//	    }
//	    return "-1";
	    
	}
}

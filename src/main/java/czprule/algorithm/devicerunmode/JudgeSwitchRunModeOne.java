package czprule.algorithm.devicerunmode;
/**
 * 版权声明 : 泰豪软件股份有限公司版权所有
 * 项  目   组 ：图形化智能操作票系统
 * 功能说明 : 开关接线方式判断
 * 作          者 : zhangyp
 * 开发日期 : 2011-12-31
 **/
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.algorithm.DeviceTypeJudgeInf;
import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.CommonSearch;

public class JudgeSwitchRunModeOne implements DeviceTypeJudgeInf{
	
	/**
	 * 用 途:开关单母接线方式判定 
	 * 判定描述：判断开关两端连接的母线数量，排除主变与开关，如果两端母线数量最多为一则是单母接线方式,注：该方法容易将3/2接线方式也判断为单母，应该先判断开关是否为3/2接线方式
	 * @参数1(Map)：输入参数 设备（pd）
	 * @return : 0代表是，1代表不是  -1代表没有意义
	 */
	@SuppressWarnings({ "unchecked", "rawtypes" })
	public String doJudge(PowerDevice pd) {
		
		if (pd==null) {
			return "-1";
		}	
		
		Map inPara = new HashMap();
		Map outPara = new HashMap();
		CommonSearch cs = new CommonSearch();
		
		//搜索相关设备
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.MotherLine);// 目标设备为母线
		inPara.put("excDevType", SystemConstants.PowerTransformer+","+SystemConstants.Switch);// 排除设备
		cs.execute(inPara, outPara);
		List<PowerDevice> allMotherOne = (ArrayList) outPara.get("linkedDeviceList");
		List<PowerDevice> mlNeedDeleteList = new ArrayList<PowerDevice>();
		for(PowerDevice mlTemp : allMotherOne){
			if(mlTemp.getDeviceRunType().equals(CBSystemConstants.RunTypeSideMother))
				mlNeedDeleteList.add(mlTemp);
		}
		allMotherOne.removeAll(mlNeedDeleteList);
		//判定相关设备及设备连接方式 是否为单母连接方式
		if (allMotherOne.size() == 1) {
			return "0";
		}else{
			return "1";
		}
	}
}

package czprulepw.wordcard.replaceclass.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.system.CBSystemConstants;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;
/**
 * 配网线路名称  术语引用    
 * @since 2015-01-09 14:51
 * <AUTHOR>
 *
 */
public class ReplaceStrPWXLMC implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		String deviceId = curDev.getPowerDeviceID();
		String sql = "select t.line_id from "+CBSystemConstants.equipUser+"t_pd_equipinfo t where t.equip_id = '" +deviceId+"'";
		List list = DBManager.query(sql);
		String line_id = "";
		Map temp = new HashMap();
		for (int i = 0; i < list.size(); i++) {
				temp = (Map) list.get(i);
				line_id = StringUtils.ObjToString(temp.get("line_id"));
				if(!line_id.equals("") && line_id != null){
					break; 
				}
		}
		String line_name = "";
		if(!line_id.equals("") && line_id !=null){
			String lineSql = "select name from "+CBSystemConstants.equipUser+"t_c_aclineend t where t.id = '"+line_id+"'";
			List linelist = DBManager.query(lineSql);
			Map lineTemp = new HashMap();
			for (int i = 0; i < linelist.size(); i++) {
					lineTemp = (Map) linelist.get(i);
					line_name = StringUtils.ObjToString(lineTemp.get("name"));
					if(!line_name.equals("") && line_name != null){
						desc = line_name;
						break;
					}
			}
		}
		return desc;
		
	}
}

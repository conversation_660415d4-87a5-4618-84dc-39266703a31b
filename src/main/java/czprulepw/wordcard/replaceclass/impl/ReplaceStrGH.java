package czprulepw.wordcard.replaceclass.impl;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.utils.StringUtils;

import czprule.model.PowerDevice;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.system.DBManager;
import czprule.wordcard.replaceclass.TempStringReplace;
/**
 * 杆号名称  术语引用
 * @since 2015-01-12 
 * <AUTHOR>
 *
 */
public class ReplaceStrGH implements TempStringReplace{
	@Override
	public String strReplace(String tempStr, PowerDevice curDev,
			PowerDevice stationDev,String desc) {
		List<PowerDevice> list = RuleExeUtil.getDeviceList(curDev, "GanTa", "", "", "", true, false, false, true);
		if(list.size() !=1){
			return "";
		}else{
			return list.get(0).getPowerDeviceName();
		}
	}
}

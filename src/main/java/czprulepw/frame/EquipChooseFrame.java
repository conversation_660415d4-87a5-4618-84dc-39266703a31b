package czprulepw.frame;

import java.awt.BorderLayout;
import java.awt.Button;
import java.awt.GridLayout;
import java.util.List;

import javax.swing.JCheckBox;
import javax.swing.JFrame;
import javax.swing.JLabel;
import javax.swing.JPanel;
import javax.swing.border.EmptyBorder;

import czprule.model.PowerDevice;

public class EquipChooseFrame extends JFrame {

    /**
	 *  
	 */
	private static final long serialVersionUID = 1L;
	private JPanel contentPane;
    private JPanel panel;
    private List<PowerDevice> equips= null ; //待选择设备集合

    /**
     * Create the frame
     */
    public EquipChooseFrame(List<PowerDevice> equips) {
        setTitle("选择设备集合");
        setDefaultCloseOperation(JFrame.EXIT_ON_CLOSE);
        setBounds(100, 100, 409, 331);
        contentPane = new JPanel();
        contentPane.setBorder(new EmptyBorder(5, 5, 5, 5));
        contentPane.setLayout(new BorderLayout(0, 0));
        setContentPane(contentPane);
        this.equips=equips;
        JLabel label = new JLabel(
                "请选择设备：");
        contentPane.add(label, BorderLayout.NORTH);
        contentPane.add(getPanel(), BorderLayout.CENTER);
    }

    private JPanel getPanel() {
        if (panel == null) {
            panel = new JPanel();// 创建面板对象
            panel.setLayout(new GridLayout(0, 4));// 设置网格布局管理器
            JCheckBox[] boxs = new JCheckBox[equips.size()];// 创建控件数组
            for (int i = 0; i < equips.size(); i++) {// 遍历控件数组
                boxs[i] = new JCheckBox(equips.get(i).getPowerDeviceName());// 初始化数组中的复选框组件
                panel.add(boxs[i]);// 把数组元素（即每个复选框）添加到面板中
            }
        }
        return panel;
    }
}

package czprulepw;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CommonSearch;
/** 
 * 设备合上拓扑着色 
 * <AUTHOR>
 *
 */
public class DevicePDLoadElec implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
        List<PowerDevice> loadElecDevices = new ArrayList<PowerDevice>();
		
		//获取设备两端的有效通路，目标设备是线路。 
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine);
		inPara.put("isSearchOffPath", false);
		inPara.put("validPort", "1");
		cs.execute(inPara, outPara);
		List<PowerDevice> onePortDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		inPara.put("validPort", "2");
		cs.execute(inPara, outPara);
		List<PowerDevice> twoPortDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		if(onePortDevice.size() > 0 && twoPortDevice.size() > 0){
			pd.setIsLoseElec("0"); 
			return true;
		}
		if(onePortDevice.size() == 0 && twoPortDevice.size() == 0){
			pd.setIsLoseElec("1"); 
			return true;
		}
		if(onePortDevice.size() == 0 && twoPortDevice.size() > 0){
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("isSearchOffPath", false);
			inPara.put("validPort", "1");
			cs.execute(inPara, outPara);
			HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoseDevices = (HashMap)outPara.get("pathList");
			List<PowerDevice> onePath = null;
			PowerDevice loadPd = null;
			for (Iterator<ArrayList<PowerDevice>> iterator = oneLoseDevices.values().iterator(); iterator.hasNext();) {
				onePath =  iterator.next();
				for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
					 loadPd = (PowerDevice) iterator2.next();
					 if("0".equals(loadPd.getDeviceStatus())&&!loadElecDevices.contains(loadPd)){
						 loadElecDevices.add(loadPd);
					 }
				}
			}
		}else{
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("isSearchOffPath", false);
			inPara.put("validPort", "2");
			cs.execute(inPara, outPara);
			HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoseDevices = (HashMap)outPara.get("pathList");
			List<PowerDevice> onePath = null;
			PowerDevice loadPd = null;
			for (Iterator<ArrayList<PowerDevice>> iterator = oneLoseDevices.values().iterator(); iterator.hasNext();) {
				onePath =  iterator.next();
				for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
					 loadPd = (PowerDevice) iterator2.next();
					 if("0".equals(loadPd.getDeviceStatus())&&!loadElecDevices.contains(loadPd)){
						 loadElecDevices.add(loadPd);
					 }
				}
			}
		}
		
		
		PowerDevice tmpPd = null;
		SvgAction action = null;
		for (int i = 0; i < loadElecDevices.size(); i++) {
			tmpPd = loadElecDevices.get(i);
			tmpPd.setIsLoseElec("0");
			String powerVolt = String.valueOf((int) pd.getPowerVoltGrade());
			if(SystemConstants.Switch.equals(tmpPd.getDeviceType())){
				action = new ChangeColorAction(tmpPd, SystemConstants
						.getMapColor().get(powerVolt), SystemConstants
						.getMapColor().get(powerVolt));
			}else{
				action = new ChangeColorAction(tmpPd, SystemConstants
						.getMapColor().get(powerVolt), null);
			}
			
			action.execute();
			
		}
		
		//插入数据库
//		DeviceStatusManager dsm = new DeviceStatusManager();
//		for (Iterator<PowerDevice> iterator = loadElecDevices.iterator(); iterator.hasNext();) {
//			PowerDevice powerDevice =  iterator.next();
//			dsm.insertDevLoseElecStatus(powerDevice);
//		}
		
		return true;
	} 

}

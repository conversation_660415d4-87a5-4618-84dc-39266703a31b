package czprulepw;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.czp.operationcard.dao.DeviceStatusManager;
import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CommonSearch;
/** 
 * 设备断开拓扑着色   
 * <AUTHOR>
 *
 */
public class DevicePDLoseElec implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		if("1".equals(pd.getIsLoseElec())){
			return true;
		}
		List<PowerDevice> loseElecDevices = new ArrayList<PowerDevice>();
		loseElecDevices.add(pd);
		
		//获取设备两端的有效通路，目标设备是线路。
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevType", SystemConstants.InOutLine);
		inPara.put("isSearchOffPath", false);
		inPara.put("validPort", "1");
		cs.execute(inPara, outPara);
		List<PowerDevice> onePortDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		inPara.put("validPort", "2");
		cs.execute(inPara, outPara);
		List<PowerDevice> twoPortDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		if(onePortDevice.size()>0 && twoPortDevice.size()>0){
			pd.setIsLoseElec("1"); //当前设备失电，不影响其他设备
			return true;
		}
		if(onePortDevice.size() == 0){
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("isSearchOffPath", false);
			inPara.put("validPort", "1");
			cs.execute(inPara, outPara);
			HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoseDevices = (HashMap)outPara.get("pathList");
			List<PowerDevice> onePath = null;
			PowerDevice losePd = null;
			for (Iterator<ArrayList<PowerDevice>> iterator = oneLoseDevices.values().iterator(); iterator.hasNext();) {
				onePath =  iterator.next();
				for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
				     losePd = (PowerDevice) iterator2.next();
				     if("0".equals(losePd.getDeviceStatus())&&!loseElecDevices.contains(losePd)){
				    	 loseElecDevices.add(losePd);
					 }
				}
			}
		}
		if(twoPortDevice.size() == 0){
			inPara.clear();
			inPara.put("oprSrcDevice", pd);
			inPara.put("isSearchOffPath", false);
			inPara.put("validPort", "2");
			cs.execute(inPara, outPara);
			HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoseDevices = (HashMap)outPara.get("pathList");
			List<PowerDevice> onePath = null;
			PowerDevice losePd = null;
			for (Iterator<ArrayList<PowerDevice>> iterator = oneLoseDevices.values().iterator(); iterator.hasNext();) {
				onePath =  iterator.next();
				for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
				     losePd = (PowerDevice) iterator2.next();
				     if("0".equals(losePd.getDeviceStatus())&&!loseElecDevices.contains(losePd)){
				    	 loseElecDevices.add(losePd);
					 }
				}
			}
		}
		
		PowerDevice tmpPd = null;
		SvgAction action = null;
		for (int i = 0; i < loseElecDevices.size(); i++) {
			tmpPd = loseElecDevices.get(i);
			tmpPd.setIsLoseElec("1");
			//失电着色
			if(SystemConstants.Switch.equals(tmpPd.getDeviceType())){
				action = new ChangeColorAction(tmpPd,
						SystemConstants.getMapColor().get(
								SystemConstants.LOSE_COLOR_CODE), SystemConstants.getMapColor().get(
										SystemConstants.LOSE_COLOR_CODE));
			}else{
				action = new ChangeColorAction(tmpPd,
						SystemConstants.getMapColor().get(
								SystemConstants.LOSE_COLOR_CODE), null);
			}
			action.execute();
		}
		
		//插入数据库
//		DeviceStatusManager dsm = new DeviceStatusManager();
//		for (Iterator<PowerDevice> iterator = loseElecDevices.iterator(); iterator.hasNext();) {
//			PowerDevice powerDevice =  iterator.next();
//			dsm.insertDevLoseElecStatus(powerDevice);
//		}
		
		return true;
	}

}

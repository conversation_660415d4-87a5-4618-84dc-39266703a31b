package czprulepw.rule;
/**
 * 设备结合环操作    
 */
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.swing.JOptionPane;

import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CommonSearch;
import czprulepw.PWSystemConstants;

public class DeviceOnOFFNet implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		//获取设备两端的有效通路，目标设备是线路。
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempPd = null;
		inPara.put("oprSrcDevice", pd);
		inPara.put("tagDevRunType", PWSystemConstants.PWRunTypeSwitchZX);
		inPara.put("isSearchOffPath", false);
		inPara.put("validPort", "1");
		cs.execute(inPara, outPara);
		List<PowerDevice> oneEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		inPara.put("validPort", "2");
		cs.execute(inPara, outPara);
		List<PowerDevice> twoEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");


		//搜索算法中的有效通路的最后一个设备不能保证在运行状态，加上后面两个过滤条件。
		List<PowerDevice> checkoneEndDevice = new ArrayList<PowerDevice>();
		for (int i = 0; i < oneEndDevice.size(); i++) {
			tempPd = oneEndDevice.get(i);
			if(tempPd.getDeviceStatus().equals("0")){
				checkoneEndDevice.add(tempPd);
			}
		}
		List<PowerDevice> checktwoEndDevice = new ArrayList<PowerDevice>();
		for (int i = 0; i < twoEndDevice.size(); i++) {
			tempPd = twoEndDevice.get(i);
			if(tempPd.getDeviceStatus().equals("0")){
				    checktwoEndDevice.add(tempPd);
				}
		}
		
		if(checkoneEndDevice.size()>0 && checktwoEndDevice.size()>0){
			String showStr="";
			if("0".equals(pd.getDeviceStatus())){
				showStr = "当前操作将导致解环，是否继续？";
			}else{
				showStr = "当前操作将导致合环，是否继续？";
			}
			int cfm = JOptionPane.showConfirmDialog(SystemConstants.getMainFrame(), showStr,"提示",JOptionPane.YES_NO_OPTION);
			if (cfm == JOptionPane.CANCEL_OPTION) {
				 return false;
			}
		}
		
		return true;
	}

}

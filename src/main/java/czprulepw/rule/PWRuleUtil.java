package czprulepw.rule;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Iterator;
import java.util.List;
import java.util.Map;

import com.tellhow.graphicframework.action.SvgAction;
import com.tellhow.graphicframework.action.impl.ChangeColorAction;
import com.tellhow.graphicframework.action.impl.ChangeDeviceFlashingAction;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.system.CommonSearch;
import czprulepw.PWSystemConstants;
/**
 * 
 * <AUTHOR>
 *
 */
public class PWRuleUtil {
	
	
	/**
	 * @作用  寻找电源点算法
	 * @param srcDev 源操作设备
	 * @param isFlash 电源点是否闪烁  
	 * @return 电源的结果集合
	 */
	public List<PowerDevice> powerEquipSearch(PowerDevice srcDev,boolean isFlash){
		
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempPd = null;
		inPara.put("oprSrcDevice", srcDev);
		inPara.put("tagDevRunType", PWSystemConstants.PWRunTypeSwitchZX);
		inPara.put("isSearchOffPath", false);
		cs.execute(inPara, outPara);
		List<PowerDevice> powerDevs = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");


		//搜索算法中的有效通路的最后一个设备不能保证在运行状态，加上后面两个过滤条件。
		List<PowerDevice> runpowerDevs = new ArrayList<PowerDevice>();
		for (int i = 0; i < powerDevs.size(); i++) {
			tempPd = powerDevs.get(i);
			if(tempPd.getDeviceStatus().equals("0")){
				runpowerDevs.add(tempPd);
			}
		}
		
		if(isFlash){
			SvgAction  action = null;
			PowerDevice tempDev = null;
			for (int i = 0; i < runpowerDevs.size(); i++) {
				tempDev = runpowerDevs.get(i);
				action = new ChangeDeviceFlashingAction(tempDev,"10");
				action.execute();
			}
		}
		return runpowerDevs;
	}


	/**
	 * @作用 设备失电范围搜索
	 * @param srcDev 源操作设备
	 * @param isShowLoseDev 失电设备是否显示
	 * @return 失电设备集合
	 */
    public List<PowerDevice> loseElecDevices(PowerDevice srcDev,boolean isShowLoseDev){
    	
    	ArrayList<PowerDevice> loseElecDevices = new ArrayList<PowerDevice>(); //失电设备集合
    	if("1".equals(srcDev.getIsLoseElec())){
			return loseElecDevices;
		}
		
		loseElecDevices.add(srcDev);
		
		CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempPd = null;
    
		//获取设备两端的有效通路。
		inPara.put("oprSrcDevice", srcDev);
		inPara.put("isSearchOffPath", false);
		inPara.put("validPort", "1");
		cs.execute(inPara, outPara);
		List<PowerDevice> oneEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoseDevices = (HashMap)outPara.get("pathList");
		inPara.put("validPort", "2");
		outPara.clear(); 
		cs.execute(inPara, outPara);
		List<PowerDevice> twoEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		HashMap<PowerDevice,ArrayList<PowerDevice>> twoLoseDevices = (HashMap)outPara.get("pathList");
		
		//搜索算法中的有效通路的最后一个设备不能保证在运行状态，加上后面两个过滤条件。
		List<PowerDevice> checkoneEndDevice = new ArrayList<PowerDevice>();
		for (int i = 0; i < oneEndDevice.size(); i++) {
			tempPd = oneEndDevice.get(i);
			if(tempPd.getDeviceStatus().equals("0")
				  &&tempPd.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
				checkoneEndDevice.add(tempPd);
			}
		}
		List<PowerDevice> checktwoEndDevice = new ArrayList<PowerDevice>();
		for (int i = 0; i < twoEndDevice.size(); i++) {
			tempPd = twoEndDevice.get(i);
			if(tempPd.getDeviceStatus().equals("0")
					  && tempPd.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
				    checktwoEndDevice.add(tempPd);
				}
		}
		
		
		Map<PowerDevice,ArrayList<PowerDevice>> loseDevices = new HashMap<PowerDevice,ArrayList<PowerDevice>>();
		//设备两端有电，只是当前设备失电，不影响其他设备
		if(checkoneEndDevice.size()>0 && checktwoEndDevice.size()>0){
			loseElecDevices.add(srcDev);
			loseDevices.put(srcDev, loseElecDevices);
		}else if(checkoneEndDevice.size() == 0 && checktwoEndDevice.size() == 0){
			if(srcDev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
				loseDevices.putAll(oneLoseDevices);
				loseDevices.putAll(twoLoseDevices);
			}
		}else if(checkoneEndDevice.size()>0){
			loseDevices = twoLoseDevices;
		}else if(checktwoEndDevice.size()>0){
			loseDevices = oneLoseDevices;
		}
		List<PowerDevice> onePath = null;
		PowerDevice losePd = null;
		for (Iterator<ArrayList<PowerDevice>> iterator = loseDevices.values().iterator(); iterator.hasNext();) {
			onePath =  iterator.next();
			for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
			     losePd = (PowerDevice) iterator2.next();
			     if("0".equals(losePd.getDeviceStatus())&&!loseElecDevices.contains(losePd)){
			    	 loseElecDevices.add(losePd);
				 }
			}
		}
    	
		if(isShowLoseDev){
			//显示所有失电设备
			SvgAction action = null;
			for (int i = 0; i < loseElecDevices.size(); i++) {
				tempPd = loseElecDevices.get(i);
				tempPd.setIsLoseElec("1");
				if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
				    action = new ChangeColorAction(tempPd,
						SystemConstants.getMapColor().get(
								SystemConstants.LOSE_COLOR_CODE), SystemConstants.getMapColor().get(
										("01".indexOf(tempPd.getDeviceStatus())>=0)?SystemConstants.LOSE_COLOR_CODE:null));
    			}else{
    				action = new ChangeColorAction(tempPd,
    						SystemConstants.getMapColor().get(
    								SystemConstants.LOSE_COLOR_CODE), null);
    			}
				action.execute();
			}
		}
		
    	return loseElecDevices;
    }
    
	/**
	 * @作用 设备送电范围搜索
	 * @param srcDev 源操作设备
	 * @param isShowLoseDev 送电设备是否显示
	 * @return 失电设备集合
	 */
    public List<PowerDevice> loadElecDevices(PowerDevice srcDev,boolean isShowLoadDev){
    	
    	List<PowerDevice> loadElecDevices = new ArrayList<PowerDevice>();
        CommonSearch cs = new CommonSearch();
		Map<String, Object> inPara = new HashMap<String, Object>();
		Map<String, Object> outPara = new HashMap<String, Object>();
		PowerDevice tempPd = null;
		
        //如果开关本身是主线开关，可以直接供电
        if(srcDev.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
        	
	        	inPara.put("oprSrcDevice", srcDev);
	    		inPara.put("isSearchOffPath", false);
	    		cs.execute(inPara, outPara);
	    		List<PowerDevice> allDevices = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
	    		loadElecDevices.add(srcDev);
	        	for (int i = 0; i < allDevices.size(); i++) {
					tempPd = allDevices.get(i);
					if("0".equals(tempPd.getDeviceStatus())&&!loadElecDevices.contains(tempPd)){
						 loadElecDevices.add(tempPd);
					}
				}
        }else{
        
				//获取设备两端的有效通路，目标设备是10kV主线开关。
				
				inPara.put("oprSrcDevice", srcDev);
				inPara.put("isSearchOffPath", false);
				inPara.put("validPort", "1");
				cs.execute(inPara, outPara);
				List<PowerDevice> oneEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
				HashMap<PowerDevice,ArrayList<PowerDevice>> oneLoadDevices = (HashMap)outPara.get("pathList");				
				inPara.put("validPort", "2");
				cs.execute(inPara, outPara);
				List<PowerDevice> twoEndDevice = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
				HashMap<PowerDevice,ArrayList<PowerDevice>> twoLoadDevices = (HashMap)outPara.get("pathList");
				
				//搜索算法中的有效通路的最后一个设备不能保证在运行状态，加上后面两个过滤条件。
				List<PowerDevice> checkoneEndDevice = new ArrayList<PowerDevice>();
				for (int i = 0; i < oneEndDevice.size(); i++) {
					tempPd = oneEndDevice.get(i);
					if(tempPd.getDeviceStatus().equals("0")
						  &&tempPd.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
						checkoneEndDevice.add(tempPd);
					}
				}
				List<PowerDevice> checktwoEndDevice = new ArrayList<PowerDevice>();
				for (int i = 0; i < twoEndDevice.size(); i++) {
					tempPd = twoEndDevice.get(i);
					if(tempPd.getDeviceStatus().equals("0")
							  && tempPd.getDeviceRunType().equals(PWSystemConstants.PWRunTypeSwitchZX)){
						    checktwoEndDevice.add(tempPd);
						}
				}
				
				
				if(checkoneEndDevice.size() > 0 && checktwoEndDevice.size() > 0){
					loadElecDevices.add(srcDev);
				}else if(checkoneEndDevice.size()>0){
					
					List<PowerDevice> onePath = null;
					PowerDevice loadPd = null;
					for (Iterator<ArrayList<PowerDevice>> iterator = twoLoadDevices.values().iterator(); iterator.hasNext();) {
						onePath =  iterator.next();
						for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
							 loadPd = (PowerDevice) iterator2.next();
							 if("0".equals(loadPd.getDeviceStatus())&&!loadElecDevices.contains(loadPd)){
								 loadElecDevices.add(loadPd);
							 }
						}
					}
					loadElecDevices.add(srcDev);
				}else if(checktwoEndDevice.size()>0){
					
					List<PowerDevice> onePath = null;
					PowerDevice loadPd = null;
					for (Iterator<ArrayList<PowerDevice>> iterator = oneLoadDevices.values().iterator(); iterator.hasNext();) {
						onePath =  iterator.next();
						for (Iterator<PowerDevice> iterator2 = onePath.iterator(); iterator2.hasNext();) {
							 loadPd = (PowerDevice) iterator2.next();
							 if("0".equals(loadPd.getDeviceStatus())&&!loadElecDevices.contains(loadPd)){
								 loadElecDevices.add(loadPd);
							 }
						}
					}
					loadElecDevices.add(srcDev);
				}	
        }
    	
        if(isShowLoadDev){
        	//显示所有送设备
			SvgAction action = null;
			for (int i = 0; i < loadElecDevices.size(); i++) {
				tempPd = loadElecDevices.get(i);
				tempPd.setIsLoseElec("0");
				String powerVolt = String.valueOf((int) tempPd.getPowerVoltGrade());
				if(SystemConstants.Switch.equals(tempPd.getDeviceType())){
    				action = new ChangeColorAction(tempPd, SystemConstants
    						.getMapColor().get(powerVolt), SystemConstants
    						.getMapColor().get(powerVolt));
    			}else{
    				action = new ChangeColorAction(tempPd, SystemConstants
    						.getMapColor().get(powerVolt), null);
    			}
				action.execute();
			}	
        }
        
    	return loadElecDevices;
    }


    /**
     * @作用  寻找负荷代供电源点
     * @param srcDev 源操作设备 
     * @param isShowLoseDev 负荷转供设备是否显示
     * @return 负荷转供电源点
     */
    public List<PowerDevice> replacePowerEquip(PowerDevice srcDev,boolean isShowPowerEquip){
    	
    	List<PowerDevice> replacePowerEquips = new ArrayList<PowerDevice>();
    	//寻找该设备的失电范围
    	List<PowerDevice> loseElecEquip = this.loseElecDevices(srcDev, false);
    	loseElecEquip.remove(srcDev);
    	if(loseElecEquip.size() == 0)
    		return replacePowerEquips;
    	
    	
    	//搜索待确定的带供开关
    	CommonSearch cs = new CommonSearch();
 		Map<String, Object> inPara = new HashMap<String, Object>();
 		Map<String, Object> outPara = new HashMap<String, Object>();
    	inPara.put("oprSrcDevice", srcDev);
    	inPara.put("tagDevType", SystemConstants.Switch);
    	inPara.put("isSearchOffPath", false);
		inPara.put("isStopOnTagDevType", false);
		inPara.put("tagDevRunType", PWSystemConstants.PWRunTypeSwitchLL);
		cs.execute(inPara, outPara);
		List<PowerDevice> allDevices = (ArrayList<PowerDevice>)outPara.get("linkedDeviceList");
		PowerDevice tempPd = null;
 		List<PowerDevice> devs = new ArrayList<PowerDevice>();
    	for (int i = 0; i < allDevices.size(); i++) {
    		tempPd = allDevices.get(i);
    		if(("1".equals(tempPd.getDeviceStatus())||"2".equals(tempPd.getDeviceStatus()))
    				&&!loseElecEquip.contains(tempPd)){
    			devs.add(tempPd);
    		}
		}
    	
    	List<PowerDevice> loadElecEquip = null;
    	for (int i = 0; i < devs.size(); i++) {
    		tempPd = devs.get(i);
    		loadElecEquip = this.loadElecDevices(tempPd, false);
//    		loadElecEquip.remove(tempPd);
//    		if(loadElecEquip.containsAll(loseElecEquip)){
//    			replacePowerEquips.add(tempPd);
//    		}
    		if(loadElecEquip.size() > 0){
    			replacePowerEquips.add(tempPd);
    		}
		}
    	
    	if(isShowPowerEquip){
			SvgAction  action = null;
			for (int i = 0; i < replacePowerEquips.size(); i++) {
				tempPd = replacePowerEquips.get(i);
				action = new ChangeDeviceFlashingAction(tempPd,"10");
				action.execute();
			}
		}
    	
    	return replacePowerEquips;
    }


}

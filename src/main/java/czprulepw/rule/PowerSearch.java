package czprulepw.rule;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
/**
 * 作用：寻找电源点
 * <AUTHOR>
 *
 */
public class PowerSearch implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		PWRuleUtil pwr = new PWRuleUtil();
		pwr.powerEquipSearch(pd, true);
		
		
		return true;
	}

}

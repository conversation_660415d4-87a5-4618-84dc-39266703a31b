package czprulepw.rule;

import java.util.List;
import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprulepw.PWSystemConstants;
/** 
 * 设备断开拓扑着色 
 * <AUTHOR>
 *
 */
public class DevicePDLoseElec implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		PWRuleUtil pwr = new PWRuleUtil();
		List<PowerDevice> loseElecDevices = pwr.loseElecDevices(pd, true);
		
		PowerDevice tempPd = null;
        for (int i = 0; i < loseElecDevices.size(); i++) {
			tempPd = loseElecDevices.get(i);
	        DispatchTransDevice dtd=new DispatchTransDevice();
	        dtd.setTransDevice(tempPd);
	        dtd.setParentDevice(CBSystemConstants.getParentDev());
	        dtd.setBeginstatus(tempPd.getIsLoseElec());
	        dtd.setEndstate("1");
	        dtd.setFlag("0");
	        PWSystemConstants.loseelecDeviceMap.put(PWSystemConstants.loseelecDeviceMap.size()+1, dtd);
        }
		
		
//		//插入数据库 
//		DeviceStatusManager dsm = new DeviceStatusManager();
//		for (Iterator<PowerDevice> iterator = loseElecDevices.iterator(); iterator.hasNext();) {
//			PowerDevice powerDevice =  iterator.next();
//			dsm.insertDevLoseElecStatus(powerDevice);
//		}
		
		return true;
	}

}

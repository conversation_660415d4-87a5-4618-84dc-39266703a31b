package czprulepw.rule;

import java.util.ArrayList;
import java.util.List;

import javax.swing.JOptionPane;

import com.tellhow.czp.mainframe.menu.DeviceMenuModel;
import com.tellhow.graphicframework.constants.SystemConstants;

import czprule.model.PowerDevice;
import czprule.rule.RuleExecute;
import czprule.rule.RulebaseInf;
import czprule.rule.model.RuleBaseMode;
import czprule.rule.operationclass.RuleExeUtil;
import czprule.rule.view.EquipCheckChoose;
import czprule.system.CBSystemConstants;
import czprule.system.ShowMessage;
import czprulepw.frame.EquipChooseFrame;

/**
 * 作用：负荷转供点
 * <AUTHOR>
 *
 */
public class DeviceReplacePower implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		PWRuleUtil pwr = new PWRuleUtil();
		List<PowerDevice> replacePowerDev = pwr.replacePowerEquip(pd, false);
		if(replacePowerDev.size() == 0){
			if(CBSystemConstants.isCurrentSys)
				ShowMessage.view("不存在代供开关！");
			return false;	
		}
		
		List<PowerDevice> chooseEquips = null;
		if(CBSystemConstants.isCurrentSys) {
			String showMessage="请选择倒供的联络开关";
			EquipCheckChoose ecc=new EquipCheckChoose(SystemConstants.getMainFrame(), true, replacePowerDev, showMessage, false, false);
			chooseEquips=ecc.getChooseEquip();
			if(ecc.isCancel() || chooseEquips.size() != 1){
				return false;
			}
		}
		else if(CBSystemConstants.mapPara.containsKey("webpara")) {
			String kgid = CBSystemConstants.mapPara.get("webpara");
			for(PowerDevice eq : replacePowerDev) {
				if(eq.getPowerDeviceID().equals(kgid)) {
					chooseEquips = new ArrayList<PowerDevice>();
					chooseEquips.add(eq);
					break;
				}
			}
			CBSystemConstants.mapPara.remove("webpara");
		}
		else {
			chooseEquips = replacePowerDev;
		}
			
//		RuleExecute ruleExecute = new RuleExecute();
//		RuleBaseMode dmm = new RuleBaseMode();
//		dmm.setPd(chooseEquips.get(0));
//		dmm.setBeginStatus("1");
//		dmm.setEndState("0");
//	    ruleExecute.execute(rbm);
		
		PowerDevice newSource = chooseEquips.get(0);
		List<PowerDevice> path = RuleExeUtil.getPathByDevice(pd, newSource, null, null, false, true);
	    
		PowerDevice kg = path.get(1);
		RuleExeUtil.deviceStatusSet(kg, kg.getDeviceStatus(), "1");
		RuleExeUtil.deviceStatusSet(kg, kg.getDeviceStatus(), "2");
		
		RuleExeUtil.deviceStatusSet(newSource, newSource.getDeviceStatus(), "1");
	    RuleExeUtil.deviceStatusSet(newSource, newSource.getDeviceStatus(), "0");
		//ShowMessage.view("负荷代供开关："+replacePowerDev.toString());
		
		
//		EquipChooseFrame ecf = new EquipChooseFrame(replacePowerDev);
//		ecf.setVisible(true);	
		
		return true;
	}
}

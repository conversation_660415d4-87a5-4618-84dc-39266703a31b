package czprulepw.rule;

import java.util.List;

import czprule.model.PowerDevice;
import czprule.rule.RulebaseInf;
import czprule.rule.model.DispatchTransDevice;
import czprule.rule.model.RuleBaseMode;
import czprule.system.CBSystemConstants;
import czprulepw.PWSystemConstants;
/**  
 * 设备合上拓扑着色 
 * <AUTHOR>
 *
 */
public class DevicePDLoadElec implements RulebaseInf {

	@Override
	public boolean execute(RuleBaseMode rbm) {
		// TODO Auto-generated method stub
		
		if(rbm==null)
			return false;
		PowerDevice pd=rbm.getPd();
		if(pd==null)
			return false;
		
		PWRuleUtil pwr = new PWRuleUtil();
        List<PowerDevice> loadElecDevices = pwr.loadElecDevices(pd, false);
        PowerDevice tempPd = null;
        for (int i = 0; i < loadElecDevices.size(); i++) {
			tempPd = loadElecDevices.get(i);
	        DispatchTransDevice dtd=new DispatchTransDevice();
	        dtd.setTransDevice(tempPd);
	        dtd.setParentDevice(CBSystemConstants.getParentDev());
	        dtd.setBeginstatus(tempPd.getIsLoseElec());
	        dtd.setEndstate("0");
	        dtd.setFlag("0");
	        PWSystemConstants.loseelecDeviceMap.put(PWSystemConstants.loseelecDeviceMap.size()+1, dtd);
        }
		
//		//插入数据库 
//		DeviceStatusManager dsm = new DeviceStatusManager();
//		for (Iterator<PowerDevice> iterator = loadElecDevices.iterator(); iterator.hasNext();) {
//			PowerDevice powerDevice =  iterator.next();
//			dsm.insertDevLoseElecStatus(powerDevice);
//		}
		
		return true;
	}

}

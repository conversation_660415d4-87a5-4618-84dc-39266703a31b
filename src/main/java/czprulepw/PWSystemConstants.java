package czprulepw;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.Map;

import czprule.model.PowerDevice;
import czprule.rule.model.DispatchTransDevice;

public class PWSystemConstants {
	
	
	//设备失电范围集合
	public static Map<Integer,DispatchTransDevice> loseelecDeviceMap = new HashMap<Integer, DispatchTransDevice>();
	public static Map<PowerDevice,ArrayList<ArrayList<PowerDevice>>> deviceSourceMap = new HashMap<PowerDevice,ArrayList<ArrayList<PowerDevice>>>(); //供电路径

	
	
	/***************** 配网设备运行类型 开始  ***********/
	public static final String PWRunTypeSwitchZX  = "pwmainlineswitch";   // 主线开关
	public static final String PWRunTypeSwitchFZX = "pwlowerlineswitch";  // 支线开关(分界开关)
	public static final String PWRunTypeSwitchLL = "pwcontactswitch";  //联络开关
	public static final String PWRunTypeSwitchRoomLL = "pwRoomcontactswitch";  //容器（如：开闭所，环网柜）内联络开关
	public static final String PWRunTypeSwitchRoomQT = "pwRoomOtherSwitch";  //容器（如：开闭所，环网柜）内进线/出线开关（这两者的设备命名标准一致，故放在一起）
	public static final String PWRunTypeSwitchFD = "pwsectionswitch";  //分段开关
	public static final String PWRunTypeSwitchBWX = "pwgridswitch";  //并网线开关

	public static final String PWRunTypeSwitchDLS  = "pwdropswitch";   // 跌落式熔断器
	public static final String PWRunTypeSwitchFuse  = "pwfuseswitch";   // 熔断器
	public static final String PWRunTypeSwitchZS  = "pwpoleswitch";   // 柱上开关
	public static final String PWRunTypeSwitchFH  = "pwloadswitch";   // 负荷开关
	public static final String PWRunTypeSwitchKGZ  = "pwinsideswitch";   // 开关站内开关
	
	public static final String PWRunTypeKnifeXL = "pwlowerlineknife";  //支线刀闸
	public static final String PWRunTypeKnifeDYC = "pwbelongknife";   //附属刀闸
	public static final String PWRunTypeACLineSegment = "ACLineSegmentPW";  
	public static final String PWRunTypeJunction = "pwjunction";
	/***************** 配网设备运行类型       结束     *****/

	/***************** 配网设备类型 开始  ***********/
	public static final String PWTypePDBYQ = "DistributionPowerTransform"; //配电变压器
	public static final String PWTypeACLineSegmentPW = "ACLineSegmentPW"; //馈线段
	public static final String PWTypePdRoom = "PdRoom"; // 配电室/开闭所
	public static final String PWTypePole = "Pole"; // 杆塔
	public static final String PWTypeMainUnit = "MainUnit"; // 环网柜
	/***************** 配网设备类型 结束  ***********/

}

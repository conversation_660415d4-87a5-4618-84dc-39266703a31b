# 控制台输出配置（新增）
log4j.appender.stdout=org.apache.log4j.ConsoleAppender
log4j.appender.stdout.Target=System.out
log4j.appender.stdout.layout=org.apache.log4j.PatternLayout
log4j.appender.stdout.layout.ConversionPattern=[%p][%d{yyyy-MM-dd HH:mm:ss}][%c{1}:%L] - %m%n


# 配置文件Appender
log4j.appender.file=org.apache.log4j.DailyRollingFileAppender
log4j.appender.file.File=${catalina.base}/logs/catalina-qd.log
#log4j.appender.file.File=logs/catalina-zj.log
log4j.appender.file.DatePattern='.'yyyy-MM-dd
log4j.appender.file.layout=org.apache.log4j.PatternLayout
log4j.appender.file.layout.ConversionPattern=[%p][%d{yyyy-MM-dd HH\:mm\:ss}][%c{1}:%L] - %m%n

# 设置日志级别和Appender
log4j.rootLogger=INFO, stdout, file

log4j.category.org.springframework = ERROR
log4j.category.org.hibernate=ERROR
log4j.logger.org.beryl.gui=ERROR
log4j.category.com.alibaba.dubbo=ERROR
log4j.category.org.beryl=File
log4j.category.org.beryl.additivity=false
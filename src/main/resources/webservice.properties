JK_STATE=http://192.168.1.161:8080/TBPWeb/webservices/JkCaoZuoPiaoWebService
#jk import oms
JK_UPDATE=http://192.168.1.161:8080/TBPWeb/webservices/JkUpdateWebService
CZP_SERVICE=http://10.157.3.21:8080/powernet-graphic-czpinerfaceoms/services/CzpService
#bj dd jk import oms
JK_SERVICE=http://10.157.3.32:8080/TBPWeb/webservices/JkUpdateWebService
DD_SERVICE=http://10.157.3.32:8080/TBPWeb/webservices/JkUpdateWebService

##http://10.157.3.32:8080/TBPWeb/webservices/JkUpdateWebService?wsdl
# guangxi OMS
#GXCZPWEB=http://10.45.12.113:7001/TBPWeb/webservices/CzpWebService
GXCZPWEB=http://10.45.12.152:8080/TBPWeb/webservices/CzpWebService
#GXCZPWEB=http://127.0.0.1:8080/TBPWeb/webservices/CzpWebService
# guangzhou OMS
GZCZPWEB=http://10.124.20.210:8080/TBPWeb/webservices/ClientProvincialDataInteractionWebService
#GZCZPWEB=http://127.0.0.1:8080/TBPWeb/webservices/ClientProvincialDataInteractionWebService
# beijing OMS
SEC_SERVICE=http://***********:8080/TBPWeb/webservices/SecCzpService
# shanghai DKY yxyc
SH_DKY_YXYC=http://127.0.0.1:8082/demo/hello/
#zhejiang DKY yxyc
ZJ_DKY_YXYC=http://***********:8083/card/meas/brkAndDis?
#changsha web
CSCZPWEB= http://smart-grid.dcloud.hn.dc.sgcc.com.cn/netorder/restful/RestfulController/InsertTicket
#shaoxing web
ZJSXWEB = http://************:9012/card/czp/synCityCzp
#ddh web
DDHWEB = http://127.0.0.1:8083/netorder/equipczp/czpController/imageTicket/refresh
#yunnanbs web
YNBSWEB_IP = *************
YNBSWEB_PORT = 8094
#yunnanqj web
YNQJWEB_IP = **************
YNQJWEB_PORT = 8094
#yunnanhh web
YNHHWEB_IP = ************
YNHHWEB_PORT = 9090
#yunnanyx web
YNYXWEB_IP = ************
YNYXWEB_PORT = 8094
#yunnankm web
YNKMWEB_IP = 
YNKMWEB_PORT = 